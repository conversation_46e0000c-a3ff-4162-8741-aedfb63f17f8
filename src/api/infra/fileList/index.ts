import request from '@/config/axios'

export interface FileVO {
  id: number
  configId: number
  path: string
  name: string
  url: string
  size: string
  type: string
  createTime: Date
}

export interface FilePageReqVO extends PageParam {
  path?: string
  type?: string
  createTime?: Date[]
}

// 查询文件列表
export const getFilePageApi = (params: FilePageReqVO) => {
  if (params.createTime!=null||params.createTime!=undefined){
    params.createTime[0]=params.createTime[0]+" 00:00:00"
    params.createTime[1] =params.createTime[1]+" 23:59:59"
  }
  return request.get({ url: '/infra/file/page', params })
}

// 删除文件
export const deleteFileApi = (id: number) => {
  return request.delete({ url: '/infra/file/delete?id=' + id })
}
