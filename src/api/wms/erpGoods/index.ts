import request from '@/config/axios'

/**
  * Author： 许祎婷
  * Time： 2024/05/20
  * Description：查询erp物料列表
*/
export const getErpGoodsPageApi = async (params) => {
  return await request.get({ url: '/wms/entitygoods/page', params })
}

/**
 * Author： 许祎婷
 * Time： 2024/05/20
 * Description：删除Erp物料
 */
export const deleteErpGoodsApi = async (data) => {
  return await request.delete({ url: '/wms/entitygoods', data })
}

/**
  * Author： 许祎婷
  * Time： 2024/05/20
  * Description：查询erp物料详情
*/
export const getErpGoodsDetailApi = async (id: number) => {
    return await request.get({ url: '/wms/entitygoods/' + (id || 0) })
}

/**
 * Author： 许祎婷
 * Time： 2024/05/20
 * Description：新增erp物料
 */
export const createErpGoodsApi = async (data) => {
  return await request.post({ url: '/wms/entitygoods', data })
}


/**
 * Author： 许祎婷
 * Time： 2024/05/20
 * Description：修改erp物料
 */
export const updateErpGoodsApi = async (data) => {
  return await request.put({ url: '/wms/entitygoods/', data })
}

/**
  * Author： 许祎婷
  * Time： 2024/05/20
  * Description：获取下拉框数据
*/
export const getSelectApi = async (typeCodes: string) => {
  return await request.get({ url: "/common/type/getMapTypeCodes?typeCodes=" + typeCodes })
}


