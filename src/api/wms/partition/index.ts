import request from '@/config/axios'

export interface PartitionVO {
  customsCode: string
  partld?: number
  measureUnit:string
  valueAddedTaxRate:number
  refundTaxRate:number
  customsChineseName:string
  customsEnglishName:string
  declareElementRefer:string
  declareElement:string
  supervisionCondition:string
  createTime:string
}

export interface Partition {
  pageNo: number
  pageSize:number
}

export interface TenantPageReqVO extends PageParam {
  name?: string
  contactName?: string
  contactMobile?: string
  status?: number
  createTime?: Date[]
}

export interface TenantExportReqVO {
  name?: string
  contactName?: string
  contactMobile?: string
  status?: number
  createTime?: Date[]
}

/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:查询单条分区信息
*/
export const getPartitionViewApi = (partId: number) => {

  return request.get({ url: '/wms/warehouse-area/get?id=' + partId })
}

/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:分区信息新增
*/
export const createPartitionViewApi = (data: PartitionVO) => {
  return request.post({ url: '/wms/warehouse-area/create', data })
}
/**
 * Author: 汪凯华
 * Time: 2024/05/22
 * Function:分区信息修改
 */
export const updatePartitionViewApi = (data: PartitionVO) => {
  return request.put({ url: '/wms/warehouse-area/update', data })
}
/**
 * Author: 汪凯华
 * Time: 2024/05/22
 * Function:查询分区列表
 */
export const getPartitionViewPageApi = async (params: Partition) => {
  return await request.get({ url: '/wms/warehouse-area/page', params})
}

/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:查询分区下拉框数据共用接口
*/
export const getSelectApi = async (typeCodes: string) => {
  return await request.get({url: "/common/type/getMapTypeCodes?typeCodes=" + typeCodes})
}

/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:下载仓位模板
*/
export const importPosTemplateApi = () => {
  return request.download({ url: '/wms/pos/exportTemplate' })
}
/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:单条删除分区信息
*/
export const delPartitionApi = async (ids: number[]) => {
  return await request.delete({ url: '/wms/warehouse-area' ,data: ids})
}
/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:批量删除分区信息
*/
export const delPartitionListApi = (ids: number[]) => {
  return request.delete({ url: '/wms/warehouse-area' ,data: ids })
}
