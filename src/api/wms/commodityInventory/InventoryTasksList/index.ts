import request from '@/config/axios'

export interface InventoryTaskVO {
  customsCode: string
  partld?: number
  measureUnit:string
  valueAddedTaxRate:number
  refundTaxRate:number
  customsChineseName:string
  customsEnglishName:string
  declareElementRefer:string
  declareElement:string
  supervisionCondition:string
  createTime:string
}

export interface InventoryTask {
  pageNo: number
  pageSize:number
  posId:any
  ctId:any
}
export interface InventoryGoodsTask {
  inOrder:boolean
  egId:number
  whId:number
  orderCode?:string
  startTime?:string
  endTime?:string
  pageNo: number
  pageSize:number

}
export interface TenantPageReqVO extends PageParam {
  name?: string
  contactName?: string
  contactMobile?: string
  status?: number
  createTime?: Date[]
}

export interface TenantExportReqVO {
  name?: string
  contactName?: string
  contactMobile?: string
  status?: number
  createTime?: Date[]
}


export const getMatchingAPi = async (egId, oiId) => {
  const url = `/wms/pos/matching?egId=${egId}&oiId=${oiId}&_t=${Date.now()}`;
  return await request.get({ url });
};

/**
  * Author: 汪凯华
  * Time: 2024/05/24
  * Function:查询仓库/仓位下拉框数据
*/
//查询下拉框数据
export const getInventoryTaskSelectListApi = () => {
  return request.get({ url: '/wms/pos/select?_t=' + new Date().getTime() })
}
/**
  * Author: 汪凯华
  * Time: 2024/05/25
  * Function:盘点任务信息保存
*/
export const saveInventoryTaskApi = async (data: InventoryTaskVO) => {
  return await request.put({ url: '/wms/wmschecktask', data})
}
/**
  * Author: 汪凯华
  * Time: 2024/05/24
  * Function:删除盘点任务
*/
export const deleteInventoryTaskApi = async (ctId: number) => {
  return await request.delete({ url: `/wms/wmschecktask/delete/${ctId}`})
}

/**
  * Author: 汪凯华
  * Time: 2024/05/24
  * Function:修改盘点任务状态
*/
export const updateInventoryTaskStatusApi = async (ctId: number, status: number) => {
  return await request.put({ url: `/wms/wmschecktask/updateStatus/${ctId}/${status}`})
}
/**
  * Author: 汪凯华
  * Time: 2024/05/24
  * Function:盘点商品数量
*/
export const updateInventoryTaskGoodsNumApi = async (data: InventoryTaskVO) => {
  return await request.put({ url: '/wms/wms/wmscheckhistory', data})
}
/**
  * Author: 汪凯华
  * Time: 2024/05/24
  * Function:新增盘点任务仓位商品
*/
export const addInventoryTaskGoodsApi = async (data: InventoryTaskVO) => {
  return await request.post({ url: '/wms/wmscheckhistory', data})
}
/**
  * Author: 汪凯华
  * Time: 2024/05/24
  * Function:完成盘点任务
*/
export const finishInventoryTaskApi = async (ctId: number) => {
  return await request.put({ url: `/wms/wmschecktask/finish/${ctId}`})
}
/**
 * Author: 汪凯华
 * Time: 2024/05/24
 * Function:查询下拉框数据共用接口
 */
export const getSelectApi = async (typeCodes: string) => {
  return await request.get({url: "/common/type/getMapTypeCodes?typeCodes=" + typeCodes})
}

/**
  * Author: 汪凯华
  * Time: 2024/05/24
  * Function:获取盘点任务仓位商品列表
*/
export const getInventoryTaskPosGoodsViewPageApi = async (params: InventoryTask) => {
  if (params.manuDate!=null||params.manuDate!=undefined){
    params.manuDateEnd =params.manuDate[1]+" 23:59:59"
    params.manuDateStart=params.manuDate[0]+" 00:00:00"
    delete params.manuDate
  }
 if (params.expiryDate!=null||params.expiryDate!=undefined){
    params.expiryDateEnd =params.expiryDate[1]+" 23:59:59"
    params.expiryDateStart=params.expiryDate[0]+" 00:00:00"
    delete params.expiryDate
  }  if (params.oiTime!=null||params.oiTime!=undefined){
    params.oiTimeEnd =params.oiTime[1]+" 23:59:59"
    params.oiTimeStart=params.oiTime[0]+" 00:00:00"
    delete params.oiTime
  }
  if (params.whName!=null||params.whName!=undefined){
    params.whId =params.whName[0]
    params.posId=params.whName[1]
    delete params.whName
  }
  let ctId= Number(sessionStorage.getItem('ctId'))
  return await request.get({ url: `/wms/wmschecktask/${ctId}`, params})
    .then(res => {
    console.log(res,'res00')

    if(res.histories!=null){
    return res.histories
    }


  })
}

/**
  * Author: 汪凯华
  * Time: 2024/05/24
  * Function:获取盘点任务待上架列表
*/
export const getInventoryTaskShelvesGoodsViewPageApi = async (params: InventoryGoodsTask) => {
  if (params.manuDate!=null||params.manuDate!=undefined){
    params.manuDateEnd =params.manuDate[1]+" 23:59:59"
    params.manuDateStart=params.manuDate[0]+" 00:00:00"
    delete params.manuDate
  }
  params.inOrder = true
  params.egId = parseInt(<string>sessionStorage.getItem('egId'))
  return await request.get({ url: '/wms/traygoods/waitList', params}).then(res => {
    console.log(res,'res01')
    let data={
      list:[],
      total:res.length
    }
    if(res.length>=0){
      res.forEach(item => {
        data.list.push(item)
      })
    }
    return data
  })
}

/**
  * Author: 汪凯华
  * Time: 2024/05/24
  * Function:获取盘点任务未复核列表
*/
export const getInventoryTaskNotReviewGoodsViewPageApi = async (params: InventoryGoodsTask) => {
  if (params.manuDate!=null||params.manuDate!=undefined){
    params.manuDateEnd =params.manuDate[1]+" 23:59:59"
    params.manuDateStart=params.manuDate[0]+" 00:00:00"
    delete params.manuDate
  }
  if (params.arrivalTime!=null||params.arrivalTime!=undefined){
    params.arrivalTimeEnd =params.arrivalTime[1]+" 23:59:59"
    params.arrivalTimeStart=params.arrivalTime[0]+" 00:00:00"
    delete params.arrivalTime
  }  if (params.oiTime!=null||params.oiTime!=undefined){
    params.oiTimeEnd =params.oiTime[1]+" 23:59:59"
    params.oiTimeStart=params.oiTime[0]+" 00:00:00"
    delete params.oiTime
  }
  params.egId = parseInt(<string>sessionStorage.getItem('egId'))
  params.inOrder = false
  return await request.get({ url: '/wms/traygoods/waitList', params}).then(res => {
    let data={
      list:[],
      total:0
    }
    if(res.length>=0){
      res.forEach(item => {
        data.list.push(item)
      })
      data.total=res.length
    }
    return data
  })
}
/**
  * Author: 汪凯华
  * Time: 2024/05/24
  * Function:查询仓位下拉框数据
*/
export const getPosListApi = (whId, wwaId) => {
  if (!wwaId) {
    wwaId = 0
  }
  return request.get({ url: `/wms/pos/selectByWhIdAndWwaId/${whId}/${wwaId}` })
}

/**
  * Author: 汪凯华
  * Time: 2024/05/24
  * Function:查询商品数据
*/
export const getGoodsListApi = () => {
  return request.get({ url: '/wms/entitygoods/list?_t=' + new Date().getTime() })
}

/**
  * Author: 汪凯华
  * Time: 2024/05/24
  * Function:创建盘点任务
*/
export const createInventoryTaskViewApi = (data: InventoryTaskVO) => {
  return request.post({ url: '/wms/wmschecktask', data })
}
/**
  * Author: 汪凯华
  * Time: 2024/05/27
  * Function:创建盘点任务的仓位商品
*/
export const createInventoryTaskHistoryViewApi = (data: InventoryTaskVO) => {
  return request.post({ url: '/wms/wmscheckhistory', data })
}
// 信息修改
export const updateInventoryTaskHistoryViewApi = (data: InventoryTaskVO) => {
  return request.put({ url: '/wms/wmscheckhistory', data })
}


/**
  * Author: 汪凯华
  * Time: 2024/05/24
  * Function:完成盘点任务
*/

export const finishInventoryTaskViewApi = (ctId: number) => {
  return request.put({ url: `wms/wmschecktask/finish/${ctId}` })
}

/**
  * Author: 汪凯华
  * Time: 2024/05/24
  * Function:获取盘点任务信息
*/
export const getInventoryTaskViewApi = (ctId: number) => {
  return request.get({ url: `wms/wmschecktask/${ctId}`})
}
export const transferApi = (data: InventoryTaskVO) => {
  return request.post({ url: '/wms/posGoods/transfer', data })
}


// 信息修改
export const updateInventoryTaskViewApi = (data: InventoryTaskVO) => {
  return request.put({ url: '/wms/pos', data })
}


/**
  * Author: 汪凯华
  * Time: 2024/05/24
  * Function:查询盘点任务列表
*/
// 查询列表
export const getInventoryTaskViewPageApi = async (params: InventoryTask) => {
  if (params.startTime!=null||params.startTime!=undefined){
    params.endTime =params.startTime[1]+" 23:59:59"
    params.startTime=params.startTime[0]+" 00:00:00"
    // delete params.startTime
  }
  if (params.whName!=null||params.whName!=undefined){
    params.whId =params.whName[0]
    params.posId=params.whName[1]
    delete params.whName
  }
  return await request.get({ url: '/wms/wmschecktask/page', params}).then((res) => {
    const list = res.list
    for (let i = 0; i < list.length; i++) {
      list[i].index = i + 1
    }
    return res
  })
}



