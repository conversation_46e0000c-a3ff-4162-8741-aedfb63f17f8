import request from '@/config/axios'

export interface AdjustmentVO {
  adjustmentId: number
  adjustmentStatus: number
  gmtCreate: Date
  gmtModified: Date
  adjustResult:boolean
  approveTime: Date
  approver: number
  adjustmentCode: string
}

export interface AdjustmentPageReqVO extends PageParam {
  adjustmentStatus?: number
  gmtCreate?: Date[]
  gmtModified?: Date[]
  approveTime?: Date[]
  approver?: number
  adjustmentCode?: string
  createTime?: Date[]
  gmtStartTime?: Date[]
  gmtEndTime?: Date[]
}

export interface AdjustmentExcelReqVO {
  adjustmentStatus?: number
  gmtCreate?: Date[]
  gmtModified?: Date[]
  approveTime?: Date[]
  approver?: number
  adjustmentCode?: string
  createTime?: Date[]
}

/**
 * @Author: WangShijun
 * @Date: 2024/10/24
 * @Function: 查询调平单列表接口
 */
export const getAdjustmentPageApi = async (params: AdjustmentPageReqVO) => {
  if (params.createTime!=null||params.createTime!=undefined){
    params.gmtStartTime=params.createTime[0]
    params.gmtEndTime =params.createTime[1]
    delete params.createTime
  }
  return await request.get({ url: '/wms/adjustment/page', params })
}
/**
 * @Author: WangShijun
 * @Date: 2024/10/24
 * @Function: 查询调平单详情接口
 */
export const getAdjustmentApi = async (adjustmentId: number) => {
  return await request.get({ url: '/wms/adjustment/get?adjustmentId=' + adjustmentId })
}
/**
  * Author: 汪凯华
  * Time: 2024/05/23
  * Function:审核调平单通过
*/
export const adjustmentApproveApi = async (params: AdjustmentVO) => {
  return await request.put({ url: `/wms/adjustment/approve/${params.adjustmentId}?adjustResult=true` })
}
/**
  * Author: 汪凯华
  * Time: 2024/05/23
  * Function:审核调平单不通过
*/
export const adjustmentRejectApi = async (data: AdjustmentVO) => {
  data.adjustResult=false
  return await request.put({ url: '/wms/adjustment/approve', data })
}
/**
 * @Author: WangShijun
 * @Date: 2024/10/24
 * @Function: 导出调平单 Excel接口
 */
export const exportAdjustmentApi = async (params: AdjustmentExcelReqVO) => {
  return await request.download({ url: '/wms/adjustment/export', params })
}
