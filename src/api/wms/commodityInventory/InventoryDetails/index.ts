import request from '@/config/axios'

export interface PosVO {
  customsCode: string
  partld?: number
  measureUnit:string
  valueAddedTaxRate:number
  refundTaxRate:number
  customsChineseName:string
  customsEnglishName:string
  declareElementRefer:string
  declareElement:string
  supervisionCondition:string
  createTime:string
}

export interface Inventory {
  pageNo: number
  pageSize:number
  posId:any
  whId:any

}

export interface TenantPageReqVO extends PageParam {
  name?: string
  contactName?: string
  contactMobile?: string
  status?: number
  createTime?: Date[]
}

export interface TenantExportReqVO {
  name?: string
  contactName?: string
  contactMobile?: string
  status?: number
  createTime?: Date[]
}


/**
 * Author: 汪凯华
 * Time: 2024/05/22
 * Function:查询下拉框数据共用接口
 */
export const getSelectApi = async (typeCodes: string) => {
  return await request.get({url: "/common/type/getMapTypeCodes?typeCodes=" + typeCodes})
}


/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:查询盘点明细列表
*/
export const getInventoryViewPageApi = async (params: Inventory) => {
  if(params.username!=null||params.username!=undefined){
    params.creator =params.username
    delete params.username
  }
  if (params.manuDate!=null||params.manuDate!=undefined){
    params.manuDateEnd =params.manuDate[1]+" 23:59:59"
    params.manuDateStart=params.manuDate[0]+" 00:00:00"
    delete params.manuDate
  }
  if (params.createTime!=null||params.createTime!=undefined){
    params.createTimeEnd =params.createTime[1]+" 23:59:59"
    params.createTimeStart=params.createTime[0]+" 00:00:00"
    delete params.createTime
  }  if (params.expiryDate!=null||params.expiryDate!=undefined){
    params.expiryDateEnd =params.expiryDate[1]+" 23:59:59"
    params.expiryDateStart=params.expiryDate[0]+" 00:00:00"
    delete params.expiryDate
  }  if (params.arrivalTime!=null||params.arrivalTime!=undefined){
    params.arrivalTimeEnd =params.arrivalTime[1]+" 23:59:59"
    params.arrivalTimeStart=params.arrivalTime[0]+" 00:00:00"
    delete params.arrivalTime
  }
  if (params.whName!=null||params.whName!=undefined){
    params.whId =params.whName[0]
    params.posId=params.whName[1]
    delete params.whName
  }
  // params.posId = Number(sessionStorage.getItem('posId'))
  return await request.get({ url: '/wms/wmscheckhistory/page', params})
}


/**
 * Author: 汪凯华
 * Time: 2024/05/22
 * Function:生成调平单
 */
export const createLevelingSheetApi = (data: Inventory ) => {
  return request.post({ url: '/wms/adjustment', data })
}


