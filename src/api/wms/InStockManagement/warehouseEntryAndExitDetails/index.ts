import request from '@/config/axios'

export interface EntryAndExitDetailsVO {
  customsCode: string
  partld?: number
  measureUnit:string
  valueAddedTaxRate:number
  refundTaxRate:number
  customsChineseName:string
  customsEnglishName:string
  declareElementRefer:string
  declareElement:string
  supervisionCondition:string
  createTime:string
}

export interface EntryAndExitDetails {
  pageNo: number
  pageSize:number
  rdiId: any
}

export interface TenantPageReqVO extends PageParam {
  name?: string
  contactName?: string
  contactMobile?: string
  status?: number
  createTime?: Date[]
}

export interface TenantExportReqVO {
  name?: string
  contactName?: string
  contactMobile?: string
  status?: number
  createTime?: Date[]
}

/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:查询字典下拉框数据
*/
export const getEntryAndExitDetailsSelectListApi = (typeCodes: string) => {
  return request.get({ url: '/common/type/getMapTypeCodes?typeCodes=' + typeCodes })
}
/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:获取负责人下拉框数据
*/
export const getUserApi = async () => {
  return await request.get({ url: '/system/user/list'})
}


/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:查询出入库明细列表
*/
export const getEntryAndExitViewPageApi = async (params: EntryAndExitDetails) => {
  return await request.get({ url: '/wms/inrditem/inventory/page', params})
}

/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:查询出入库商品明细信息
*/
export const getEntryAndExitDetailsPosGoodsViewPageApi = async (params: EntryAndExitDetails) => {
  params.rdiId ? params.rdiId = params.rdiId : params.rdiId = sessionStorage.getItem('rdiId')
  return await request.get({ url: '/wms/pos-goods/pageByRdiId', params}).then(res => {
    return res
  })
}
/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:查询出入库仓位商品明细列表
*/
export const getEntryAndExitDetailsPosGoodsListPageApi = async (params: EntryAndExitDetails) => {
  params.rdiId=sessionStorage.getItem('rdiId')
  return await request.get({ url: '/wms/pos-goods/pageByRdiId', params}).then(res => {
    let data={
      list:res.list,
      total:res.total
    }

    return data
  })
}
/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:查询出入库待上架商品明细列表
*/
export const getEntryAndExitDetailsWaitUpByRdiIdPosGoodsViewPageApi = async (params: EntryAndExitDetails) => {
  return await request.get({ url: '/wms/traygoods/waitUpByRdiId', params})
}
/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:查询出入库待复核商品明细列表
*/
export const getEntryAndExitDetailsWaitCheckByRdiIdPosGoodsViewPageApi = async (params: EntryAndExitDetails) => {
  return await request.get({ url: '/wms/traygoods/waitCheckByRdiId', params})
}
/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:获取字典下拉框数据
*/
// 获取下拉框数据
export const getSelectApi = async (typeCodes: string) => {
  return await request.get({url: "/common/type/getMapTypeCodes?typeCodes=" + typeCodes})
}

