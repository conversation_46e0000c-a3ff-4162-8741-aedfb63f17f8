import request from '@/config/axios'

export interface PositionCommoditiesVO {
    customsCode: string
    partld?: number
    measureUnit: string
    valueAddedTaxRate: number
    refundTaxRate: number
    customsChineseName: string
    customsEnglishName: string
    declareElementRefer: string
    declareElement: string
    supervisionCondition: string
    createTime: string
}

export interface PositionCommodities {
    oiId: number
    pageNo: number
    pageSize: number
    manuEndDate?: Date[]
    manuStartDate?: Date[]
    manuDate?: Date[]
    oiEndDate?: Date[]
    oiStartDate?: Date[]
    oiTime?: Date[]
    expiryEndDate?: Date[]
    expiryStartDate?: Date[]
    expiryDate?: Date[]
}

export interface TenantPageReqVO extends PageParam {
    name?: string
    contactName?: string
    contactMobile?: string
    status?: number
    createTime?: Date[]
}

export interface TenantExportReqVO {
    name?: string
    contactName?: string
    contactMobile?: string
    status?: number
    createTime?: Date[]
}


/**
 * Author: 汪凯华
 * Time: 2024/05/22
 * Function:查询商品条码下拉框数据
 */
export const getPositionCommoditiesBarCodeSelectListApi = () => {
    return request.get({url: '/wms/entitygoods/select/barCode'})
}

/**
 * Author: 汪凯华
 * Time: 2024/05/22
 * Function:查询目标仓位下拉框信息
 */
export const getPositionCommoditiesMatchingApi = (params: PositionCommodities) => {
    return request.get({url: '/wms/pos/matching', params})
}
/**
 * Author: 汪凯华
 * Time: 2024/05/22
 * Function:库内商品转移
 */
export const TransferPosGoodsApi = (data: PositionCommoditiesVO) => {
    return request.post({url: '/wms/pos-goods/transfer', data})
}

/**
 * Author: 汪凯华
 * Time: 2024/05/22
 * Function:查询仓位商品列表
 */
export const getPositionCommoditiesViewPageApi = async (params: PositionCommodities) => {
    if (params.manuDate != null || params.manuDate != undefined) {
        params.manuStartDate = params.manuDate[0]
        params.manuEndDate = params.manuDate[1]
        delete params.manuDate
    }
    if (params.expiryDate != null || params.expiryDate != undefined) {
        params.expiryStartDate = params.expiryDate[0]
        params.expiryEndDate = params.expiryDate[1]
        delete params.expiryDate
    }
    if (params.oiTime != null || params.oiTime != undefined) {
        params.manuStartDate = params.oiTime[0]
        params.manuEndDate = params.oiTime[1]
        delete params.oiTime
    }
    return await request.get({url: '/wms/pos-goods/page', params})
}
/**
 * Author: 汪凯华
 * Time: 2024/05/22
 * Function:查询仓位商品中转移商品列表
 */
export const getPositionCommoditiesGoodsViewPageApi = async (ids: number[]) => {
    // console.log(ids,'cc')
    //
    // return await request.get({url: "/wms/pos-goods/list",params:{ids} })
    // 将 `ids` 数组转换为查询字符串
    const params = new URLSearchParams();
    ids.forEach(id => params.append('ids', id.toString()));

    // 构建 URL 包含查询字符串
    const url = `/wms/pos-goods/list?${params.toString()}`;

    return await request.get({url}).then(res => {
        let data = {
            list: []
        }
        data.list = res
        console.log(data, 'data')
        return data
    })
}

/**
 * Author: 汪凯华
 * Time: 2024/05/22
 * Function:查询商品条码
 */
export const getPositionCommoditiesBarCodeApi = () => {
    return request.get({url: '/wms/entitygoods/select/barCode'})
}

