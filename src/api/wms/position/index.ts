import request from '@/config/axios'

export interface PosVO {
  customsCode: string
  partld?: number
  measureUnit:string
  valueAddedTaxRate:number
  refundTaxRate:number
  customsChineseName:string
  customsEnglishName:string
  declareElementRefer:string
  declareElement:string
  supervisionCondition:string
  createTime:string
}

export interface Pos {
  pageNo: number
  pageSize:number
  posId:any
  whId:any

}

export interface TenantPageReqVO extends PageParam {
  name?: string
  contactName?: string
  contactMobile?: string
  status?: number
  createTime?: Date[]
}

export interface TenantExportReqVO {
  name?: string
  contactName?: string
  contactMobile?: string
  status?: number
  createTime?: Date[]
}
/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:查询单条仓位信息
*/
export const getPosViewApi = (partId: number) => {
  return request.get({ url: 'wms/pos/getInfo' + '/' +partId })
}
/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:查询订单号查询目标仓位下拉框数据
*/
export const getMatchingAPi = async (egId, oiId) => {
  const url = `/wms/pos/matching?egId=${egId}&oiId=${oiId}&_t=${Date.now()}`;
  return await request.get({ url });
};
/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:查询全部仓位下拉框数据
*/
export const getPosSelectListApi = () => {
  return request.get({ url: 'wms/warehouse-area/select?_t=' + new Date().getTime() })
}
/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:单条新增仓位信息
*/
export const createPosViewApi = (data: PosVO) => {
  return request.post({ url: '/wms/pos', data })
}

/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:单条/多条库内转移
*/
export const transferApi = (data: PosVO) => {
  return request.post({ url: '/wms/posGoods/transfer', data })
}

/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:单条仓位信息修改
*/
export const updatePosViewApi = (data: PosVO) => {
  return request.put({ url: '/wms/pos', data })
}

/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:仓位列表查询
*/
export const getPosViewPageApi = async (params: Pos) => {
  // params.posId = Number(sessionStorage.getItem('posId'))
  return await request.get({ url: '/wms/pos/page', params})
}

/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:查询仓库下仓位列表查询
*/
export const getWhPosViewPageApi = async (params: Pos) => {
    params.whId = sessionStorage.getItem('whId')
  return await request.get({ url: '/wms/pos/page', params})
}
/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:仓位商品列表查询
*/
export const getPosGoodsViewPageApi = async (params: Pos) => {
  params.posId = sessionStorage.getItem('posId')
  return await request.get({ url: '/wms/pos-goods/page', params})
}

/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:生成仓位码
*/
export const getPosCodeApi = async (ids: number[]) => {
  // 将ids数组转换为逗号分隔的字符串
  const idsParam = ids.map(id => `ids=${id}`).join('&');
  // 拼接完整的URL
  const url = `/wms/pos/barCode?${idsParam}`;
  console.log(url);
  // 发起请求
  return await request.get({url});
}

/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:单条删除仓位信息
*/
export const delPosApi = async (ids: number[]) => {

  return await request.delete({ url: '/wms/pos',  data: ids })
}

/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:批量删除仓位信息
*/
export const delPosListApi = (ids: number[]) => {
  console.log(ids,'9')
  return request.delete({ url: '/wms/pos', data: ids })
}

/**
  * Author: 汪凯华
  * Time: 2024/05/28
  * Function:查询仓库仓位
*/
export const getwhPosListApi = () => {
  return request.get({ url: '/wms/pos/select?_t=' + new Date().getTime() })
}
