import request from '@/config/axios'

export interface StoreHouseVO {
  customsCode: string
  partld?: number
  measureUnit:string
  valueAddedTaxRate:number
  refundTaxRate:number
  customsChineseName:string
  customsEnglishName:string
  declareElementRefer:string
  declareElement:string
  supervisionCondition:string
  createTime:string
}

export interface StoreHouse {
  whAddress: any
  pageNo: number
  pageSize:number
}

export interface TenantPageReqVO extends PageParam {
  name?: string
  contactName?: string
  contactMobile?: string
  status?: number
  createTime?: Date[]
}

export interface TenantExportReqVO {
  name?: string
  contactName?: string
  contactMobile?: string
  status?: number
  createTime?: Date[]
}

/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:查询单条仓库信息
*/
export const getStoreHouseViewApi = (id: number) => {
  return request.get({ url: '/wms/warehouse/' + id })
}

/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:查询所有仓库下拉框数据
*/
//查询下拉框数据
export const getStoreHouseListApi = () => {
  return request.get({ url: '/wms/warehouse/select?_t=' + new Date().getTime()})
}
/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:查询仓库负责人下拉框数据
*/
export const getUserApi = async () => {
  return await request.get({ url: '/system/user/list'})
}
/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:仓库信息新增
*/
export const createStoreHouseViewApi = (data: StoreHouseVO) => {
  return request.post({ url: '/wms/warehouse/create', data })
}
/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:仓库信息修改
*/
export const updateStoreHouseViewApi = (data: StoreHouseVO) => {
  return request.put({ url: '/wms/warehouse/update', data })
}

/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:查询仓库列表
*/
export const getStoreHouseViewPageApi = async (params: StoreHouse) => {
  if(params.whAddress==''){
    params.whAddress=null
  }
  return await request.get({ url: '/wms/warehouse/page', params})
}
/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:获取仓位列表
*/
export const getPosListApi = async (params: StoreHouse) => {
  return await request.get({ url: '/wms/pos/page', params})
}


/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:单条删除仓库信息
*/
export const delStoreHouseApi = async (id: number[]) => {

  return await request.delete({ url: '/wms/warehouse',  data: id })
}

/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:批量删除仓库信息
*/
export const delStoreHouseListApi = (ids: number[]) => {
  return request.delete({ url: '/wms/warehouse', data: ids })
}
/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:获取共用类型下拉框数据
*/
export const getSelectApi = async (typeCodes: string) => {
  return await request.get({url: "/common/type/getMapTypeCodes?typeCodes=" + typeCodes})
}
