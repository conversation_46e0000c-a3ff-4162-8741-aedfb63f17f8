import request from '@/config/axios'

export interface WmsWareHousePicVO {
  customsCode: string
  partld?: number
  measureUnit:string
  valueAddedTaxRate:number
  refundTaxRate:number
  customsChineseName:string
  customsEnglishName:string
  declareElementRefer:string
  declareElement:string
  supervisionCondition:string
  createTime:string
}

export interface WmsWareHousePic {
  pageNo: number
  pageSize:number
}

export interface TenantPageReqVO extends PageParam {
  name?: string
  contactName?: string
  contactMobile?: string
  status?: number
  createTime?: Date[]
}

export interface TenantExportReqVO {
  name?: string
  contactName?: string
  contactMobile?: string
  status?: number
  createTime?: Date[]
}

/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:查询垂直编号下拉框数据
*/
export const getPosFloorListApi = (id: number) => {
  return request.get({ url: `/wms/pos/getIndexZ/${id}?_t=`+ new Date().getTime() })
}
/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:查询仓位位置信息
*/
export const getMapByWhIdAndIndexZApi = (params: any) => {

  return request.get({ url: `/wms/pos/getMapByWhIdAndIndexZ/${params.whId}/${params.posFloor}/?_t=`+ new Date().getTime() })
}
/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:获取仓位号
*/
export const getPosIdApi = (params: any) => {
  params._t=new Date().getTime()
  return request.get({ url: '/wms/pos/getPosId', params })
}
/**
  * Author: 汪凯华
  * Time: 2024/05/22
  * Function:获取仓位信息
*/
export const getPosMapInfoApi = (id: number) => {
  return request.get({ url: '/wms/pos/posMapInfo/' + id })
}









// 批量删除
export const delWmsWareHousePicListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/wms/warehouse/delete-batch?ids=' + ids })
}
