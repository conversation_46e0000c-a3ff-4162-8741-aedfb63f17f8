import request from '@/config/axios'

/**
  * Author： 许祎婷
  * Time： 2024/05/16
  * Description：查询入库订单列表
*/
export const getOutOrderPageApi = async (params) => {
  if (params.eaTime!=null||params.eaTime!=undefined) {
    params.eaTime[0] = params.eaTime[0] + " 00:00:00"
    params.eaTime[1]  = params.eaTime[1] + " 23:59:59"
  }
  if(params.orgName!=null&&params.orgName!=''&&params.orgName!=undefined){
    params.orgId=params.orgName
    delete params.orgName
  }
  if(params.whName!=null&&params.whName!=''&&params.whName!=undefined){
    params.whId=params.whName
    delete params.whName
  }
  return await request.get({ url: 'wms/outOrder/page', params })
}
/**
  * Author： 许祎婷
  * Time： 2024/05/16
  * Description：查询订单明细列表
*/
export const getOutOrderItemPageApi = async (params) => {
  return await request.get({ url: '/wms/outorderitem/page', params })
}
/**
 * Author： 许祎婷
 * Time： 2024/05/20
 * Description：查询商品列表
 */

export const getGoodsListApi = () => {
  return request.get({ url: '/wms/entitygoods/list'})
}
/**
 * Author： 许祎婷
 * Time： 2024/05/20
 * Description：查询出库订单详情
 */
export const getOutOrderApi = async (id: number) => {
  return await request.get({ url: '/wms/outOrder/' + id })
}


/**
  * Author： 许祎婷
  * Time： 2024/05/16
  * Description：复核信息
*/
export const getOutOrderItemApi = async (id: number) => {
    return await request.get({ url: '/wms/outorderitem/selectOff/' + (id || 0) })
}

/**
 * Author： 许祎婷
 * Time： 2024/05/16
 * Description：获取人员信息
 */
export const getPersonApi = async (id: number) => {
  return await request.get({ url: '/system/user/get?id=' + id })
}

/**
  * Author： 许祎婷
  * Time： 2024/05/16
  * Description：查询客户/供应商下拉框数据
*/
export const getCustomerSelectListApi = (orgCategory:number) => {
  return request.get({ url: '/common/org/list?orgCategory=' + orgCategory})
}


/**
  * Author： 许祎婷
  * Time： 2024/05/16
  * Description：查询仓库列表
*/
export const getWhListApi = () => {
  return request.get({ url: '/wms/warehouse/select'})
}


  /**
    * Author： 许祎婷
    * Time： 2024/05/16
    * Description：计算入库订单
  */
  export const computeApi = async (orderId) => {
    return await request.put({ url: `/wms/outOrder/refreshAllNum/${orderId}`})
  }

  /**
    * Author： 许祎婷
    * Time： 2024/05/16
    * Description：新增入库订单
  */
export const createOutOrderApi = async (data) => {
    return await request.post({ url: 'wms/outOrder', data })
}

/**
  * Author： 许祎婷
  * Time： 2024/05/17
  * Description：完成订单
*/
export const finishOrderApi = async (data) => {
  return await request.put({ url: '/wms/outOrder/updateStatusByFlag', data })
}


/**
  * Author： 许祎婷
  * Time： 2024/05/17
  * Description：取消订单
*/
export const deleteOutOrderApi = async (data) => {
  return await request.delete({ url: 'wms/outOrder', data })
}

/**
 * Author： 许祎婷
 * Time： 2024/05/21
 * Description：修改订单明细
 */
export const updateOutOrderItemApi = async (data) => {
  return await request.put({ url: '/wms/outorderitem', data })
}

/**
  * Author： 许祎婷
  * Time： 2024/05/17
  * Description：删除订单明细
*/
export const deleteOutOrderItemApi = async (data) => {
  return await request.delete({ url: '/wms/outorderitem', data })
}


/**
 * Author： 许祎婷
 * Time： 2024/05/20
 * Description：查询选中的数据
 */
export const outOrderSelectApi = (ids) => {
  return request.get({url: '/wms/outOrder/selectByIds?' + ids })
}


/**
  * Author： 许祎婷
  * Time： 2024/05/21
  * Description：
*/
export const getLuItemApi = (id) => {
  return request.get({url: '/wms/outorderitem/getInfoByOiId/' + id })
}

/**
  * Author： 许祎婷
  * Time： 2024/05/21
  * Description：仓位查询
*/
export const getPosApi = (id) => {
  return request.get({url: '/wms/pos/selectByOiId/' + id })
}

/**
  * Author： 许祎婷
  * Time： 2024/05/21
  * Description：下架
*/
export const luItemApi = async (data) => {
  return await request.post({ url: '/wms/luitem', data })
}

/**
 * Author： 许祎婷
 * Time： 2024/05/21
 * Description：出库订单商品列表
 */
export const getOutOrderGoodsApi = async (whId) => {
  return await request.get({ url: '/wms/pos-goods/getByContractId?whId=' + whId })
}

/**
 * Author： 许祎婷
 * Time： 2024/05/22
 * Description：获取下拉框数据
 */
export const getSelectApi = async (typeCodes: string) => {
  return await request.get({ url: "/common/type/getMapTypeCodes?typeCodes=" + typeCodes })
}


/**
  * Author： 许祎婷
  * Time： 2024/05/22
  * Description：出库订单明细新增
*/
export const createOutOrderItemApi = async (data) => {
  return await request.post({ url: '/wms/outorderitem/saveBatch', data })
}

/**
  * Author： 许祎婷
  * Time： 2024/05/23
  * Description：复核
*/
export const reviewApi = async (data) => {
  return await request.post({ url: '/wms/outrditem/review', data })
}

