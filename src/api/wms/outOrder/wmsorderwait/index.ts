import request from '@/config/axios'


// 查询已下架未复核列表
export const getOutOrderWaitPageApi = async (params) => {
  if(params.orgName!=null&&params.orgName!=''&&params.orgName!=undefined){
    params.orgId=params.orgName
    delete params.orgName
  }
  if (params.manuDate!=null||params.manuDate!=undefined) {
    params.manuStartDate = params.manuDate[0] + " 00:00:00"
    params.manuEndDate = params.manuDate[1] + " 23:59:59"
    delete params.manuDate
  }
  return await request.get({ url: '/wms/traygoods/waitCheck', params })
}

// 查询入库异常订单列表
export const getInorderItemApi = async (params) => {
  return await request.get({ url: '/wms/inorderitem/page', params })
}


// 查询入库订单详情
export const getPartVersionApi = async (id: number) => {
  return await request.get({ url: '/part/version/get?id=' + id })
}
//获取人员信息
export const getPersonApi = async (id: number) => {
  return await request.get({ url: '/system/user/get?id=' + id })
}

// 新增入库订单
export const createPartVersionApi = async (data) => {
  return await request.post({ url: '/part/version/create', data })
}

// 修改入库订单
export const updatePartVersionApi = async (data) => {
  return await request.put({ url: '/part/version/update', data })
}

// 删除入库订单
export const deleteInOrderApi = async (data) => {
  return await request.delete({ url: '/wms/inOrder', data })
}

// 导出入库订单 Excel
export const exportPartVersionApi = async (params) => {
  return await request.download({ url: '/part/version/export-excel', params })
}

// 获取下拉框数据
export const getSelectApi = async (typeCodes: string) => {
  return await request.get({ url: "/common/type/getMapTypeCodes?typeCodes=" + typeCodes })
}
// 获取下拉框树形数据
export const getSelectListApi = async (typeCodes: string) => {
  return await request.get({ url: "/common/type/getTypeCodes?typeCodes=" + typeCodes })
}

//获取负责人下拉框数据
export const getUserApi = async () => {
  return await request.get({ url: '/system/user/list'})
}
//获取部门下负责人下拉框数据
export const getDeptUserApi = async (deptId:number,searchStatus?:number) => {
  var str='';
  if(deptId!=null&&deptId!=undefined){
    str='deptId='+deptId
  }
  if(searchStatus!=null&&searchStatus!=undefined){
    if(str){
      str+='&'
    }
    str+='searchStatus='+searchStatus
  }
  // console.log(str!='',888)
  if(str!=''){
    str='?'+str
  }
  return await request.get({ url: '/system/user/list'+str})
}

//获取轮次下拉框数据
export const getRoundNumberSelectApi = async (entityCode: String, objectId: string | null) => {
  return await request.get({url: '/project/rfq-round/get-round?entityCode=' + entityCode+ '&objectId=' + objectId})
}

//获取采购组下拉框数据
export const getDeptIdApi = async (type: number) => {
  return await request.get({url: '/system/dept/list?type=' + type})
}

//查询物料组的计量单位
export const getUnitListApi = async (id) => {
  const linkType = "'g_type_part_version_group_base_entity_link','g_type_part_version_group_entity_link'"
  return request.get({ url: '/common/entity-link/list?linkType='+ linkType +'&primaryObjectId=' + id })
}
//查询物料组的单位
export const getUnitApi = async (id) => {
  return request.get({ url: '/common/type/get?id=' + id })
}
