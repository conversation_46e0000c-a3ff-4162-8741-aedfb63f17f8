import request from '@/config/axios'

/**
  * Author： 许祎婷
  * Time： 2024/05/21
  * Description：查询入库订单列表
*/
export const getInOrderItemPageApi = async (params) => {
  if(params.orgName!=null&&params.orgName!=''&&params.orgName!=undefined){
    params.orgId=params.orgName
    delete params.orgName
  }
  if (params.manuDate!=null||params.manuDate!=undefined) {
    params.manuStartDate = params.manuDate[0] + " 00:00:00"
    params.manuEndDate = params.manuDate[1] + " 23:59:59"
    delete params.manuDate
  }
  return await request.get({ url: '/wms/inorderitem/page', params })
}

/**
  * Author： 许祎婷
  * Time： 2024/05/21
  * Description：查询上架记录列表
*/
export const getLuItemPageApi = async (params) => {
  if(params.orgName!=null&&params.orgName!=''&&params.orgName!=undefined){
    params.orgId=params.orgName
    delete params.orgName
  }
  if (params.manuDate!=null||params.manuDate!=undefined) {
    params.manuStartDate = params.manuDate[0] + " 00:00:00"
    params.manuEndDate = params.manuDate[1] + " 23:59:59"
    delete params.manuDate
  }
  return await request.get({ url: '/wms/luitem/page', params })
}



// 获取下拉框数据
export const getSelectApi = async (typeCodes: string) => {
  return await request.get({ url: "/common/type/getMapTypeCodes?typeCodes=" + typeCodes })
}
