import request from '@/config/axios'

/**
  * Author： 许祎婷
  * Time： 2024/05/16
  * Description：查询入库订单列表
*/
export const getInOrderPageApi = async (params) => {
  if(params.orgName!=null&&params.orgName!=''&&params.orgName!=undefined){
    params.orgId=params.orgName
    delete params.orgName
  }
  if(params.whName!=null&&params.whName!=''&&params.whName!=undefined){
    params.whId=params.whName
    delete params.whName
  }
  if (params.eaTime!=null||params.eaTime!=undefined) {
    params.eaTime[0] = params.eaTime[0] + " 00:00:00"
    params.eaTime[1] = params.eaTime[1] + " 23:59:59"
  }
  return await request.get({ url: '/wms/inOrder/page', params })
}
/**
  * Author： 许祎婷
  * Time： 2024/05/16
  * Description：查询订单明细列表
*/
export const getInOrderItemPageApi = async (params) => {
  return await request.get({ url: '/wms/inorderitem/page', params })
}

/**
  * Author： 许祎婷
  * Time： 2024/05/16
  * Description：查询入库订单详情
*/
export const getInOrderApi = async (id: number) => {
    return await request.get({ url: '/wms/inOrder/' + id })
}

/**
 * Author： 许祎婷
 * Time： 2024/05/16
 * Description：获取人员信息
 */
export const getPersonApi = async (id: number) => {
  return await request.get({ url: '/system/user/get?id=' + id })
}

/**
  * Author： 许祎婷
  * Time： 2024/05/16
  * Description：查询客户/供应商下拉框数据
*/
export const getCustomerSelectListApi = (orgCategory:number) => {
  return request.get({ url: '/common/org/list?orgCategory=' + orgCategory})
}


/**
 * Author： 许祎婷
 * Time： 2024/05/20
 * Description：查询商品列表
 */

export const getGoodsListApi = () => {
  return request.get({ url: '/wms/entitygoods/list'})
}



/**
  * Author： 许祎婷
  * Time： 2024/05/16
  * Description：查询仓库列表
*/
export const getWhListApi = () => {
  return request.get({ url: '/wms/warehouse/select'})
}


  /**
    * Author： 许祎婷
    * Time： 2024/05/16
    * Description：计算入库订单
  */
  export const computeApi = async (id) => {
    return await request.put({ url: '/wms/inOrder/refreshAllNum/' + id})
  }
  /**
    * Author： 许祎婷
    * Time： 2024/05/21
    * Description：计算实收数量
  */
  export const refreshApi = async (id) => {
    return await request.put({ url: '/wms/inOrder/refreshActualNum' + id})
  }

  /**
    * Author： 许祎婷
    * Time： 2024/05/16
    * Description：新增入库订单
  */
export const createInOrderApi = async (data) => {
    return await request.post({ url: '/wms/inOrder', data })
}

/**
  * Author： 许祎婷
  * Time： 2024/05/17
  * Description：完成订单
*/
export const finishOrderApi = async (data) => {
  return await request.put({ url: '/wms/inOrder/updateStatusByFlag', data })
}


/**
  * Author： 许祎婷
  * Time： 2024/05/17
  * Description：取消订单
*/
export const deleteInOrderApi = async (data) => {
  return await request.delete({ url: '/wms/inOrder', data })
}

/**
  * Author： 许祎婷
  * Time： 2024/05/17
  * Description：新增订单明细
*/
export const createOrderItemApi = async (data) => {
  return await request.post({ url: '/wms/inorderitem', data })
}

/**
  * Author： 许祎婷
  * Time： 2024/05/17
  * Description：修改订单明细
*/
export const updateOrderItemApi = async (data) => {
    return await request.put({ url: '/wms/inorderitem', data })
}

/**
  * Author： 许祎婷
  * Time： 2024/05/17
  * Description：删除订单明细
*/
export const deleteInOrderItemApi = async (data) => {
  return await request.delete({ url: '/wms/inorderitem', data })
}

/**
  * Author： 许祎婷
  * Time： 2024/05/17
  * Description：生成标签
*/
export const getQRCodeApi = async (ids) => {
  return await request.get({ url: '/wms/inorderitem/QRCode?' + ids })
}

/**
  * Author： 许祎婷
  * Time： 2024/05/16
  * Description：获取附件列表
*/
export const getFilePageApi = (params) => {
  return request.post({url: '/infra/file/list', params}).then((res) => {
    const list = res.list
    let updateUrl = 'http://' + window.location.hostname + import.meta.env.VITE_SERVICE_PORT + import.meta.env.VITE_API_URL
    if (list) {
      for (let i = 0; i < list.length; i++) {
        list[i].url = updateUrl + list[i].url;
        console.log(list)
      }
    }
    return res
  })
}
/**
 * Author： 许祎婷
 * Time： 2024/05/16
 * Description：删除文件
 */
export const deleteFileApi = (id: number) => {
  return request.delete({url: '/infra/file/delete?id=' + id})
}

/**
  * Author： 许祎婷
  * Time： 2024/05/20
  * Description：查询选中的数据
*/
export const inOrderSelectApi = (ids) => {
return request.get({url: '/wms/inOrder/selectByIds?' + ids })
}


/**
 * Author： 许祎婷
 * Time： 2024/05/20
 * Description：查询erp物料详情
 */
export const getErpGoodsDetailApi = async (id: number) => {
  return await request.get({ url: '/wms/entitygoods/' + (id || 0) })
}

/**
 * Author： 许祎婷
 * Time： 2024/05/17
 * Description：验收入库查询
 */
export const getInOrderItem = async (oiId) => {
  return await request.get({ url: '/wms/inorderitem/' + oiId })
}


/**
  * Author： 许祎婷
  * Time： 2024/05/20
  * Description：验收入库
*/
export const inRditemApi = async (data) => {
  return await request.post({ url: '/wms/inrditem', data })
}

/**
  * Author： 许祎婷
  * Time： 2024/05/21
  * Description：预上架切换仓位
*/
export const readyPosApi = async (data) => {
  return await request.post({ url: '/wms/pos/predistribution', data })
}


/**
 * Author： 许祎婷
 * Time： 2024/05/20
 * Description：查询验收入库记录详情
 */
export const getRditemApi = async (id) => {
  return await request.get({ url: '/wms/inrditem/'+ id })
}

/**
  * Author： 许祎婷
  * Time： 2024/05/22
  * Description：获取下拉框数据
*/
export const getSelectApi = async (typeCodes: string) => {
  return await request.get({ url: "/common/type/getMapTypeCodes?typeCodes=" + typeCodes })
}

/**
  * Author： 许祎婷
  * Time： 2024/05/28
  * Description：导出excel
*/
export const getExportListApi = async (params) => {
  return await request.get({ url: "/wms/inOrder/export", params })
}
