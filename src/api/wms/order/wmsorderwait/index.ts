import request from '@/config/axios'


// 查询已验收未上架列表
export const getInOrderWaitPageApi = async (params) => {
  if(params.orgName!=null&&params.orgName!=''&&params.orgName!=undefined){
    params.orgId=params.orgName
    delete params.orgName
  }
  if (params.manuDate!=null||params.manuDate!=undefined) {
    params.manuStartDate = params.manuDate[0] + " 00:00:00"
    params.manuEndDate = params.manuDate[1] + " 23:59:59"
    delete params.manuDate
  }
  return await request.get({url: '/wms/traygoods/waitUp', params})
}

// 查询入库异常订单列表
export const getInorderItemApi = async (params) => {
  return await request.get({url: '/wms/inorderitem/page', params})
}


// 查询入库订单详情
export const getPartVersionApi = async (id: number) => {
  return await request.get({url: '/part/version/get?id=' + id})
}

/**
 * Author： 许祎婷
 * Time： 2024/05/21
 * Description：获取客户列表
 */
export const getCustomerSelectListApi = (orgCategory: number) => {
  return request.get({url: '/common/org/list?orgCategory=' + orgCategory})
}

/**
 * Author： 许祎婷
 * Time： 2024/05/21
 * Description：获取上架详情
 */
export const getUpInfoApi = (id) => {
  return request.get({url: '/wms/traygoods/' + id})
}

/**
 * Author： 许祎婷
 * Time： 2024/05/21
 * Description：获取仓位列表
 */
export const getPosListApi = (id) => {
  return request.get({url: '/wms/inorderitem/getPrePos/' + id})
}

/**
 * Author： 许祎婷
 * Time： 2024/05/21
 * Description：查仓位
 */
export const getPosMatchApi = (msg) => {
  return request.get({url: '/wms/pos/matching' + msg})
}


/**
 * Author： 许祎婷
 * Time： 2024/05/21
 * Description：上架
 */
export const getPreCountApi = (msg) => {
  return request.get({url: '/wms/inorderitem/getPreCount' + msg})
}

/**
  * Author： 许祎婷
  * Time： 2024/05/21
  * Description：
*/
export const luItemApi = async (data) => {
  return await request.post({ url: '/wms/luitem', data })
}
// 获取下拉框数据
export const getSelectApi = async (typeCodes: string) => {
  return await request.get({url: "/common/type/getMapTypeCodes?typeCodes=" + typeCodes})
}
