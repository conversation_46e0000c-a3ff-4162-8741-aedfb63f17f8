import request from '@/config/axios'


// 查询入库订单列表
export const getInRditemPageApi = async (params) => {
  if(params.orgName!=null&&params.orgName!=''&&params.orgName!=undefined){
    params.orgId=params.orgName
    delete params.orgName
  }
  if (params.manuDate!=null||params.manuDate!=undefined) {
    params.manuStartDate = params.manuDate[0] + " 00:00:00"
    params.manuEndDate = params.manuDate[1] + " 23:59:59"
    delete params.manuDate
  }
  return await request.get({ url: '/wms/inrditem/page', params })
}

// 查询入库异常订单列表
export const getErrorOrderApi = async (params) => {
  if(params.orgName!=null&&params.orgName!=''&&params.orgName!=undefined){
    params.orgId=params.orgName
    delete params.orgName
  }
  if (params.manuDate!=null||params.manuDate!=undefined) {
    params.manuStartDate = params.manuDate[0] + " 00:00:00"
    params.manuEndDate = params.manuDate[1] + " 23:59:59"
    delete params.manuDate
  }
  return await request.get({ url: '/wms/inorderitem/page', params })
}

/**
  * Author： 许祎婷
  * Time： 2024/05/21
  * Description：撤销
*/
export const abandonApi = async (typeCodes: string) => {
    return request.post({ url: '/wms/inrditem/abandon/'+ typeCodes })
}
// 获取下拉框树形数据
export const getSelectListApi = async (typeCodes: string) => {
  return await request.get({ url: "/common/type/getTypeCodes?typeCodes=" + typeCodes })
}

