import request from '@/config/axios'
import { getRefreshToken } from '@/utils/auth'
import type { UserLoginVO } from './types'

export interface CodeImgResult {
  captchaOnOff: boolean
  img: string
  uuid: string
}
export interface SmsCodeVO {
  mobile: string
  scene: number
}
export interface SmsLoginVO {
  mobile: string
  code: string
}
export interface resetPasswordVO {
  mobile: number
  code: number
  password: string
  passwordCheck: string
}

// 登录
export const loginApi = (data: UserLoginVO) => {
  return request.post({ url: '/system/auth/login', data })
}


// 登录获取所属org
export const getLoginOrgApi = (id: number) => {
  return request.get({ url: '/system/user/getOrg?id=' + id })
}


// 刷新访问令牌
export const refreshToken = () => {
  return request.post({ url: '/system/auth/refresh-token?refreshToken=' + getRefreshToken() })
}

// 使用租户名，获得租户编号
export const getTenantIdByNameApi = (name: string) => {
  return request.get({ url: '/system/tenant/get-id-by-name?name=' + name })
}

// 登出
export const loginOutApi = () => {
  return request.post({ url: '/system/auth/logout' })
}

// 获取用户权限信息
export const getPermissionInfoApi = () => {
  return request.get({ url: '/system/auth/get-permission-info' })
}

// 路由
export const getAsyncRoutesApi = () => {
  return request.get({ url: '/system/auth/list-menus' })
}

//获取登录验证码
export const sendSmsCodeApi = (data: SmsCodeVO) => {
  return request.post({ url: '/system/auth/send-sms-code', data })
}

// 短信验证码登录
export const smsLoginApi = (data: SmsLoginVO) => {
  return request.post({ url: '/system/auth/sms-login', data })
}

// 校验验证码
export const checkCaptchaApi = (data: resetPasswordVO) => {
  return request.post({ url: '/user/checkCaptcha', data })
}

// 重置密码
export const resetPasswordApi = (data: resetPasswordVO) => {
  return request.post({ url: '/user/reset-password', data })
}

// 社交授权的跳转
export const socialAuthRedirectApi = (type: number, redirectUri: string) => {
  return request.get({
    url: '/system/auth/social-auth-redirect?type=' + type + '&redirectUri=' + redirectUri
  })
}
// 获取验证图片  以及token
export const getCodePicApi = (data) => {
  return request.postOriginal({ url: 'system/captcha/get', data })
}

// 滑动或者点选验证
export const reqCheckApi = (data) => {
  return request.postOriginal({ url: 'system/captcha/check', data })
}

// 查询用户角色信息
export const getUserApi = (id: number) => {
  return request.get({ url: 'system/permission/list-user-roles?userId=' + id })
}
// ========== OAUTH 2.0 相关 ==========

export const getAuthorize = (clientId) => {
  return request.get({ url: '/system/oauth2/authorize?clientId=' + clientId})
}

export const authorize = (responseType, clientId, redirectUri, state,
                          autoApprove, checkedScopes, uncheckedScopes) => {
  // 构建 scopes
  const scopes = {};
  for (const scope of checkedScopes) {
    scopes[scope] = true;
  }
  for (const scope of uncheckedScopes) {
    scopes[scope] = false;
  }
  let data = {
      response_type: responseType,
      client_id: clientId,
      redirect_uri: redirectUri,
      state: state,
      auto_approve: autoApprove,
      scope: JSON.stringify(scopes)
    }
  // 发起请求
  return request.post({
    url: '/system/oauth2/authorize',
    params: data,
    headers: {
      'Content-type': 'application/x-www-form-urlencoded'
    }
  })
};


export class socialBindLogin {
}
