// 查询租户列表
import request from "@/config/axios";

export interface OrderPageReqVO extends PageParam {
  // orgId: number
  // orgCategory: number
  wmsOrderId: number
  entityCode: string | null;
  objectId: string | null;
}

export interface releaseVO {
  id: number
}

export interface OrderContentVO {
  id:number
  entityCode:string
  objectId:number
  objectItemId:number
  wmsOrderId:string
  partVersionId:number
  actualQuantity:string
  partQuanlityId: string
}

//查询入库申请单列表数据
export const getStorageApplicationFormPageApi = (params: OrderPageReqVO) => {
  if(window.location.pathname == '/purchaseProject/saleOrder/purchasenav'){
    //采购订单
    params.objectId = sessionStorage.getItem('purchaseOrderId')
    params.entityCode = 'p_inquiry_document_code'
  } else {
    params.objectId = null
    params.entityCode = null
    params.wmsOrderId = Number(sessionStorage.getItem('OrderInId'))
  }
  return request.get({ url: '/project/wms-order-item/page', params })
}

// //查询列表数据
// export const getOrderPageApi = (params: OrderPageReqVO) => {
//   params.orgId = 1
//   params.orgCategory = 1
//   return request.get({ url: '/purchaseOrder/stuff-item/purchasepage', params })
// }


// 查询入库申请单详情
export const getStorageApplicationFormApi = async (id: number) => {
  return await request.get({ url: '/project/wms-order-item/get?id=' + id })
}

// 入库申请单信息新增
export const createStorageApplicationFormApi = (data: OrderContentVO) => {
  return request.post({ url: '/project/wms-order-item/create', data })
}

// 入库申请单信息修改
export const updateStorageApplicationFormApi = (data: OrderContentVO) => {
  return request.put({ url: '/project/wms-order-item/update', data })
}

//入库申请单单条删除
export const delStorageApplicationFormApi = async (id: number) => {
  return await request.delete({ url: '/project/wms-order-item/delete?id=' + id })
}

// 入库申请单批量删除
export const delStorageApplicationFormListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/project/wms-order-item/delete-batch?ids=' + ids })
}
