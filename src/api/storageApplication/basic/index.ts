import request from '@/config/axios'

export interface OrderContentVO {
  orgId:number
  id:number
  orderDesc:string
  actualDate:Date[]
  orderCategory: string
}

// // 查询入库申请单详情（单条）
export const getStorageApplicationFormApi = async (id: number) => {
  return await request.get({ url: '/project/wms-order/get?id=' + id })
}

// 入库申请单信息新增
export const createStorageApplicationFormApi = (data: OrderContentVO) => {
  return request.post({ url: '/project/wms-order/create', data })
}

// 入库申请单信息修改
export const updateStorageApplicationFormApi = (data: OrderContentVO) => {
  return request.put({ url: '/project/wms-order/update', data })
}

// 获取下拉框数据
export const getSelectApi = async (typeCodes: string) => {
  return await request.get({ url: "/common/type/getMapTypeCodes?typeCodes=" + typeCodes })
}
//获取负责人下拉框数据
export const getUserApi = async () => {
  return await request.get({ url: '/system/user/list'})
}

// 查询部门列表
export const listSimpleDeptApi = async () => {
  return await request.get({ url: '/system/dept/list' })
}

// 获取客户工厂信息
export const getOrgFactoryMsg = async (orgId: number) => {
  return await request.get({ url: '/common/factory/list?orgId=' +  orgId })
}

//获取客户下拉框数据
export const getOrgSelectApi = async () => {
  return await request.get({ url: '/common/org/list?orgCategory=0'})
}
