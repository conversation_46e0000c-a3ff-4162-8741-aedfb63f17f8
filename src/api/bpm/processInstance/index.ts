import request from '@/config/axios'

export type Task = {
  id: string
  name: string
}
export type ProcessInstanceVO = {
  id: number
  name: string
  processDefinitionId: string
  category: string
  result: number
  tasks: Task[]
  fields: string[]
  status: number
  remark: string
  businessKey: string
  createTime: string
  endTime: string
}

export const getMyProcessInstancePageApi = async (params) => {
  if (params.createTime!=null||params.createTime!=undefined){
    params.createTime[0]=params.createTime[0]+" 00:00:00"
    params.createTime[1] =params.createTime[1]+" 23:59:59"
  }
  return await request.get({ url: '/bpm/process-instance/my-page', params })
}

export const createProcessInstanceApi = async (data) => {
  return await request.post({ url: '/bpm/process-instance/create', data: data })
}

export const cancelProcessInstanceApi = async (id: number, reason: string) => {
  const data = {
    id: id,
    reason: reason
  }
  return await request.delete({ url: '/bpm/process-instance/cancel', data: data })
}

export const getProcessInstanceApi = async (id: number) => {
  return await request.get({ url: '/bpm/process-instance/get?id=' + id })
}


export const createInstanceApi = async (data) => {
  return await request.post({ url: '/bpm/myTask/create',  data: data })
}

//问题处理提交审批
export const createChallengeApi = async (data) => {
  return await request.post({ url: '/bpm/myChallenge/create',  data: data })
}

//重新分配
export const updateTaskAssignee = async (data) => {
  return await request.put({ url: '/bpm/myChallenge/update-assignee', data })
}
