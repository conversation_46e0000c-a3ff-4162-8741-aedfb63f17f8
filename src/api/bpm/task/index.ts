import request from '@/config/axios'

export const getTodoTaskPage = async (params) => {
  if (params.createTime!=null||params.createTime!=undefined){
    params.createTime[0]=params.createTime[0]+" 00:00:00"
    params.createTime[1] =params.createTime[1]+" 23:59:59"
  }
  return await request.get({ url: '/bpm/task/todo-page', params })
}

export const getDoneTaskPage = async (params) => {
  if (params.createTime!=null||params.createTime!=undefined){
    params.createTime[0]=params.createTime[0]+" 00:00:00"
    params.createTime[1] =params.createTime[1]+" 23:59:59"
  }
  return await request.get({ url: '/bpm/task/done-page', params })
}

export const completeTask = async (data) => {
  return await request.put({ url: '/bpm/task/complete', data })
}

export const approveTask = async (data) => {
  return await request.put({ url: '/bpm/task/approve', data })
}

export const approveChallenge = async (data) => {
  return await request.post({ url: '/bpm/myChallenge/approve', data })
}

export const approveReportWork = async (data) => {
  return await request.post({ url: '/bpm/reportWork/approve', data })
}

export const rejectChallenge = async (data) => {
  return await request.post({ url: '/bpm/myChallenge/reject', data })
}

export const rejectReportWork = async (data) => {
  return await request.post({ url: '/bpm/reportWork/reject', data })
}

export const rejectTask = async (data) => {
  return await request.put({ url: '/bpm/task/reject', data })
}
export const backTask = async (data) => {
  return await request.put({ url: '/bpm/task/back', data })
}

export const updateTaskAssignee = async (data) => {
  return await request.put({ url: '/bpm/task/update-assignee', data })
}

export const getTaskListByProcessInstanceId = async (processInstanceId) => {
  return await request.get({
    url: '/bpm/task/list-by-process-instance-id?processInstanceId=' + processInstanceId
  })
}
