import request from '@/config/axios'

export interface ClassifyVO {
  parentTypeId: number
  typeSort: number
  typeName:string
  typeCode:string
  typeSuffix:string
  typePrefix:string
  typeStartNumber:number
  typeNumberLength:number
}

export interface ClassifyPageReqVO extends PageParam {
  parentTypeId?: number
}
// 分类信息保存
export const createTypeApi = (data: ClassifyVO) => {
  return request.post({ url: '/common/type/create', data })
}

 // 修改分类信息
export const updateTypeApi = (data: ClassifyVO) => {
  return request.put({ url: '/common/type/update', data })
}
 // 删除分类信息
export const deleteTypeApi = (id: number) => {
  return request.delete({ url: '/common/type/delete?id=' + id })
}
// 单条查询分类信息
export const getTypeApi = (id: number) => {
  return request.get({ url: '/common/type/get?id=' + id })
}

//查询分类列表信息
export const getTypeListApi = (params: ClassifyVO) => {
  params.parentTypeId = 0
  return request.get({ url: '/common/type/page', params })
}

//查询分类列表信息
export const getSonTypeListApi = async (params: ClassifyVO) => {
  params.typeCode = "'" + sessionStorage.getItem('typeCodes') + "'"
  // params.typeName = "'" + params.typeName + "'"
  return await request.get({ url: '/common/type/getChildrenTypeCodes', params })
}


//物料组关联单位
export const partLinkApi = (data) => {
  return request.post({ url: '/common/entity-link/create', data })
}

//物料组关联单位
export const updatePartLinkApi = (data) => {
  return request.post({ url: '/common/entity-link/update', data })
}


//点击修改时获取单位信息
export const getpartLinkApi = (id) => {
  const linkType = "'g_type_part_version_group_base_entity_link','g_type_part_version_group_entity_link'"
  return request.get({ url: '/common/entity-link/list?linkType='+ linkType +'&primaryObjectId=' + id })
}
