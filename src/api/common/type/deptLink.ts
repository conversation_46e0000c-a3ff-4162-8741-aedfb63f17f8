import request from '@/config/axios'

export interface DeptVO {
  deptId: number
  typeId: number
  isEstimate:number
  isActual:number
  isTop:number
}
/**
  * Author： 许祎婷
  * Time： 2024/10/22
  * Description：新增关联
*/
export const createLinkApi = (data: DeptVO) => {
  return request.post({ url: '/cost/dept-type-link/create', data })
}

 // 修改分类信息
/**
  * Author： 许祎婷
  * Time： 2024/10/22
  * Description：修改关联信息
*/
export const updateLinkApi = (data: DeptVO) => {
  return request.put({ url: '/cost/dept-type-link/update', data })
}
/**
  * Author： 许祎婷
  * Time： 2024/10/22
  * Description：删除关联信息
*/
export const deleteLinkApi = (id: number) => {
  return request.delete({ url: '/cost/dept-type-link/delete?id=' + id })
}

/**
  * Author： 许祎婷
  * Time： 2024/10/22
  * Description：查询关联详情
 */
export const getLinkApi = (id: number) => {
  return request.get({ url: '/cost/dept-type-link/get?id=' + id })
}

/**
  * Author： 许祎婷
  * Time： 2024/10/22
  * Description：查询关联列表
*/
export const getLinkListApi = (params: DeptVO) => {
  return request.get({ url: '/cost/dept-type-link/page', params })
}

//点击修改时获取单位信息
export const getpartLinkApi = (id) => {
  const linkType = "'g_type_part_version_group_base_entity_link','g_type_part_version_group_entity_link'"
  return request.get({ url: '/common/entity-link/list?linkType='+ linkType +'&primaryObjectId=' + id })
}
