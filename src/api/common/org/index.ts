import request from '@/config/axios'

export interface ConnectionVO {
  id:number
  orgCode: string
  orgDuns: string
  orgName:string
  orgEnglishName:string
  orgNameAbb:string
  orgType:number
  orgCategory:number
  orgCharacter:string
  orgRank:string
  orgCountry:string
  orgProvince:string
  orgCity:string
  orgAddress:string
  orgZipcode:string
  orgEmail:string
  orgTelephone:string
  orgPv:number
  orgProductQuantityRan:string
  orgTradePartner:string
  orgCustomer:string
  orgProductCert:string
  orgBsci:string
  orgChargePersonId:number
  orgDesc:string
  orgStatus:number
  factoryInspectionScore:number
  mainProductionEquipment:string
  outProcessDescription:string
  laboratorySituation:string
  sapCode:string
  legalPerson:string
}

export interface ConnectionPageReqVO extends PageParam {
  orgCategory: number,
  partVersionId: string | null;
  partId:number
}

export interface PartConnectionVO{
  ppolType:number
  partId:number
  orgId:number
  orgPartCode:string
  orgPartName:string
  partVersionId: number
}

export interface SelectListVO{
  orgCategory: number
}
export interface ConnectionStatusVO{
  ids: string
  handleType: number
}
// 客户信息保存
export const createCustomerApi = (data: ConnectionVO) => {
  data.orgCategory=0
  return request.post({ url: '/common/org/create', data })
}

// 本公司信息保存
export const createCompanyApi = (data: ConnectionVO) => {
  data.orgCategory = 2
  return request.post({ url: '/common/org/create', data })
}

 // 修改客户信息
export const updateCustomerApi = (data: ConnectionVO) => {
  return request.put({ url: '/common/org/update', data })
}
 // 删除客户信息
export const deleteCustomerApi = (id: number) => {
  return request.delete({ url: '/common/org/delete?id=' + id })
}
// 单条查询客户信息
export const getCustomerApi = (id: number) => {
  return request.get({ url: '/common/org/get?id=' + id })
}

//查询客户列表信息
export const getCustomerPageApi = (params: ConnectionPageReqVO) => {
  params.orgCategory = 0
  return request.get({ url: '/common/org/page', params })
}

//查询客户列表信息
export const getCompanyPageApi = (params: ConnectionPageReqVO) => {
  params.orgCategory = 2
  return request.get({ url: '/common/org/page', params })
}

// 批量删除
export const delCustomerListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/common/org/delete-batch?ids=' + ids })
}
//查询供应商下拉框数据
export const getCustomerSelectListApi = (orgCategory:number) => {
  return request.get({ url: '/common/org/list?orgCategory=' + orgCategory})
}

//查询客户下拉框数据
export const getClientSelectListApi = (orgCategory:number) => {
  return request.get({ url: '/common/org/list?orgCategory=' + orgCategory})
}
//物料供应商列表查询接口
export const getPartCustomerPageApi = (params: ConnectionPageReqVO) => {
  params.orgCategory = 1
  let str  = window.location.search.split('partId=')[1]
  if(str){
    params.partId = parseInt(str)
  }
  // params.partId=sessionStorage.getItem("partId")
  params.partVersionId = sessionStorage.getItem(`partVersionId`)
  return request.get({ url: '/common/org/partOrgPage', params })
}
//物料供应商列表查询接口
export const getPartClientPageApi = (params: ConnectionPageReqVO) => {
  params.orgCategory = 0
  params.partId=parseInt(<string>sessionStorage.getItem("partId"))
  params.partVersionId = sessionStorage.getItem(`partVersionId`)
  return request.get({ url: '/common/org/partOrgPage', params })
}
//物料供应商列表新增接口
export const createPartCustomerApi = (data: PartConnectionVO) => {
  return request.post({ url: '/common/part-org-link/create', data })
}

//物料供应商列表单条查询接口
export const getPartClientApi = (id: number) => {
  return request.get({ url: '/common/part-org-link/get?id=' + id })
}

// 修改物料供应商信息
export const updatePartCustomerApi = (data: PartConnectionVO) => {
  return request.put({ url: '/common/part-org-link/update', data })
}
// 删除物料供应商列表信息
export const deletePartCustomerApi = (id: number) => {
  return request.delete({ url: '/common/part-org-link/delete?id=' + id })
}
// 批量删除物料供应商列表信息
export const delPartCustomerListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/common/part-org-link/delete-batch?ids=' + ids })
}
// 冻结/解冻供应商列表信息
export const updateOrgStatusApi = (params:ConnectionStatusVO) => {
  // data.ids = data.ids.toString()
  return request.put({ url: '/common/org/updateOrgStatus' ,params})
}
// 查询客户
export const getOrgApi = async () => {
  return await request.get({ url: '/common/org/list?orgCategory=0'})
}
//售后管理查询接口
export const getAfterSalesPageApi = (params: ConnectionPageReqVO) => {
  params.pageSize = 12
  return request.get({ url: '/common/org/detailPage', params })
}
