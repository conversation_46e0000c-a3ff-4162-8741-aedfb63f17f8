import request from '@/config/axios'

export interface FactoryVO {
  id:number
  orgId:number
  factoryName: string
  factoryDesc:string
  factoryCode:string
  factoryAddress:string
  factoryZipcode:string
  factoryTelphone:string

}

export interface FactoryPageReqVO extends PageParam {
  orgId:number
}
// 客户工厂信息保存
export const createCustomerFactoryApi = (data: FactoryVO) => {
  return request.post({ url: '/common/factory/create', data })
}

// 修改客户工厂信息
export const updateCustomerFactoryApi = (data: FactoryVO) => {
  return request.put({ url: '/common/factory/update', data })
}
// 删除客户工厂信息
export const deleteCustomerFactoryApi = (id: number) => {
  return request.delete({ url: '/common/factory/delete?id=' + id })
}
// 单条查询客户工厂信息
export const getOneCustomerFactoryApi = (id: number) => {
  return request.get({ url: '/common/factory/get?id=' + id })
}

//查询客户工厂列表信息
export const getCustomerFactoryPageApi = (params: FactoryPageReqVO) => {
  // @ts-ignore
  params.orgId=parseInt(sessionStorage.getItem("CustomerId"))
  return request.get({ url: '/common/factory/page',   params })
}

// 批量删除
export const delCustomerFactoryListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/common/factory/delete-batch?ids=' + ids })
}
