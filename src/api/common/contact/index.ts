import request from '@/config/axios'

export interface ContactVO {
  orgId:number
  userId: number
  contactName: string
  contactStatus:number
  contactTelphone:string
  contactJob:string
  contactQq:string
  contactWechat:string
  contactEmail:string
  contactDesc:string
}

export interface ContactPageReqVO extends PageParam {
  userId: number
  orgId:number
}
// 联系人信息保存
export const createContactApi = (data: ContactVO) => {
  return request.post({ url: '/common/contact/create', data })
}

// 修改联系人信息
export const updateContactApi = (data: ContactVO) => {
  return request.put({ url: '/common/contact/update', data })
}
// 删除联系人信息
export const deleteContactApi = (id: number) => {
  return request.delete({ url: '/common/contact/delete?id=' + id })
}
// 单条查询联系人信息
export const getContactApi = (id: number) => {
  return request.get({ url: '/common/contact/get?id=' + id })
}

//查询联系人列表信息
export const getContactPageApi = (params: ContactPageReqVO) => {
  // params.orgType = 0
  params.orgId=parseInt(sessionStorage.getItem("CustomerId"))
  return request.get({ url: '/common/contact/page', params })
}
//查询供应商联系人列表信息
export const getSupplierContactPageApi = (params: { pageNo: number; pageSize: number; orgId: number }) => {
  // params.orgType = 0
  params.orgId=parseInt(sessionStorage.getItem("supplierId"))
  return request.get({ url: '/common/contact/page', params })
}
// 批量删除联系人
export const delContactListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/common/contact/delete-batch?ids=' + ids })
}

// 授权登录
export const getAuthorizationApi = (id: number) => {
  return request.get({ url: '/common/contact/authorization?id=' + id })
}
// 取消授权登录
export const getAuthorizationDeleteApi = (id: number) => {
  return request.get({ url: '/common/contact/authorization-delete?id=' + id })
}
