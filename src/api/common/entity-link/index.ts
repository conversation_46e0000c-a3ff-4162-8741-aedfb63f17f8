import request from '@/config/axios'


export interface EntityLinkVO {
  primaryObjectId: number
  secondaryObjectId: number
  linkType: string
  partCategory: number
}

export interface ContactPageReqVO extends PageParam {
  userId: number
  orgId:number
}
// 实体关系保存
export const createEntityLinkApi = (data: EntityLinkVO) => {
  return request.post({ url: '/common/entity-link/create', data })
}

// 任务与物料关系查询
export const getTasksMaterialsLinkPageApi = (params: EntityLinkVO) => {
  params.linkType = 't_task_design_part_version_link'
  params.primaryObjectId = parseInt(sessionStorage.getItem("taskDesignId"))
  params.partCategory = 1
  return request.get({ url: '/common/entity-link/page', params })
}
// 任务与模具关系查询
export const getTasksDiesLinkPageApi= (params: EntityLinkVO) => {
  params.linkType = 'p_mold_task_design_link'
  params.primaryObjectId = parseInt(sessionStorage.getItem("taskDesignId"))
  params.partCategory = 3
  return request.get({ url: '/common/entity-link/page', params })
}
// 批量删除中间表
export const delLinkListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/common/entity-link/delete-batch?ids=' + ids })
}
// 报工问题关联
export const workReportQuestionApi = (data: EntityLinkVO) => {
  return request.post({ url: '/common/entity-link/workReport-question', data })
}
