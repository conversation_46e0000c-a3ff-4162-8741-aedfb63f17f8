import request from '@/config/axios'

// 获取下拉框数据
export const getSelectApi = async (typeCodes: string) => {
  return await request.get({ url: "/common/type/getMapTypeCodes?typeCodes=" + typeCodes })
}

export interface SaleClaimPageReqVO extends PageParam {
  objectId:number
  projectId:number
}
export interface ClaimPageReqVO extends PageParam {
  claimCode:string
  claimType:number
  orgCode:string
  orgName:string
}
export interface ClaimVO {
  id:number
  claimCode:string
  claimType:number
  claimDesc:string
  orgId:number
  orderId:number
  clamimStatus:number
  claimRemark:string
}
//获取销售管理的索赔列表信息
export const getSaleClaimPageApi = (params: SaleClaimPageReqVO) => {
  // params.orgType = 0
  params.projectId = Number(sessionStorage.getItem('marketId'))

  return request.get({ url: '/p/claim/page-project', params })
}

//获取索赔管理的列表
export const getClaimPageApi = (params: ClaimPageReqVO) => {
  // params.orgType = 0
  return request.get({ url: '/p/claim/page', params })
}

// 查询索赔记录详情
export const getClaimApi = async (id: number) => {
  return await request.get({ url: '/p/claim/get?id=' + id })
}

// 修改索赔记录
export const updateClaimApi = async (data: ClaimVO) => {
  return await request.put({ url: '/p/claim/update', data })
}
// 新增索赔记录
export const createClaimApi = async (data: ClaimVO) => {
  return await request.post({ url: '/p/claim/create', data })
}

//新增索赔记录单条删除
export const delClaimApi = async (id: number) => {
  return await request.delete({ url: '/p/claim/delete?id=' + id })
}

// 新增索赔记录批量删除
export const delClaimListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/p/claim/delete-batch?ids=' + ids })
}
