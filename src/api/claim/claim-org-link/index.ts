import request from '@/config/axios'
export interface ClaimOrgPageReqVO extends PageParam {
  claimId:number
}
export interface ClaimOrgVO {
  claimId:number
  claimDesc:string
  orgId:number

}
//获取索赔关联供应商信息
export const getClaimOrgLinkPageApi = (params: ClaimOrgPageReqVO) => {
  // params.orgType = 0
  params.claimId=sessionStorage.getItem("claimId")
  return request.get({ url: '/p/claim-org-link/page', params })
}



// // 修改索赔记录
// export const updateClaimApi = async (data: ClaimVO) => {
//   return await request.put({ url: '/claim/claim/update', data })
// }
// 新增索赔供应商记录
export const createClaimOrgLinkApi = async (data: ClaimOrgVO) => {
  return await request.post({ url: '/p/claim-org-link/create', data })
}

//索赔供应商记录单条删除
export const delClaimOrgLinkApi = async (id: number) => {
  return await request.delete({ url: '/p/claim-org-link/delete?id=' + id })
}

// // 新增索赔记录批量删除
export const delClaimOrgLinkListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/p/claim-org-link/delete-batch?ids=' + ids })
}
