import request from '@/config/axios'

/**
  * Author： 许祎婷
  * Time： 2025/03/02
  * Description：报表
*/
export const getSheetData = async (params) => {
  console.log(params,'params')
  if(params) {
    //判断一下日期框条件
    if (params.createTime !== null && params.createTime !== undefined) {
      const planType = typeof params.createTime
      if (planType === 'string') {
        //路由查询，转换格式
        params.createTime = params.createTime.split(',')
      }
      params.createTimeStart = params.createTime[0] + ' 00:00:00'
      params.createTimeEnd = params.createTime[1] + ' 23:59:59'
      // delete params.createTime
    }
    if (params.planTime !== null && params.planTime !== undefined) {
      const planType = typeof params.planTime
      if (planType === 'string') {
        //路由查询，转换格式
        params.planTime = params.planTime.split(',')
      }
      params.planStartTime = params.planTime[0] + ' 00:00:00'
      params.planEndTime = params.planTime[1] + ' 23:59:59'
      delete params.planTime
    }
    if (params.actualTime !== null && params.actualTime !== undefined) {
      const actualTime = typeof params.actualTime
      if (actualTime === 'string') {
        //路由查询，转换格式
        params.actualTime = params.actualTime.split(',')
      }
      params.actualStartTime = params.actualTime[0] + ' 00:00:00'
      params.actualEndTime = params.actualTime[1] + ' 23:59:59'
      delete params.actualTime
    }
    params.isTemplate = 2
  }
  return await request.get({ url: '/cost/info-details/luckysheet',params})
}

/**
  * Author： 许祎婷
  * Time： 2025/03/04
  * Description：查询部门
*/
export const listSimpleDeptsApi = async (searchStatus:number) => {
  return await request.get({ url: '/system/dept/list?searchStatus='+ searchStatus })
}
