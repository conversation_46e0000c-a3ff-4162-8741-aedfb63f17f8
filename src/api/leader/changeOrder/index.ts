import request from '@/config/axios'

export interface ChangeOrderVO {
  id: number
  changeOrderCode: string
  entityCode: string
  objectId: number
  changeUser: number
  processInstanceId: string
  processStatus: number
}

export interface ChangeOrderPageReqVO extends PageParam {
  changeOrderCode?: string
  entityCode?: string
  objectId?: number
  changeUser?: number
  processInstanceId?: string
  topTypeCode?: string
  processStatus?: number
  createTime?: Date
  effectiveTime?: Date
  createTimeStart?: Date
  createTimeEnd?: Date
}

export interface ChangeOrderExcelReqVO {
  changeOrderCode?: string
  entityCode?: string
  objectId?: number
  changeUser?: number
  processInstanceId?: string
  processStatus?: number
  createTime?: Date[]
}

// 查询变更列表
export const getChangeOrderPageApi = async (params: ChangeOrderPageReqVO) => {
  if (params.effectiveTime!=null||params.effectiveTime!=undefined) {
    params.createTimeStart = params.effectiveTime[0] + " 00:00:00"
    params.createTimeEnd = params.effectiveTime[1] + " 23:59:59"
    delete params.effectiveTime
  }
  //顶级类型
  params.topTypeCode = "'project_issues'"
  return await request.get({ url: '/cm/change-order/page', params })
}

// 查询变更详情
export const getChangeOrderApi = async (id: number) => {
  return await request.get({ url: '/cm/change-order/get?id=' + id })
}

// 新增变更
export const createChangeOrderApi = async (data: ChangeOrderVO) => {
  return await request.post({ url: '/cm/change-order/create', data })
}

// 修改变更
export const updateChangeOrderApi = async (data: ChangeOrderVO) => {
  return await request.put({ url: '/cm/change-order/update', data })
}

// 删除变更
export const deleteChangeOrderApi = async (id: number) => {
  return await request.delete({ url: '/cm/change-order/delete?id=' + id })
}

// 导出变更 Excel
export const exportChangeOrderApi = async (params: ChangeOrderExcelReqVO) => {
  return await request.download({ url: '/cm/change-order/export-excel', params })
}
