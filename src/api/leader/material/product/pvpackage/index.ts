import request from '@/config/axios'
export interface Brand {
  partVersionId: number
  innerBoxQuantity: number
  innerBoxEan: string
  middleBoxQuantity: number
  middleBoxEan: string
  outBoxQuantity: number
  outBoxEan: string
  outBoxLength: number
  outBoxWidth: number
  outBoxHeight: number
  outBoxVolume: number
  lengthUnit: string
  weightUnit: string
  outBoxNetWeight: number
  outBoxGrossWeight: number
  createTime: Date[]
}
// 新增包装信息
export const createPackageApi = async (data: Brand) => {
  return await request.post({ url: 'part/pvpackage/create', data })
}

//查询包装信息
export const getPackageApi = (id: number) => {
  return request.get({ url: '/part/pvpackage/get?partVersionId=' + id })
}

//查询包装信息页面下拉框数据
export const getPackageSeletListApi = (typeCodes: string) => {
  return request.get({ url: '/common/type/getMapTypeCodes?typeCodes=' + typeCodes })
}

// 包装信息修改
export const updatePackageApi = (data: Brand) => {
  return request.put({ url: '/part/pvpackage/update', data })
}
