import request from '@/config/axios'

export interface BrandVO {
  id: number
  partVersionld: number
  upDownStatus: boolean
  cycleCheckDays: number
  knowledgeldentify: string
  brandVersion: string
  productIdentify: number
  rvIdentify: number
  mrpType: number
  subPartType: number
  upTime: string
  downTime: string
  reorderPoint: string
  safetyQuantity: number
  fclQuantity: number
  economicOrderQuantity: number
  minimumOrderQuantity: number
  versionChangeRecor: string
  createTime: string
}

export interface TenantPageReqVO extends PageParam {
  name?: string
  contactName?: string
  contactMobile?: string
  status?: number
  createTime?: Date[]
}

export interface TenantExportReqVO {
  name?: string
  contactName?: string
  contactMobile?: string
  status?: number
  createTime?: Date[]
}

// 品牌信息保存
export const createBrandApi = (data: BrandVO) => {
  return request.post({ url: '/part/brand/create', data })
}
//查询品牌信息
export const getBrandApi = (id: number) => {
  return request.get({ url: '/part/brand/get?partVersionId=' + id })
}
//查询品牌信息页面下拉框数据
export const getBrandSelectListApi = (typeCodes: string) => {
  return request.get({ url: '/common/type/getMapTypeCodes?typeCodes=' + typeCodes })
}
// 修改品牌信息
export const updateBrandApi = (data: BrandVO) => {
  return request.put({ url: '/part/brand/update', data })
}


