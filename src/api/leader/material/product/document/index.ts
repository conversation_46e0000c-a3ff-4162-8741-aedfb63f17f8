import request from '@/config/axios'
export interface Document {
  partVersionId: number
    pageNo: number
    pageSize:number
    entityCode:string
    objectId:number
    documentVersion:number
}
export interface purchase {
  orgId: number
  orderCategory: number
  pageNo: number
  pageSize:number
}
export interface DocumentVO{
  id:number
  documentId:number
  documentVersionName:string
  documentDesc:string
  documentStatus:number
  documentVersion:any
  documentTypeId:number
  documentRemark:string
  entityCode:String
  objectId:number
}

export interface releaseVO {
  id: number
}


// 查询图文档详情
export const getDocumentApi = async (params: Document) => {
  // @ts-ignore
  params.partVersionId = sessionStorage.getItem(`partVersionId`)
  return await request.get({ url: '/document/version/page', params})
}
// 查询任务图文档列表
export const getTaskDocumentApi = async (params: Document) => {
  // @ts-ignore
  params.entityCode = 'T_TASK_DESIGN_DOCUMENT_LINK'
  params.objectId = parseInt(<string>sessionStorage.getItem(`taskDesignId`))
  if (params.documentVersion === 'WIP') {
    params.documentVersion = 0
  }
  return await request.get({ url: '/document/version/page', params})
}

// 查询物料图文档列表
export const getPartDocumentApi = async (params: Document) => {
  // @ts-ignore
  params.partVersionId = sessionStorage.getItem(`partVersionId`)
  // @ts-ignore
  params.objectId = sessionStorage.getItem(`partVersionId`)
  params.entityCode = 'part_version_link'
  if (params.documentVersion === 'WIP') {
    params.documentVersion = 0
  }
  return await request.get({ url: '/document/version/page', params})
}

// 查询项目CAD文档列表
export const getProjectDocumentApi = async (params: Document) => {
  // @ts-ignore
  // params.partVersionId = sessionStorage.getItem(`partVersionId`)
  // @ts-ignore
  params.objectId = sessionStorage.getItem(`marketId`)
  params.entityCode = 'p_project_document_link'
  if (params.documentVersion === 'WIP') {
    params.documentVersion = 0
  }
  return await request.get({ url: '/document/version/page', params})
}


// 查询图文档列表
export const getDocumentListApi = async (params: Document) => {
  if (params.documentVersion === 'WIP') {
    params.documentVersion = 0
  }
  return await request.get({ url: '/document/version/documentPage', params})
}
export const createPartDocumentLinkApi = async (data: DocumentVO) => {
  return await request.post({ url: '/document/version/createDocumentLink', data })
}
// 查询图文档附件列表
export const getDocumentFileListApi = async (data: Document) => {
  // if (params.documentVersion === 'WIP') {
  //   params.documentVersion = 0
  // }
  // return await request.post({ url: '/document/version/page-file', data})
  return request.post({ url: '/document/version/page-file', data }).then((res)=>{
    const list = res.list
    let updateUrl = import.meta.env.VITE_BASE_URL?import.meta.env.VITE_BASE_URL+import.meta.env.VITE_SERVICE_PORT+import.meta.env.VITE_API_URL:'http://'+window.location.hostname+import.meta.env.VITE_SERVICE_PORT+import.meta.env.VITE_API_URL
    if(list){
      for(let i = 0; i<list.length;i++){
        list[i].fileUrl=updateUrl+list[i].fileUrl;
        console.log(list)
      }
    }
    return res
  })
}

// 新增图文档
export const createDocumentApi = async (data: DocumentVO) => {
  return await request.post({ url: '/document/version/create', data })
}
// 新增物料文档
export const createPartDocumentApi = async (data: DocumentVO) => {
  return await request.post({ url: '/document/version/createDocument', data })
}
// 修改图文档信息
export const updateDocumentApi = async (data: DocumentVO) => {
  return await request.put({ url: '/document/version/update', data })
}
// 删除图文档信息
export const deleteDocumentApi = async (id: number) => {
  return await request.delete({ url: '/document/version/delete?id=' + id })
}
// 批量删除图文档
export const delDocumentListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/document/version/delete-batch?ids=' + ids })
}
// 删除中间表
export const deleteDocumentVersionLinkApi = async (id: number) => {
  return await request.delete({ url: '/document/entity-document-version-link/delete?id=' + id })
}

// 批量删除中间表
export const delDocumentVersionLinkApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/document/entity-document-version-link/delete-batch?ids=' + ids })
}
//获取单条数据
export const getDocumentOneApi = async (id: number) => {
  return await request.get({ url: '/document/version/get?id=' + id })
}
//创建新版本
export const createNewVersionApi = (data: DocumentVO) => {
  return request.post({ url: '/document/version/createNewVersion', data })
}

// 发布
export const releaseApi = async (params: releaseVO) => {
  return await request.post({ url: '/document/version/release' , params })
}

//升版
export const upgradeVersionApi = async (id: number) => {
return await request.get({ url: '/document/version/upgrade?id=' + id })
}

// 提交审批
export const checkDocumentApi = async (data) => {
  return await request.post({ url: '/bpm/document/create',  data: data })
}

//供应商采购信息记录列表查询
export const getSupplierPurchaseApi = async (params: purchase) => {
  params.orderCategory = 2
  params.orgId = sessionStorage.getItem(`supplierId`)
  return await request.get({ url: '/project/part-item/purchasepage', params})
}
