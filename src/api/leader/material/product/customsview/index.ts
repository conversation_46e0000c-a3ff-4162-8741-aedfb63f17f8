import request from '@/config/axios'

export interface CustomsVO {
  customsCode: string
  partld?: number
  measureUnit:string
  valueAddedTaxRate:number
  refundTaxRate:number
  customsChineseName:string
  customsEnglishName:string
  declareElementRefer:string
  declareElement:string
  supervisionCondition:string
  createTime:string
}

export interface Customs {
  pageNo: number
  pageSize:number
}

export interface TenantPageReqVO extends PageParam {
  name?: string
  contactName?: string
  contactMobile?: string
  status?: number
  createTime?: Date[]
}

export interface TenantExportReqVO {
  name?: string
  contactName?: string
  contactMobile?: string
  status?: number
  createTime?: Date[]
}

// 查询报关视图信息
export const getCustomsViewApi = (partId: number) => {
  let str  = window.location.search.split('partId=')[1]
  if(str){
    partId = parseInt(str)
  }
  return request.get({ url: '/part/customs-declaration/get?partId=' + partId })
}

//查询下拉框数据
export const getCustomsSelectListApi = (typeCodes: string) => {
  return request.get({ url: '/common/type/getMapTypeCodes?typeCodes=' + typeCodes })
}
// 报关视图信息新增
export const createCustomsViewApi = (data: CustomsVO) => {
  return request.post({ url: '/part/customs-declaration/create', data })
}
// 报关视图信息修改
export const updateCustomsViewApi = (data: CustomsVO) => {
  return request.put({ url: '/part/customs-declaration/update', data })
}

// 查询报关视图列表
export const getCustomsViewPageApi = async (params: Customs) => {
  return await request.get({ url: '/part/customs-declaration/page', params})
}


