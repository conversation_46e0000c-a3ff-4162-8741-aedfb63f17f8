import request from '@/config/axios'

export interface VersionVO {
  versionManageId: number
  partId: number
  goodsCode: string
  partVersionCode: string
  partVersionName: string
  sapno: string
  chineseName: string
  englishName: string
  chineseQualityDesc: string
  englishQualityDesc: string
  chinesePackMode: string
  englishPackMode: string
  businessPersonId: number
  goodsPersonId: number
  isOwnBrand: number
  isOemSample: number
  objectId: number
  entityCode: string
}

export interface VersionPageReqVO extends PageParam {
  versionManageId?: number
  partId?: number
  goodsCode?: string
  partVersionCode?: string
  partVersionName?: string
  sapno?: string
  chineseName?: string
  englishName?: string
  chineseQualityDesc?: string
  englishQualityDesc?: string
  chinesePackMode?: string
  englishPackMode?: string
  businessPersonId?: number
  goodsPersonId?: number
  isOwnBrand?: number
  isOemSample?: number
  createTime?: Date[]
}

export interface VersionExcelReqVO {
  versionManageId?: number
  partId?: number
  goodsCode?: string
  partVersionCode?: string
  partVersionName?: string
  sapno?: string
  chineseName?: string
  englishName?: string
  chineseQualityDesc?: string
  englishQualityDesc?: string
  chinesePackMode?: string
  englishPackMode?: string
  businessPersonId?: number
  goodsPersonId?: number
  isOwnBrand?: number
  isOemSample?: number
  createTime?: Date[]
}

// 查询物料版本列表
export const getPartVersionPageApi = async (params: VersionPageReqVO) => {
  return await request.get({ url: '/part/version/page', params })
}

// 查询物料版本详情
export const getPartVersionApi = async (id: number) => {
  return await request.get({ url: '/part/version/get?id=' + id })
}
//获取人员信息
export const getPersonApi = async (id: number) => {
  return await request.get({ url: '/system/user/get?id=' + id })
}

// 新增物料版本
export const createPartVersionApi = async (data: VersionVO) => {
  return await request.post({ url: '/part/version/create', data })
}

// 修改物料版本
export const updatePartVersionApi = async (data: VersionVO) => {
  return await request.put({ url: '/part/version/update', data })
}

// 删除物料版本
export const deletePartVersionApi = async (id: number) => {
  return await request.delete({ url: '/part/version/delete?id=' + id })
}

// 导出物料版本 Excel
export const exportPartVersionApi = async (params: VersionExcelReqVO) => {
  return await request.download({ url: '/part/version/export-excel', params })
}

// 获取下拉框数据
export const getSelectApi = async (typeCodes: string) => {
  return await request.get({ url: "/common/type/getMapTypeCodes?typeCodes=" + typeCodes })
}
// 获取下拉框树形数据
export const getSelectListApi = async (typeCodes: string) => {
  return await request.get({ url: "/common/type/getTypeCodes?typeCodes=" + typeCodes })
}

//获取负责人下拉框数据
export const getUserApi = async () => {
  return await request.get({ url: '/system/user/list'})
}
//获取部门下负责人下拉框数据
export const getDeptUserApi = async (deptId:number,searchStatus?:number) => {
  var str='';
  if(deptId!=null&&deptId!=undefined){
    str='deptId='+deptId
  }
  if(searchStatus!=null&&searchStatus!=undefined){
    if(str){
       str+='&'
    }
    str+='searchStatus='+searchStatus
  }
  // console.log(str!='',888)
  if(str!=''){
    str='?'+str
  }
  return await request.get({ url: '/system/user/list'+str})
}
//获取负责人下拉框数据
export const getChargePersonDeptUserApi = async (chargePersonDeptId:number,searchStatus?:number) => {
  var str='';
  if(chargePersonDeptId!=null&&chargePersonDeptId!=undefined){
    str='chargePersonDeptId='+chargePersonDeptId
  }
  if(searchStatus!=null&&searchStatus!=undefined){
    if(str){
      str+='&'
    }
    str+='searchStatus='+searchStatus
  }
  // console.log(str!='',888)
  if(str!=''){
    str='?'+str
  }
  return await request.get({ url: '/system/user/list'+str})
}

//获取轮次下拉框数据
export const getRoundNumberSelectApi = async (entityCode: String, objectId: string | null) => {
  return await request.get({url: '/project/rfq-round/get-round?entityCode=' + entityCode+ '&objectId=' + objectId})
}

//获取采购组下拉框数据
export const getDeptIdApi = async (type: number) => {
  return await request.get({url: '/system/dept/list?type=' + type})
}

//查询物料组的计量单位
export const getUnitListApi = async (id) => {
  const linkType = "'g_type_part_version_group_base_entity_link','g_type_part_version_group_entity_link'"
  return request.get({ url: '/common/entity-link/list?linkType='+ linkType +'&primaryObjectId=' + id })
}
//查询物料组的单位
export const getUnitApi = async (id) => {
  return request.get({ url: '/common/type/get?id=' + id })
}
