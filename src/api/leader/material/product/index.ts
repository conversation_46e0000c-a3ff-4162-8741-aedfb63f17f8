// 查询租户列表
import request from "@/config/axios";
// @ts-ignore
import {BrandVO} from "@/api/leader/material/product/brand";


export interface VersionPageReqVO extends PageParam {
  partVersionCode?: string
  partCategory: number;
  id?:number
}

export interface releaseVO {
  id: number
}

export interface BrandVO {
  id: number
  partId: number
  goodsCode: string
  partVersionCode: string
  partVersionStatus: number
  partVersionName: string
  sapno: string
  chineseName: string
  englishName: string
  chineseQualityDesc: string
  englishQualityDesc: string
  chinesePackMode: string
  englishPackMode: string
  businessPersonId: number
  goodsPersonId: number
  isOwnBrand: number
  isOemSample: number
}

//查询物料版本列表数据
export const getPartVersionPageApi = (params: VersionPageReqVO) => {
  if(params.partVersionCode === 'WIP'){
    params.partVersionCode = 0
  }
  params.partCategory = 1
  return  request.get({ url: '/part/version/page', params }).then((res)=>{
    const list = res.list
    for(let i = 0; i<list.length;i++){
      list[i].index=i + 1
    }
    return res
  })
}
// 查询物料版本详情
export const getPartVersionApi = async (id: number) => {
  return await request.get({ url: '/part/version/get?id=' + id })
}

// 发布
export const releasePartVersionApi = async (params: releaseVO) => {
  return await request.post({ url: '/part/version/release' , params })
}

//单条删除
export const delPartVersionApi = async (id: number) => {
  return await request.delete({ url: '/part/version/delete?id=' + id })
}

// 批量删除
export const delPartVersionListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/part/version/delete-batch?ids=' + ids })
}

// 复制
export const copyPartVersionApi = (data: BrandVO) => {
  return request.post({ url: '/part/version/copy', data })
}

//升版
export const upgradePartVersionApi = async (data: releaseVO) => {
  return await request.post({ url: '/part/version/upgrade' , data })
}

//获取采购组下拉框数据
export const getDeptIdApi = async (type: number) => {
  return await request.get({url: '/system/dept/list?type=' + type})
}
