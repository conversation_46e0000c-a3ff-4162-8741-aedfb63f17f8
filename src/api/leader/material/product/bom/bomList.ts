// 查询租户列表
import request from "@/config/axios";
// @ts-ignore


export interface VersionPageReqVO extends PageParam {
  partVersionId: number;
  ppvVersionStatus: number;
  partCode: string;
  id?:number
}

export interface releaseVO {
  id: number
}

//查询列表数据
export const getPartVersionPageApi = (params: VersionPageReqVO) => {
  params.ppvVersionStatus = 3
  params.partVersionId = parseInt(<string>sessionStorage.getItem("partVersionId"))
  console.log(sessionStorage.getItem("partCode"))
  if(sessionStorage.getItem("partCode")){
    params.partCode = <string>sessionStorage.getItem("partCode")
  }
  return  request.get({ url: '/part/version/page', params }).then((res)=>{
    const list = res.list
    for(let i = 0; i<list.length;i++){
      list[i].index=i + 1
    }
    return res
  })
}

//查询入库订单明细列表数据
export const getInboundOrderDetailsPageApi = (params: VersionPageReqVO) => {
  params.ppvVersionStatus = 4
  return  request.get({ url: '/part/version/page', params }).then((res)=>{
    const list = res.list
    for(let i = 0; i<list.length;i++){
      list[i].index=i + 1
    }
    return res
  })
}

// 查询物料版本详情
export const getPartVersionApi = async (id: number) => {
  return await request.get({ url: '/part/version/get?id=' + id })
}

// 发布
export const releasePartVersionApi = async (params: releaseVO) => {
  return await request.post({ url: '/part/version/release' , params })
}

//单条删除
export const delPartVersionApi = async (id: number) => {
  return await request.delete({ url: '/part/version/delete?id=' + id })
}

// 批量删除
export const delPartVersionListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/part/version/delete-batch?ids=' + ids })
}


//升版
export const upgradePartVersionApi = async (data: releaseVO) => {
  return await request.post({ url: '/part/version/upgrade' , data })
}
