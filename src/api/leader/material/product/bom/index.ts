// 查询租户列表
import request from "@/config/axios";
// @ts-ignore
import {BrandVO} from "@/api/leader/material/product/brand";


export interface BomPageReqVO extends PageParam {
}

export interface releaseVO {
  id: number
}

export interface BOMList{
  id: number;
  partVersionId?: number
  sapno?: number
  partCode?: number
}

export interface BomVO {
  id?: number
  partVersionId?: number
  versionId?: number
  bomQuanlity?: number
  bomUnit?: string
  partCode?: string
}


//查询bom列表数据
export const getBomPageApi = () => {
  return  request.get({ url: 'ppv/bom/list'}).then((res)=>{
    return res
  })
}

//查询bom列表list信息
export const getBomListApi = (params: BOMList) => {
  if(params.id){
    let partVersionId = params.id
    params = {
      partVersionId: partVersionId
    }
  } else {
    params = {
      partVersionId: parseInt(<string>sessionStorage.getItem("partVersionId"))
    }
  }
    return request.get({ url: '/ppv/bom/all', params })
}

//查询bom列表所有list信息
export const getAllBomListApi = (params: BOMList) => {
  return request.get({ url: '/ppv/bom/all', params })

}


// 查询单条bom数据
export const getBomApi = async (id: number) => {
  return await request.get({ url: '/ppv/bom/get?id=' + id })
}

//单条bom删除
export const delBomApi = async (id: number) => {
  return await request.delete({ url: '/ppv/bom/delete?id=' + id })
}

// bom批量删除
export const delBomListApi = (ids: string) => {
  return request.delete({ url: '/ppv/bom/delete-batch?ids=' + ids })
}

// 新增bom数据
export const createBomApi = (data: BomVO) => {
  return request.post({ url: '/ppv/bom/create', data })
}

// 修改bom信息
export const updateBomApi = (data: BomVO) => {
  return request.put({ url: '/ppv/bom/update', data })
}

// 获取下拉框数据
export const getSelectApi = async (typeCodes: string) => {
  return await request.get({ url: "/common/type/getMapTypeCodes?typeCodes=" + typeCodes })
}

//获取负责人下拉框数据
export const getUserApi = async () => {
  return await request.get({ url: '/system/user/list'})
}
