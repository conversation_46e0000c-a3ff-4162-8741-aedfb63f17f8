// 查询租户列表
import request from "@/config/axios";

export interface QualityPageReqVO extends PageParam {
  objectId: string | null;
  entityCode: string;
  partQuanlityId: string[];
  partId: string | null;
}

export interface QualityConfigPageReqVO extends PageParam {
  objectId: string | null;
  entityCode: string;
  sign: number
}

export interface QualityVO {
  partld: number ,
  checkAmount: number,
  finishCheckDate: string,
  infoDefect: string,
  packageDefect: string,
  partFacadeDefect: string,
  partFunctionDefect: string,
  checkResult: string,
  checkerld: number,
  checkCharge: number,
  remark: string,
}

export interface QualityConfigVO {
  entityCode: 'p_part_version_quality_config_link',
  objectId: number,
  qualityType: number,
  qualityLevel: number,
  checkDay: number
}


//查询质量信息列表数据
export const getQualityPageApi = (params) => {
  // @ts-ignore
  params.partId = sessionStorage.getItem(`partId`)
  return request.get({ url: '/part/quality/page', params })
}

//查询质检信息列表数据
export const getQualityConfigPageApi = (params: QualityConfigPageReqVO) => {
  params.objectId = sessionStorage.getItem(`partId`)
  params.entityCode = 'p_part_version_quality_config_link'
  params.sign = 2
  return request.get({ url: '/part/quality-config/page', params })
}

//入库订单订单行质检数据
export const getOrderInQualityPageApi = (params: QualityPageReqVO) => {
  //订单行质检
  params.partId = sessionStorage.getItem(`orderDetailId`)
  const search = window.location.search
  if(search.split('partQuanlityId=').length > 1){
    //走的详情，传相关参数
    let partQuanlityId = search.split('partQuanlityId=')[1]
    params.partQuanlityId = partQuanlityId.split(',')
  }
  return request.get({ url: '/part/quality/page', params })
}

//查询列表数据
export const getPurchaseQualityApi = (params: QualityPageReqVO) => {
  let index = window.location.search.split('index=')[1].split('&')[0]
  // @ts-ignore
  if(index == 2){
    //订单行质检
    params.entityCode = 'b_quality_order_item'
    params.objectId = sessionStorage.getItem(`orderDetailId`)
  } else {
    //子菜单质检
    params.objectId = sessionStorage.getItem(`purchaseOrderId`)
  }
  return request.get({ url: '/part/quality/quality-page', params })
}


// 查询质量详情（单条）
export const getQualityApi = async (id: number) => {
  return await request.get({ url: '/part/quality/get?id=' + id })
}

// 查询质检信息详情（单条）
export const getQualityConfigApi = async (id: number) => {
  return await request.get({ url: '/part/quality-config/get?id=' + id })
}

// 产品零部件根据质检类型获取质检等级
export const getQualityConfigLevel = async (id: number) => {
  const objectId = sessionStorage.getItem("partId");
  let str = '&entityCode=p_part_version_quality_config_link&objectId=' + objectId
  return await request.get({ url: '/part/quality-config/list?typeId=' + id + str})
}

// 采购订单根据质检类型获取质检等级
export const getPurchaseQualityLevel = async (id: number, partId: number) => {
  let str = '&entityCode=p_part_version_quality_config_link&objectId=' + partId
  return await request.get({ url: '/part/quality-config/list?typeId=' + id + str})
}

// 质量信息新增
export const createQualityApi = (data: QualityVO) => {
  return request.post({ url: '/part/quality/create', data })
}

// 质检信息新增
export const createQualityConfigApi = (data: QualityConfigVO) => {
  return request.post({ url: '/part/quality-config/create', data })
}

// 质量信息修改
export const updateQualityApi = (data: QualityVO) => {
  return request.put({ url: '/part/quality/update', data })
}

// 质检信息修改
export const updateQualityConfigApi = (data: QualityVO) => {
  return request.put({ url: '/part/quality-config/update', data })
}

//单条删除
export const delQualityApi = async (id: number) => {
  return await request.delete({ url: '/part/quality/delete?id=' + id })
}

//质检信息单条删除
export const delQualityConfigApi = async (id: number) => {
  return await request.delete({ url: '/part/quality-config/delete?id=' + id })
}

// 批量删除
export const delQualityListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/part/quality/delete-batch?ids=' + ids })
}
// 批量删除质检信息
export const delQualityConfigListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/part/quality-config/delete-batch?ids=' + ids })
}

// 获取下拉框数据
export const getSelectApi = async (typeCodes: string) => {
  return await request.get({ url: "/common/type/getMapTypeCodes?typeCodes=" + typeCodes })
}

//获取负责人下拉框数据
export const getUserApi = async () => {
  return await request.get({ url: '/system/user/list'})
}
