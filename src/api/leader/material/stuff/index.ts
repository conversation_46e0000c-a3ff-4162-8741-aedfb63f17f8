// 查询租户列表
import request from "@/config/axios";

export interface MaterialPageReqVO extends PageParam {
  goods_Code?: string
  partVersionName?: string
  ppType?: number
  partCode?: string
  partVersionCode?: string
}

export interface modelPageReqVO extends PageParam {
  chineseName?: string
  partCode?: string
}

export interface ModelVO {
  ppvType: number
  ppvVersionStatus: number
  chineseDesc: string
  avail_status: number
  cladding: string
  length: string
  width: string
  height: string
  effective_time: string
  expire_time: string
  goods_code: string
  partVersionName: string
  partCategory: number
}

export interface releaseVO {
  id: number
}



//查询材料列表数据
export const getMaterialPageApi = (params: MaterialPageReqVO) => {
  if(params.partVersionCode === 'WIP'){
    params.partVersionCode = 0
  }
  return  request.get({ url: '/p/material-version/page', params }).then((res)=>{
    if(res.list){
      const list = res.list
      for(let i = 0; i<list.length;i++){
        list[i].index=i + 1
      }
      return res
    }
  })
}

//查询模具列表数据
export const getModelPageApi = (params: modelPageReqVO) => {
  return  request.get({ url: '/p/mold-version/page', params }).then((res)=>{
    if(res.list){
      const list = res.list
      for(let i = 0; i<list.length;i++){
        list[i].index=i + 1
      }
      return res
    }
  })
}

// 新增模具
export const createModelApi = async (data: ModelVO) => {
  return await request.post({ url: '/p/mold-version/create', data })
}

// 修改模具
export const updateModelApi = async (data: ModelVO) => {
  return await request.put({ url: '/p/mold-version/update', data })
}

// 查询模具详情
export const getModelApi = async (id: number) => {
  return await request.get({ url: '/p/mold-version/get?id=' + id })
}

// 查询材料详情
export const getMaterialApi = async (id: number) => {
  return await request.get({ url: '/p/material-version/get?id=' + id })
}

// 发布
export const releaseApi = async (params: releaseVO) => {
  return await request.post({ url: '/p/material-version/release' , params })
}

// 发布模具
export const releaseModelApi = async (params: releaseVO) => {
  return await request.get({ url: '/p/mold-version/release' , params })
}


//创建新版本材料
export const createNewVersionMaterialApi = async (id: number) => {
  return await request.get({ url: '/p/material-version/new?id=' + id })
}

//创建新版本模具
export const createNewModelVersionApi = async (id: number) => {
  return await request.get({ url: '/p/mold-version/new?id=' + id })
}

//单条删除材料
export const delMaterialApi = async (id: number) => {
  return await request.delete({ url: '/p/material-version/delete?id=' + id })
}

//单条删除模具
export const delModelApi = async (id: number) => {
  return await request.delete({ url: '/p/mold-version/delete?id=' + id })
}

// 批量删除材料
export const delMaterialListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/p/material-version/delete-batch?ids=' + ids })
}


//复制创建材料
export const copyMaterialApi = async (id: number) => {
  return await request.get({ url: '/p/material-version/copy?id=' + id })
}

// 复制创建模具
export const copyModelApi = (id: number) => {
  return request.get({ url: '/p/mold-version/copy?id=' + id })
}


//升版
export const upgradeMaterialVersionApi = async (data: releaseVO) => {
  return await request.post({ url: '/part/version/upgrade' , data })
}
