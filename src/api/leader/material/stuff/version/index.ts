// 查询租户列表
import request from "@/config/axios";
// @ts-ignore
import {BrandVO} from "@/api/leader/material/product/brand";


export interface VersionPageReqVO extends PageParam {
  id: string | null;
  goodsCode?: string
  partVersionCode?: string
  chineseName?: string
  englishName?: string
  isOwnBrand?: boolean
}

export interface releaseVO {
  id: number
}

export interface BrandVO {
  id: number
  partId: number
  goodsCode: string
  partVersionCode: string
  partVersionStatus: number
  partVersionName: string
  sapno: string
  chineseName: string
  englishName: string
  chineseQualityDesc: string
  englishQualityDesc: string
  chinesePackMode: string
  englishPackMode: string
  businessPersonId: number
  goodsPersonId: number
  isOwnBrand: number
  isOemSample: number
}

//查询材料列表数据
export const getMaterialVersionPageApi = (params: VersionPageReqVO) => {
  params.id = sessionStorage.getItem(`materialId`)
  return  request.get({ url: '/p/material-version/getVersion', params }).then((res)=>{
    if(res.list){
      const list = res.list
      for(let i = 0; i<list.length;i++){
        list[i].index=i + 1
      }
      return res
    }
  })
}
// 查询材料详情
export const getMaterialVersionApi = async (id: number) => {
  return await request.get({ url: '/part/version/get?id=' + id })
}

// 发布
export const releaseMaterialVersionApi = async (params: releaseVO) => {
  return await request.post({ url: '/part/version/release' , params })
}

//单条删除
export const delMaterialVersionApi = async (id: number) => {
  return await request.delete({ url: '/part/version/delete?id=' + id })
}

// 批量删除
export const delMaterialVersionListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/part/version/delete-batch?ids=' + ids })
}

// 复制
export const copyMaterialVersionApi = (data: BrandVO) => {
  return request.post({ url: '/part/version/copy', data })
}

//升版
export const upgradeMaterialVersionApi = async (data: BrandVO) => {
  return await request.post({ url: '/part/version/upgrade' , data })
}
