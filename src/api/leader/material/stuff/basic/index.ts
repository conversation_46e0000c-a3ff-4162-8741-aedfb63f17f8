import request from '@/config/axios'

export interface MaterialVO {
  ppvType: number
  ppvVersionStatus: number
  chineseDesc: string
  avail_status: number
  cladding: string
  length: string
  width: string
  height: string
  effective_time: string
  expire_time: string
  goods_code: string
  partVersionName: string
  partCategory: number
}

export interface VersionPageReqVO extends PageParam {
  versionManageId?: number
  partId?: number
  goodsCode?: string
  partVersionCode?: string
  partVersionName?: string
  sapno?: string
  chineseName?: string
  englishName?: string
  chineseQualityDesc?: string
  englishQualityDesc?: string
  chinesePackMode?: string
  englishPackMode?: string
  businessPersonId?: number
  goodsPersonId?: number
  isOwnBrand?: number
  isOemSample?: number
  createTime?: Date[]
}

export interface VersionExcelReqVO {
  versionManageId?: number
  partId?: number
  goodsCode?: string
  partVersionCode?: string
  partVersionName?: string
  sapno?: string
  chineseName?: string
  englishName?: string
  chineseQualityDesc?: string
  englishQualityDesc?: string
  chinesePackMode?: string
  englishPackMode?: string
  businessPersonId?: number
  goodsPersonId?: number
  isOwnBrand?: number
  isOemSample?: number
  createTime?: Date[]
}


// 查询材料详情
export const getMaterialApi = async (id: number) => {
  return await request.get({ url: '/p/material-version/get?id=' + id })
}

// 新增材料信息
export const createMaterialApi = async (data: MaterialVO) => {
  return await request.post({ url: '/p/material-version/create', data })
}

// 修改材料版本
export const updateMaterialApi = async (data: MaterialVO) => {
  return await request.put({ url: '/p/material-version/update', data })
}

// 获取下拉框数据
export const getSelectApi = async (typeCodes: string) => {
  return await request.get({ url: "/common/type/getMapTypeCodes?typeCodes=" + typeCodes })
}
//获取负责人下拉框数据
export const getUserApi = async () => {
  return await request.get({ url: '/system/user/list'})
}
