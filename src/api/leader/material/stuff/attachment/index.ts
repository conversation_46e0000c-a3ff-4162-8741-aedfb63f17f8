import request from '@/config/axios'

export interface FileVO {
  id: number
  configId: number
  path: string
  name: string
  url: string
  size: string
  type: string
  createTime: Date
}

export interface FilePageReqVO extends PageParam {
  path?: string
  type?: string
  createTime?: Date[]
  objectId?: number
  entityCode?: string
}


// 查询文件code字段
export const getFileCodeApi = () => {
  return request.get({ url: '/infra/file/code'})
}
// 查询文件列表
export const getFilePageApi = (params: FilePageReqVO) => {
    params.entityCode = 'p_material_file'
  const objectId = sessionStorage.getItem(`materialId`)
    // @ts-ignore
  params.objectId = parseInt(objectId)
  return request.post({ url: '/infra/file/list', params }).then((res)=>{
    const list = res.list
    let updateUrl = import.meta.env.VITE_BASE_URL?import.meta.env.VITE_BASE_URL+import.meta.env.VITE_SERVICE_PORT+import.meta.env.VITE_API_URL:'http://'+window.location.hostname+import.meta.env.VITE_SERVICE_PORT+import.meta.env.VITE_API_URL
    if(list){
      for(let i = 0; i<list.length;i++){
        list[i].url=updateUrl+list[i].url;
        console.log(list)
      }
    }
    return res
  })
}

// 删除文件
export const deleteFileApi = (id: number) => {
  return request.delete({ url: '/infra/file/delete?id=' + id })
}
