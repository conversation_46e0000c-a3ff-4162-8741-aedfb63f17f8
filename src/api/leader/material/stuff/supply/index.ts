// 查询租户列表
import request from "@/config/axios";

export interface QualityPageReqVO extends PageParam {
  orgCategory: number;
  partVersionId: string | null;
  goodsCode?: string
  partVersionCode?: string
  chineseName?: string
  englishName?: string
  isOwnBrand?: boolean
  partId?: number
}

export interface PartConnectionVO{
  ppolType:number
  partId:number
  orgId:number
  orgPartCode:string
  orgPartName:string
  partVersionId: number
}


//查询供应信息列表数据
export const getProvideInformationPageApi = (params: QualityPageReqVO) => {
  params.partVersionId = sessionStorage.getItem(`materialId`)
  params.orgCategory = 1
  return request.get({ url: '/common/org/partOrgPage', params })
}

// 查询（单条）
export const getProvideInformationApi = async (id: number) => {
  return await request.get({ url: '/common/org/getByOrgLinkId?id=' + id })
}

// 查询供应商
export const getOrgApi = async () => {
  return await request.get({ url: '/common/org/list?orgCategory=1'})
}


// 新增
export const createProvideInformationApi = (data) => {
  return request.post({ url: '/common/part-org-link/create', data })
}

// 修改
export const updateProvideInformationApi = (data) => {
  return request.put({ url: '/common/part-org-link/update', data })
}

//单条删除
export const delProvideInformationApi = async (id: number) => {
  return await request.delete({ url: '/common/part-org-link/delete?id=' + id })
}

// 批量删除
export const delProvideInformationListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/common/part-org-link/delete-batch?ids=' + ids })
}

// 获取下拉框数据
export const getSelectApi = async (typeCodes: string) => {
  return await request.get({ url: "/common/type/getMapTypeCodes?typeCodes=" + typeCodes })
}

//获取负责人下拉框数据
export const getUserApi = async () => {
  return await request.get({ url: '/system/user/list'})
}
