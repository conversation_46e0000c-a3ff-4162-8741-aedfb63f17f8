import request from '@/config/axios'

export interface InfoDetailsVO {
  id: number
  objectCostTypeLarge: number
  objectCostTypeSmall: string
  linkedObjectId: number
  objectCode: string
  objectName: string
  projectSchemeType: number
  projectSchemeId: number
  changeOrderId: number
  costType: number
  quantityOrHours: number
  unit: string
  unitPrice: number
  currency: number
  totalPrice: number
  isProjectShare: boolean
  sharedItemId: number
  source: string
  description: string
  costReason: string
  costStatus: number
  effectiveTime: Date
  expirationTime: Date
  reimburseTime: Date
  departmentCostCenterId: number
  userId: number
  thirdPartyDocument: string
  processInstanceId: string
  processStatus: number
}

export interface InfoDetailsPageReqVO extends PageParam {
  objectCostTypeLarge?: number
  objectCostTypeSmall?: string
  linkedObjectId?: number
  objectCode?: string
  objectName?: string
  projectSchemeType?: number
  projectSchemeId?: number
  changeOrderId?: number
  costType?: number
  quantityOrHours?: number
  unit?: string
  unitPrice?: number
  currency?: number
  totalPrice?: number
  isProjectShare?: boolean
  sharedItemId?: number
  source?: string
  description?: string
  costReason?: string
  costStatus?: number
  effectiveTime?: Date[]
  expirationTime?: Date[]
  reimburseTime?: Date[]
  departmentCostCenterId?: number
  userId?: number
  thirdPartyDocument?: string
  processInstanceId?: string
  systemCode?: string
  processStatus?: number
  createTime?: Date[]
  filed?: any
  order?: any
}

export interface InfoDetailsExcelReqVO {
  objectCostTypeLarge?: number
  objectCostTypeSmall?: string
  linkedObjectId?: number
  objectCode?: string
  objectName?: string
  projectSchemeType?: number
  projectSchemeId?: number
  changeOrderId?: number
  costType?: number
  quantityOrHours?: number
  unit?: string
  unitPrice?: number
  currency?: number
  totalPrice?: number
  isProjectShare?: boolean
  sharedItemId?: number
  source?: string
  description?: string
  costReason?: string
  costStatus?: number
  effectiveTime?: Date[]
  expirationTime?: Date[]
  reimburseTime?: Date[]
  departmentCostCenterId?: number
  userId?: number
  thirdPartyDocument?: string
  processInstanceId?: string
  processStatus?: number
  createTime?: Date[]
}

// 查询成本明细列表
export const getInfoDetailsPageApi = async (params: InfoDetailsPageReqVO) => {
  return await request.get({ url: '/cost/info-details/page', params })
}

// 查询成本明细详情
export const getInfoDetailsApi = async (id: number) => {
  return await request.get({ url: '/cost/info-details/get?id=' + id })
}

// 新增成本明细
export const createInfoDetailsApi = async (data: InfoDetailsVO) => {
  return await request.post({ url: '/cost/info-details/create', data })
}

// 修改成本明细
export const updateInfoDetailsApi = async (data: InfoDetailsVO) => {
  return await request.put({ url: '/cost/info-details/update', data })
}

// 删除成本明细
export const deleteInfoDetailsApi = async (id: number) => {
  return await request.delete({ url: '/cost/info-details/delete?id=' + id })
}

// 导出成本明细 Excel
export const exportInfoDetailsApi = async (params: InfoDetailsExcelReqVO) => {
  return await request.download({ url: '/cost/info-details/export-excel', params })
}

// 导出成本明细 Excel
export const downloadTemplate = async (type: number) => {
  return await request.download({ url: '/cost/info-details/export-template?type=' + type })
}
