import request from "@/config/axios";
import {RegisterVO} from "@/api/leader/saleProject/register";

// @ts-ignore

export interface ReportWorkVO {
  projectId: number
  typeId: number
  reportType: number
  abnormalReportType: string
  reportStatus:string
  reportHour:Date[]
  reportTime:number
  remark:number
  unitPrice:string
  totalPrice:string
}
export interface FilePageReqVO extends PageParam {
  entityCode: string;
  objectId:number
  path?: string
  type?: string
  createTime?: Date[]
}
export interface ReportWorkPageReqVO extends PageParam {
  typeId:number
  projectId:number
  reportType:number
  abnormalReportType:number
  reportStatus:number
  createTime:Date[],
  createTimeStart:Date,
  createTimeEnd:Date
}
// 获取报工记录列表
export const getReportWorkPageApi = async (params: ReportWorkPageReqVO) => {
  if (params.createTime != null && params.createTime.length > 0) {
    params.createTimeStart = params.createTime[0]
    params.createTimeEnd = params.createTime[1]
  }
  return  request.get({url: '/cost/report_work/page', params})
}

// 获取报工记录详情
export const getReportWorkDetailApi = async (id: number) => {
  return  request.get({url: '/cost/report_work/get?id=' + id})
}

//获取负责人下拉框数据
export const getUserApi = async () => {
  return await request.get({ url: '/system/user/list'})
}

export const createWorkApi = async (data: RegisterVO) => {
  return await request.post({url: '/cost/report_work/create', data})
}
/**
 * @Author: WangShijun
 * @Date: 2025/03/04
 * @Function: 任务报工
 */
export const createTaskWorkApi = async (data: RegisterVO) => {
  return await request.post({url: '/cost/report_work/taskReport', data})
}

/**
 * @Author: WangShijun
 * @Date: 2024/12/02
 * @Function: 批量创建报工
 */
export const batchCreateWorkApi = async (data: RegisterVO[]) => {
  return await request.post({url: '/cost/report_work/createList', data})
}

/**
  * Author： 许祎婷
  * Time： 2024/11/06
  * Description：获取事项
*/
export const getTypeOptionUserApi = async (str) => {
  return await request.get({ url: '/cost/dept-type-link/list?' + str})
}

/**
 * @Author: WangShijun
 * @Date: 2025/01/17
 * @Function:报工任务事项下拉框
 */
export const getTypeListReportApi = async (str) => {
  return await request.get({ url: '/cost/dept-type-link/list-report?' + str})
}

/**
  * Author： 许祎婷
  * Time： 2024/11/06
  * Description：获取下拉框数据
*/
export const getSelectApi = async (typeCodes: string) => {
  return await request.get({ url: "/common/type/getMapTypeCodes?typeCodes=" + typeCodes })
}
