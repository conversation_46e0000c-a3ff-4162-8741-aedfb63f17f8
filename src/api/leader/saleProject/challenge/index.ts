import request from "@/config/axios";

// @ts-ignore

export interface ChallengeVO {
  projectId:number
  challengeName: string
  challengeType: number
  reason: string
  proposer:string
  proposalDate:Date[]
  isRepeat:number
  severity:number
  solutionMeasures:string
  measureTrackingResults:string
  status:number
}
export interface FilePageReqVO extends PageParam {
  entityCode: string;
  objectId:number
  path?: string
  type?: string
  createTime?: Date[]
}
export interface ChallengePageReqVO extends PageParam {
  proposalDate:Date[]
  isQuestion:number
  projectId:number
  entityCode:string
  chargePersonDeptId:number
  proposerDeptId:number
  chargePerson:number
  proposer:number
  filed:string
  order:string
  status?: number[] | string[]
  statusStr?: string
}
// 获取问题列表
export const getQuestionPageApi = async (params: ChallengePageReqVO) => {
  if (params.proposalDate!=null||params.proposalDate!=undefined){
    params.proposalDate[0]=params.proposalDate[0]+" 00:00:00"
    params.proposalDate[1] =params.proposalDate[1]+" 23:59:59"
  }
  if (params.status && Array.isArray(params.status) && params.status.length > 0) {
    params.statusStr = params.status.join(',')
    delete params.status
  }
  //判断是否有部门查询
  const chargePersonDeptId = sessionStorage.getItem('chargePersonDeptId')
  const proposerDeptId=sessionStorage.getItem('proposerDeptId')
  if(chargePersonDeptId !== null && chargePersonDeptId !== undefined) {
    params.chargePersonDeptId = chargePersonDeptId
  }
  if(proposerDeptId !== null && proposerDeptId !== undefined) {
    params.proposerDeptId = proposerDeptId
  }
  params.isQuestion=1
  return  request.get({url: '/project/challenge/page', params})
}
export const getAfterSalesPageApi = async (params: ChallengePageReqVO) => {
  // if (params.proposalDate!=null||params.proposalDate!=undefined){
  //   params.proposalDate[0]=params.proposalDate[0]+" 00:00:00"
  //   params.proposalDate[1] =params.proposalDate[1]+" 23:59:59"
  // }
  //判断是否有部门查询
  const chargePersonDeptId = sessionStorage.getItem('chargePersonDeptId')
  const proposerDeptId=sessionStorage.getItem('proposerDeptId')
  if(chargePersonDeptId !== null && chargePersonDeptId !== undefined) {
    params.chargePersonDeptId = chargePersonDeptId
  }
  if(proposerDeptId !== null && proposerDeptId !== undefined) {
    params.proposerDeptId = proposerDeptId
  }
  params.isQuestion=3
  return  request.get({url: '/project/challenge/page', params})
}
// 获取项目中问题列表
export const getProjectQuestionPageApi = async (params: ChallengePageReqVO) => {
  if (params.proposalDate!=null||params.proposalDate!=undefined){
    console.log(params.proposalDate)
    params.proposalDate[0]=params.proposalDate[0]+" 00:00:00"
    params.proposalDate[1] =params.proposalDate[1]+" 23:59:59"
  }
  // params.entityCode="p_project_tab_question"
  params.projectId=sessionStorage.getItem("marketId")
  params.isQuestion=1
  return  request.get({url: '/project/challenge/page', params})
}


// 获取项目中售后列表
export const getProjectAfterSalesPageApi = async (params: ChallengePageReqVO) => {
  // if (params.proposalDate!=null||params.proposalDate!=undefined){
  //   console.log(params.proposalDate)
  //   params.proposalDate[0]=params.proposalDate[0]+" 00:00:00"
  //   params.proposalDate[1] =params.proposalDate[1]+" 23:59:59"
  // }
  // params.entityCode="p_project_tab_question"
  params.projectId=sessionStorage.getItem("marketId")
  params.isQuestion=3
  return  request.get({url: '/project/challenge/page', params})
}
// 获取风险列表
export const getRiskPageApi = async (params: ChallengePageReqVO) => {
  if (params.proposalDate!=null||params.proposalDate!=undefined){
    params.proposalDate[0]=params.proposalDate[0]+" 00:00:00"
    params.proposalDate[1] =params.proposalDate[1]+" 23:59:59"
  }
  params.isQuestion=2
  return  request.get({url: '/project/challenge/page', params})
}
// 获取x项目中风险列表
export const getProjectRiskPageApi = async (params: ChallengePageReqVO) => {
  if (params.proposalDate!=null||params.proposalDate!=undefined){
    params.proposalDate[0]=params.proposalDate[0]+" 00:00:00"
    params.proposalDate[1] =params.proposalDate[1]+" 23:59:59"
  }
  params.projectId=sessionStorage.getItem("marketId")
  params.isQuestion=2
  return  request.get({url: '/project/challenge/page', params})
}

//创建项目问题/风险
export const createChallengeApi = (data: ChallengeVO) => {
  return request.post({url: '/project/challenge/create', data})
}

// 项目问题/风险信息修改
export const updateChallengeApi = (data: ChallengeVO) => {
  return request.put({url: '/project/challenge/update', data})
}

//单条项目问题/风险信息删除
export const delChallengeApi = async (id: number) => {
  return await request.delete({url: '/project/challenge/delete?id=' + id})
}


// 单条项目问题/风险信息批量删除
export const delChallengeListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/project/challenge/delete-batch?ids=' + ids })
}
//获取项目二级问题类型
export const getChallengeTypeApi = async (typeCode:string) => {
  typeCode = "'" + typeCode + "'"
  return await request.get({ url: '/common/type/getChildrenTypeCodes?typeCode='+typeCode })
}
// // 查询单条项目问题/风险信息
export const getChallengeApi = async (id: number,isTemplate: number) => {
  console.log('444444')
  // return await request.get({ url: '/project/challenge/get?id=' + id  +'&isTemplate='+isTemplate})
  return request.get({ url: '/project/challenge/get?id=' + id  +'&isTemplate='+isTemplate}).then((res)=>{
    let updateUrl = import.meta.env.VITE_BASE_URL?import.meta.env.VITE_BASE_URL+import.meta.env.VITE_SERVICE_PORT:'http://'+window.location.hostname+import.meta.env.VITE_SERVICE_PORT
    if(res.reason){
      let start=res.reason.indexOf("http://")
      let end=res.reason.indexOf(import.meta.env.VITE_API_URL)
      if(start!=-1&&end!=-1){
        let str=res.reason.substring(start,end)
        res.reason=res.reason.replaceAll(str,updateUrl)
      }
    }
    console.log(res,'打印图片地址转化4444')
    return res
  })
}

export const getQuestionChallengeApi = async (id: number,isTemplate:number) => {
  // return await request.get({ url: '/project/challenge/get?id=' + id+'&isTemplate='+isTemplate })
  return request.get({ url: '/project/challenge/get?id=' + id+'&isTemplate='+isTemplate }).then((res)=>{
    let updateUrl = import.meta.env.VITE_BASE_URL?import.meta.env.VITE_BASE_URL+import.meta.env.VITE_SERVICE_PORT:'http://'+window.location.hostname+import.meta.env.VITE_SERVICE_PORT
    if(res.reason){
      let start=res.reason.indexOf("http://")
      let end=res.reason.indexOf(import.meta.env.VITE_API_URL)
      if(start!=-1&&end!=-1){
        let str=res.reason.substring(start,end)
        res.reason=res.reason.replaceAll(str,updateUrl)
      }
    }
    console.log(res,'打印图片地址转化5555')
    return res
  })
}

// 查询文件列表
export const getFilePageApi = (params: FilePageReqVO) => {
  // params.entityCode = 'p_challenge_file'
  // const objectId = sessionStorage.getItem(`questionId`)
  // params.objectId = parseInt(<string>objectId)
  if(params.objectId === 0) {
    return console.log('不发接口，没id')
  } else {
    return request.post({ url: '/infra/file/list', params }).then((res)=>{
      const list = res.list
      let updateUrl = import.meta.env.VITE_BASE_URL?import.meta.env.VITE_BASE_URL+import.meta.env.VITE_SERVICE_PORT+import.meta.env.VITE_API_URL:'http://'+window.location.hostname+import.meta.env.VITE_SERVICE_PORT+import.meta.env.VITE_API_URL
      if(list){
        for(let i = 0; i<list.length;i++){
          list[i].url=updateUrl+list[i].url;
          console.log(list)
        }
      }
      return res
    })
  }

}
// 查询项目问题Tab文件列表
export const getQuestionTabFilePageApi = (params: FilePageReqVO) => {
  params.entityCode = 'p_challenge_file'
  const objectId = sessionStorage.getItem(`questionId`)
  params.objectId = parseInt(<string>objectId)
  return request.post({ url: '/infra/file/list', params }).then((res)=>{
    const list = res.list
    let updateUrl = import.meta.env.VITE_BASE_URL?import.meta.env.VITE_BASE_URL+import.meta.env.VITE_SERVICE_PORT+import.meta.env.VITE_API_URL:'http://'+window.location.hostname+import.meta.env.VITE_SERVICE_PORT+import.meta.env.VITE_API_URL
    if(list){
      for(let i = 0; i<list.length;i++){
        list[i].url=updateUrl+list[i].url;
        console.log(list)
      }
    }
    return res
  })
}
// 查询售后Tab文件列表
export const getAfterSalesTabFilePageApi = (params: FilePageReqVO) => {
  params.entityCode = 'p_project_sales_file'
  const objectId = sessionStorage.getItem(`afterSaleId`)
  params.objectId = parseInt(<string>objectId)
  return request.post({ url: '/infra/file/list', params }).then((res)=>{
    const list = res.list
    let updateUrl = import.meta.env.VITE_BASE_URL?import.meta.env.VITE_BASE_URL+import.meta.env.VITE_SERVICE_PORT+import.meta.env.VITE_API_URL:'http://'+window.location.hostname+import.meta.env.VITE_SERVICE_PORT+import.meta.env.VITE_API_URL
    if(list){
      for(let i = 0; i<list.length;i++){
        list[i].url=updateUrl+list[i].url;
        console.log(list)
      }
    }
    return res
  })
}
export const deleteFileApi = (id: number) => {
  return request.delete({ url: '/infra/file/delete?id=' + id })
}
// 查询文件列表
export const getQuestionFilePageApi = (params: FilePageReqVO) => {
  params.entityCode = 'p_challenge_file'
  const objectId = sessionStorage.getItem(`marketId`)
  params.objectId = parseInt(<string>objectId)
  return request.post({ url: '/infra/file/list', params }).then((res)=>{
    const list = res.list
    let updateUrl = import.meta.env.VITE_BASE_URL?import.meta.env.VITE_BASE_URL+import.meta.env.VITE_SERVICE_PORT+import.meta.env.VITE_API_URL:'http://'+window.location.hostname+import.meta.env.VITE_SERVICE_PORT+import.meta.env.VITE_API_URL
    if(list){
      for(let i = 0; i<list.length;i++){
        list[i].url=updateUrl+list[i].url;
        console.log(list)
      }
    }
    return res
  })
}
//获取负责人下拉框数据
export const getUserApi = async () => {
  return await request.get({ url: '/system/user/list'})
}
