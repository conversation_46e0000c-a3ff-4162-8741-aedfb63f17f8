// 查询租户列表
import request from "@/config/axios";
// @ts-ignore


export interface VersionPageReqVO extends PageParam {
  partCategory: number;
  ppvVersionStatus: number;
  id?:number
}

export interface releaseVO {
  id: number
}

export interface ProductVO {
  objectId: number
  entityCode: string
  partVersionId: number
}


//查询物料版本列表数据
export const getVersionPageApi = (params: VersionPageReqVO) => {
  params.partCategory = 1
  return  request.get({ url: '/part/version/page', params }).then((res)=>{
    const list = res.list
    for(let i = 0; i<list.length;i++){
      list[i].index=i + 1
    }
    return res
  })
}
// 查询单条物料版本
export const getPartVersionApi = async (id: number) => {
  return await request.get({ url: '/part/version/get?id=' + id })
}


// 订单明细新增
export const createOrderDetailsApi = (data: ProductVO) => {
  return request.post({url: '/project/part-item/create', data})
}

// 获取下拉框数据
export const getSelectApi = async (typeCodes: string) => {
  return await request.get({ url: "/common/type/getMapTypeCodes?typeCodes=" + typeCodes })
}
