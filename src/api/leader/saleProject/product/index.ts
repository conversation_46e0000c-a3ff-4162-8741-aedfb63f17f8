// 查询租户列表
import request from "@/config/axios";
// @ts-ignore


export interface ProductPageReqVO extends PageParam {
  objectId?: number;
  entityCode?: string
}

export interface ProductVO {
  objectId: number
  entityCode: string
  partVersionId: number
}


// 查询销售报价列表
export const getSalesQuotePageApi = async (params: ProductPageReqVO) => {
  params.entityCode = 's_sale_version_code'
  const objectId = sessionStorage.getItem(`marketId`)
  // @ts-ignore
  params.objectId = parseInt(objectId)
  return  request.get({url: '/project/part-item/page', params})
}

// // 查询产品清单详情
// export const getProductApi = async (id: number) => {
//   return await request.get({ url: '/stuff/version/get?id=' + id })
// }

// 销售报价信息新增
export const createSalesQuoteApi = (data: ProductVO) => {
  return request.post({url: '/project/part-item/create', data})
}

// 销售报价信息修改
export const updateSalesQuoteApi = (data: ProductVO) => {
  return request.put({url: '/project/part-item/update', data})
}


//单条销售报价信息删除
export const delSalesQuoteApi = async (id: number) => {
  return await request.delete({url: '/project/part-item/delete?id=' + id})
}

// 销售报价信息批量删除
export const delSalesQuoteListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({url: '/project/part-item/delete-batch?ids=' + ids})
}
