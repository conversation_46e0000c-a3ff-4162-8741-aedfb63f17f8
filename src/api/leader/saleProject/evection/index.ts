import request from "@/config/axios";

// @ts-ignore

export interface ExperienceVO {
  deptId: string;
  projectId:number
  objectId: number
  projectSource:number
  chargePersonId:number
  partVersionId: number
  summarizeTime:Date[]
  experienceSummarize:string
  experienceCode:string
  experienceName:string
  questionDesc:string
  questionAnalyze:string
  solution:string
}
export interface ExperiencePageReqVO extends PageParam {
  projectId:number
}

//转化为经验教训
export const createEvectionApi = (data: ExperienceVO) => {
  return request.post({url: '/project/business/create', data})
}

//获取负责人下拉框数据
export const getUserApi = async () => {
  return await request.get({ url: '/system/user/list'})
}

// 获取下拉框数据
export const getSelectApi = async (typeCodes: string) => {
  return await request.get({ url: "/common/type/getMapTypeCodes?typeCodes=" + typeCodes })
}

//出差列表查询
export const getEvectionPageApi = (params: ExperienceVO) => {
  //判断是否有部门查询
  const deptId = sessionStorage.getItem('deptId')
  if(deptId !== null && deptId !== undefined) {
    params.deptId = deptId
  }
  return  request.get({ url: '/project/business/page', params }).then((res)=>{
    const list = res.list
    for(let i = 0; i<list.length;i++){
      list[i].index=i + 1
    }
    return res
  })
}

//根据部门查询用户
export const getDeptUserApi = (params) => {
  return  request.get({ url: '/system/user/page', params }).then((res)=>{
    const list = res.list
    for(let i = 0; i<list.length;i++){
      list[i].index=i + 1
    }
    return res
  })
}

//经验教训修改
export const updateEvectionApi = (data: ExperienceVO) => {
  return request.put({url: '/project/business/update', data})
}
// 单条查询经验教训
export const getEvectionApi = async (id: number) => {
  return await request.get({ url: '/project/business/get?id=' + id})
}

// 删除
export const deleteEvectionApi = async (id: number) => {
  return await request.delete({ url: '/project/business/delete?id=' + id })
}

// 批量删除
export const delEvectionListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/project/business/delete-batch?ids=' + ids })
}

