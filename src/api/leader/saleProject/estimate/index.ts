import request from "@/config/axios";
import {FilePageReqVO} from "@/api/leader/saleProject/project/meet";

export interface EstimatePageReqVO {
  projectId: string | null;
  projectCode?: string
  projectName?: string
  estimateCode?: string
  estimateName?: string
  estimateStatus?: string
  createTime: string
  createStartTime: string
  createEndTime: string
  deptId: string;
  creatorDeptId: number;
  actualStartTime: string;
  actualEndTime: string;
  actualTime: null;
  planTime: string;
  planEndTime: string;
  planStartTime: string;
  isTemplate: number;
  projectStatusList: number[];
  projectCategory: number
  projectCategoryList: any
  orgCode?: string
  orgName?: string
  chargePersonId?: number
  pageSize: number
  pageNo: number
  filed?:any,
  order?:any
}

export interface DeptTypeLinkPageReqVO {
  typeId: number
  isEstimate: number
  isActual: number
  isTop: number
  createTime: string
  createStartTime: string
  createEndTime: string
  deptId: string
  pageSize: number
  pageNo: number
  filed?:any
  order?:any
}

export interface DeptTypeLinkListReqVO {
  typeId: number
  isEstimate: number
  isActual: number
  isTop: number
  createTime: string
  createStartTime: string
  createEndTime: string
  deptId: string
  outSide: number
}

export const getEstimatePageApi = (params: EstimatePageReqVO) => {
  return request.get({url: '/cost/estimate/page', params}).then((res) => {
    const list = res.list
    for (let i = 0; i < list.length; i++) {
      list[i].index = i + 1
    }
    return res
  })
}

export const getDeptTypeLinkPageApi = (params: DeptTypeLinkPageReqVO) => {
  return request.get({url: '/cost/dept-type-link/page', params}).then((res) => {
    const list = res.list
    for (let i = 0; i < list.length; i++) {
      list[i].index = i + 1
    }
    return res
  })
}

export const getDeptTypeLinkListApi = (params: DeptTypeLinkListReqVO) => {
  return request.get({url: '/cost/dept-type-link/list', params}).then((res) => {
    return res
  })
}

// 查询定额明细列表
export const getListApi = async (params: EstimatePageReqVO) => {
  return await request.get({ url: '/cost/estimate-details/list', params })
}

// 查询定额工时明细详情
export const getDetailsApi = async (id: string) => {
  return await request.get({ url: `/cost/estimate-details/get?id=${id}` })
}


// 创建定额工时
export const createEstimateApi = async (data: any) => {
  return await request.post({url: '/cost/estimate/create', data})
}

// 创建定额工时明细
export const createEstimateDetailsApi = async (data: any) => {
  return await request.post({ url: '/cost/estimate-details/create', data })
}

// 获取定额工时
export const getEstimateApi = async (id: string) => {
  return await request.get({ url: `/cost/estimate/get?id=${id}` })
}

// 获取定额工时列表
export const getEstimateListApi = async (params: EstimatePageReqVO) => {
  return await request.get({ url: '/cost/estimate/list', params })
}

/**
  * Author： 许祎婷
  * Time： 2024/11/12
  * Description：更新定额工时列表
*/
export const submitEstimateListApi = async (data) => {
  return await request.put({ url: '/cost/estimate/update', data })
}

export const submitEstimateDetailsApi = async (estimateId, itemTopTypeId, confirmUser) => {
  return await request.get({
    url: `/cost/estimate-details/confirm`,
    params: { estimateId, itemTopTypeId, confirmUser }
  })
}


/**
  * Author： 许祎婷
  * Time： 2024/11/12
  * Description： 删除事项
*/
export const delEstimateApi = async (id) => {
  return await request.delete({ url: '/cost/estimate-details/deleteInfo?id=' + id})
}

/**
 * Author： 许祎婷
 * Time： 2024/11/20
 * Description： 删除分类
 */
export const delEstimateTypeApi = async (topTypeId: number[],estimateId: number) => {
  return await request.delete({ url: '/cost/estimate-details/delete?topTypeId=' + topTypeId + '&estimateId=' + estimateId})
}

/**
 * Author： 许祎婷
 * Time： 2024/11/21
 * Description： 删除模板
 */
export const delEstimateTemplateApi = async (ids) => {
  return await request.delete({ url: '/cost/estimate/delete?id=' + ids})
}

/**
  * Author： 许祎婷
  * Time： 2024/11/14
  * Description：变更提交
*/
export const createChangeListApi = async (data: any) => {
  return await request.post({ url: '/cm/change-order/create', data })
}

/**
  * Author： 许祎婷
  * Time： 2024/11/14
  * Description：获取变更记录
*/
export const getChangeListApi = async (params) => {
  return await request.get({ url: '/cm/change-order/page', params })
}

/**
  * Author： 许祎婷
  * Time： 2024/11/21
  * Description：选择模板后自动创建定额工时
*/
export const createAutoEstimateApi = async (params) => {
  return await request.get({ url: '/cost/estimate/autoCreate', params })
}

/**
  * Author： 许祎婷
  * Time： 2024/11/21
  * Description：查询项目下的部门
*/
export const getEstimateDept = async (id) => {
  return await request.get({ url: '/cost/estimate/getDept?projectId=' + id })
}

/**
  * Author： 许祎婷
  * Time： 2024/12/02
  * Description: 分类维护自动保存
*/
export const autoCreateApi = (typeIds,estimateId,deptId) => {
  return request.get({url: '/cost/estimate-details/autoCreate?typeIds=' + typeIds + '&estimateId=' + estimateId + '&deptId=' + deptId})
}

export const getTypeListApi = async (params) => {
  return request.get({ url: '/common/type/getList', params })
}

// 查询文件列表
export const getFilePageApi = (data: FilePageReqVO) => {
  const objectId = data.objectId
  let params = {
    entityCode: 'cm_estimate_fileList',
    objectId: parseInt(<string>objectId)
  }
  return request.post({url: '/infra/file/list', params}).then((res) => {
    const list = res.list
    let updateUrl = 'http://' + window.location.hostname + import.meta.env.VITE_SERVICE_PORT + import.meta.env.VITE_API_URL
    if (list) {
      for (let i = 0; i < list.length; i++) {
        list[i].url = updateUrl + list[i].url;
        console.log(list)
      }
    }
    return res
  })
}

