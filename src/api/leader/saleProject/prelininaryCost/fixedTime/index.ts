import request from '@/config/axios'

// 查询单条销售项目
export const getProjectsDetailApi = async (id: number, isTemplate: number) => {
  isTemplate = 1
  return await request.get({url: '/project/manager/get?id=' + id + '&isTemplate=' + isTemplate})
}

// 查询销售项目列表
export const getReportTimeDetailApi = async (projectId: number, typeId: number) => {
  return await request.get({url: '/cost/report_work/getDetail?projectId=' + projectId + '&typeId=' + typeId})
}



