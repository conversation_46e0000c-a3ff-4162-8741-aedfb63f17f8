// 查询租户列表
import request from "@/config/axios";
// @ts-ignore
import {BrandVO} from "@/api/leader/material/product/brand";
import {ReportPageReqVO} from "@/api/leader/productionReport";

export interface MarketPageReqVO {
  projectNotNature: number;
  projectId: string | null;
  createTime: string
  costCreateTime: string
  createStartTime: string
  createEndTime: string
  chargePersonName: string;
  chargePersonDeptId: number;
  projectNature: number;
  deptId: string;
  creatorDeptId: number;
  actualStartTime: string;
  actualEndTime: string;
  actualTime: null;
  planTime: string;
  planEndTime: string;
  planStartTime: string;
  isTemplate: number;
  projectStatusList: number[];
  projectCode?: string
  projectName?: string
  projectCategory: number
  projectCategoryList: any
  orgCode?: string
  orgName?: string
  chargePersonId?: number
  pageSize: number
  pageNo: number
  isUncompleted: number
  a?: number
  filed?:any,
  order?:any
}

export interface ReportPageReqVO {
  projectNotNature: number;
  projectId: string | null;
  createTime: string
  createStartTime: string
  createEndTime: string
  chargePersonName: string;
  chargePersonDeptId: number;
  projectNature: number;
  deptId: string;
  creatorDeptId: number;
  actualStartTime: string;
  actualEndTime: string;
  actualTime: null;
  planTime: Boolean;
  planEndTime: string;
  planStartTime: string;
  isTemplate: number;
  projectStatusList: number[];
  projectCode?: string
  projectName?: string
  projectCategory: number
  projectCategoryList: any
  orgCode?: string
  orgName?: string
  chargePersonId?: number
  pageSize: number
  pageNo: number
  isUncompleted: number
  userId: string | null
  planDateStart: string;
  planDateEnd: string;
  actualDateStart: string;
  actualDateEnd: string;
  filed?: any,
  order?: any,

}

export interface releaseVO {
  id: number
}

export interface PlanPageReqVO {
  deptId: string;
  actualEndTime: any;
  primaryObjectId: number
  linkType: string
  projectNotNature: number
}

export interface BrandVO {
  id: number
  partId: number
  goodsCode: string
  partVersionCode: string
  partVersionStatus: number
  partVersionName: string
  sapno: string
  chineseName: string
  englishName: string
  chineseQualityDesc: string
  englishQualityDesc: string
  chinesePackMode: string
  englishPackMode: string
  businessPersonId: number
  goodsPersonId: number
  isOwnBrand: number
  isOemSample: number
}

//查询销售项目列表数据
export const getSaleProjectPageApi = (params: MarketPageReqVO) => {
  //判断是否有部门查询
  const deptId = sessionStorage.getItem('deptId')
  if (deptId !== null && deptId !== undefined) {
    params.deptId = deptId
  }
  if (params.projectCategory == 1) {
    params.projectCategoryList = [1]
  }
  if (params.projectCategory == 2) {
    params.projectCategoryList = [2]
  }
  //只查项目的数据
  params.isTemplate = 2
  return request.get({url: '/project/manager/page', params}).then((res) => {
    const list = res.list
    for (let i = 0; i < list.length; i++) {
      list[i].index = i + 1
    }
    return res
  })
}

//项目问题下拉选择框（去除草稿状态）
export const getProjectChoicePageApi = (params: MarketPageReqVO) => {
  //判断是否有部门查询
  const deptId = sessionStorage.getItem('deptId')
  if (deptId !== null && deptId !== undefined) {
    params.deptId = deptId
  }
  if (params.projectCategory == 1) {
    params.projectCategoryList = [1]
  }
  if (params.projectCategory == 2) {
    params.projectCategoryList = [2]
  }
  //只查项目的数据
  params.isTemplate = 2
  //去除草稿状态
  params.a = 1
  return request.get({url: '/project/manager/page', params}).then((res) => {
    const list = res.list
    for (let i = 0; i < list.length; i++) {
      list[i].index = i + 1
    }
    return res
  })
}
export const getTaskProjectPageApi = (params: MarketPageReqVO) => {
  params.projectId = sessionStorage.getItem('projectId')
  //判断是否有部门查询
  const deptId = sessionStorage.getItem('deptId')
  if (deptId !== null && deptId !== undefined) {
    params.deptId = deptId
  }
  if (params.projectCategory == 1) {
    params.projectCategoryList = [1]
  }
  if (params.projectCategory == 2) {
    params.projectCategoryList = [2]
  }
  //只查项目的数据
  params.isTemplate = 2
  return request.get({url: '/project/manager/page', params}).then((res) => {
    const list = res.list
    for (let i = 0; i < list.length; i++) {
      list[i].index = i + 1
    }
    return res
  })
}

// 存储列表信息
export const createWorkspaceApi = (data: MarketPageReqVO) => {
  return request.post({ url: '/system/user/workspace', data })
}
//查询列表字段
export const getWorkspaceApi = (params: MarketPageReqVO) => {
  return request.get({ url: '/system/user/workspace', params })
}
export const getProjectCategoryPageApi = (params: MarketPageReqVO) => {
  if (params.projectCategory) {
    params.projectCategoryList = [params.projectCategory]
  }
  //判断一下日期框条件
  if (params.createTime !== null && params.createTime !== undefined) {
    const planType = typeof params.createTime
    if (planType === 'string') {
      //路由查询，转换格式
      params.createTime = params.createTime.split(',')
    }
    params.createTime[0] = params.createTime[0] + ' 00:00:00'
    params.createTime[1] = params.createTime[1] + ' 23:59:59'
    // delete params.createTime
  }
  if (params.costCreateTime !== null && params.costCreateTime !== undefined) {
    const planType = typeof params.costCreateTime
    console.log(planType, params.costCreateTime)
    if (planType === 'string') {
      //路由查询，转换格式
      params.costCreateTime = params.costCreateTime.split(',')
    }
    params.costCreateTime[0] = params.costCreateTime[0] + ' 00:00:00'
    params.costCreateTime[1] = params.costCreateTime[1] + ' 23:59:59'
    // delete params.createTime
  }
  if (params.planTime !== null && params.planTime !== undefined) {
    const planType = typeof params.planTime
    if (planType === 'string') {
      //路由查询，转换格式
      params.planTime = params.planTime.split(',')
    }
    params.planStartTime = params.planTime[0] + ' 00:00:00'
    params.planEndTime = params.planTime[1] + ' 23:59:59'
    delete params.planTime
  }
  if (params.actualTime !== null && params.actualTime !== undefined) {
    const actualTime = typeof params.actualTime
    if (actualTime === 'string') {
      //路由查询，转换格式
      params.actualTime = params.actualTime.split(',')
    }
    params.actualStartTime = params.actualTime[0] + ' 00:00:00'
    params.actualEndTime = params.actualTime[1] + ' 23:59:59'
    delete params.actualTime
  }
  params.isTemplate = 2
  return request.get({url: '/project/manager/page', params}).then((res) => {
    const list = res.list
    for (let i = 0; i < list.length; i++) {
      list[i].index = i + 1
    }
    return res
  })
}
/**
  * Author： 许祎婷
  * Time： 2025/04/14
  * Description：項目成本分頁
*/
export const getProjectCostPageApi = (params: MarketPageReqVO) => {
  if (params.projectCategory) {
    params.projectCategoryList = [params.projectCategory]
  }
  //判断一下日期框条件
  if (params.createTime !== null && params.createTime !== undefined) {
    const planType = typeof params.createTime
    if (planType === 'string') {
      //路由查询，转换格式
      params.createTime = params.createTime.split(',')
    }
    params.createTime[0] = params.createTime[0] + ' 00:00:00'
    params.createTime[1] = params.createTime[1] + ' 23:59:59'
    // delete params.createTime
  }
  if (params.costCreateTime !== null && params.costCreateTime !== undefined) {
    const planType = typeof params.costCreateTime
    if (planType === 'string') {
      //路由查询，转换格式
      params.costCreateTime = params.costCreateTime.split(',')
    }
    params.costCreateTime[0] = params.costCreateTime[0] + ' 00:00:00'
    params.costCreateTime[1] = params.costCreateTime[1] + ' 23:59:59'
    // delete params.createTime
  }
  if (params.planTime !== null && params.planTime !== undefined) {
    const planType = typeof params.planTime
    if (planType === 'string') {
      //路由查询，转换格式
      params.planTime = params.planTime.split(',')
    }
    params.planStartTime = params.planTime[0] + ' 00:00:00'
    params.planEndTime = params.planTime[1] + ' 23:59:59'
    delete params.planTime
  }
  if (params.actualTime !== null && params.actualTime !== undefined) {
    const actualTime = typeof params.actualTime
    if (actualTime === 'string') {
      //路由查询，转换格式
      params.actualTime = params.actualTime.split(',')
    }
    params.actualStartTime = params.actualTime[0] + ' 00:00:00'
    params.actualEndTime = params.actualTime[1] + ' 23:59:59'
    delete params.actualTime
  }
  params.isTemplate = 2
  return request.get({url: '/project/manager/page-estimate', params}).then((res) => {
    const list = res.list
    for (let i = 0; i < list.length; i++) {
      list[i].index = i + 1
    }
    return res
  })
}

export const getProjectPricePageApi = (params: MarketPageReqVO) => {
  if (params.projectCategory) {
    params.projectCategoryList = [params.projectCategory]
  }
  params.isTemplate = 2
  return request.get({url: '/project/manager/pricePage', params}).then((res) => {
    const list = res.list
    for (let i = 0; i < list.length; i++) {
      list[i].index = i + 1
    }
    return res
  })
}

export const getLinkPlanPageApi = (params: MarketPageReqVO) => {
  //判断是否有部门查询
  const deptId = sessionStorage.getItem('deptId')
  if (deptId !== null && deptId !== undefined) {
    params.deptId = deptId
  }
  params.projectCategoryList = [1, 2]
  if (params.projectCategory) {
    params.projectCategoryList = [params.projectCategory]
  }
  //判断一下日期框条件
  if (params.planTime !== null && params.planTime !== undefined) {
    const planType = typeof params.planTime
    if (planType === 'string') {
      //路由查询，转换格式
      params.planTime = params.planTime.split(',')
    }
    params.planStartTime = params.planTime[0] + ' 00:00:00'
    params.planEndTime = params.planTime[1] + ' 23:59:59'
    delete params.planTime
  }
  if (params.actualTime !== null && params.actualTime !== undefined) {
    const actualTime = typeof params.actualTime
    if (actualTime === 'string') {
      //路由查询，转换格式
      params.actualTime = params.actualTime.split(',')
    }
    params.actualStartTime = params.actualTime[0] + ' 00:00:00'
    params.actualEndTime = params.actualTime[1] + ' 23:59:59'
    delete params.actualTime
  }
  params.isTemplate = 2
  params.projectNature = 1022
  return request.get({url: '/project/manager/page', params}).then((res) => {
    const list = res.list
    for (let i = 0; i < list.length; i++) {
      list[i].index = i + 1
    }
    return res
  })
}
export const getProjectPageApi = (params) => {
  //判断是否有部门查询
  const deptId = sessionStorage.getItem('deptId')
  if (deptId !== null && deptId !== undefined) {
    params.deptId = deptId
  }
  params.projectCategoryList = [1, 2]
  if (params.projectCategory) {
    params.projectCategoryList = [params.projectCategory]
  }
  //判断一下日期框条件
  if (params.planTime !== null && params.planTime !== undefined) {
    const planType = typeof params.planTime
    if (planType === 'string') {
      //路由查询，转换格式
      params.planTime = params.planTime.split(',')
    }
    params.planStartTime = params.planTime[0] + ' 00:00:00'
    params.planEndTime = params.planTime[1] + ' 23:59:59'
    delete params.planTime
  }
  if (params.actualTime !== null && params.actualTime !== undefined) {
    const actualTime = typeof params.actualTime
    if (actualTime === 'string') {
      //路由查询，转换格式
      params.actualTime = params.actualTime.split(',')
    }
    params.actualStartTime = params.actualTime[0] + ' 00:00:00'
    params.actualEndTime = params.actualTime[1] + ' 23:59:59'
    delete params.actualTime
  }
  params.isTemplate = 2
  params.projectNotNature = 1022
  return request.get({url: '/project/manager/page', params}).then((res) => {
    const list = res.list
    for (let i = 0; i < list.length; i++) {
      list[i].index = i + 1
    }
    return res
  })
}

export const getLinkProjectPlanPageApi = (params: PlanPageReqVO) => {
  params.primaryObjectId = parseInt(<string>sessionStorage.getItem("marketId"))
  //判断是否有部门查询
  const deptId = sessionStorage.getItem('deptId')
  if (deptId !== null && deptId !== undefined) {
    params.deptId = deptId
  }
  //判断一下日期框条件
  if (params.planTime !== null && params.planTime !== undefined) {
    const planType = typeof params.planTime
    if (planType === 'string') {
      //路由查询，转换格式
      params.planTime = params.planTime.split(',')
    }
    params.planStartTime = params.planTime[0]
    params.planEndTime = params.planTime[1]
    delete params.planTime
  }
  if (params.actualTime !== null && params.actualTime !== undefined) {
    const actualTime = typeof params.actualTime
    if (actualTime === 'string') {
      //路由查询，转换格式
      params.actualTime = params.actualTime.split(',')
    }
    params.actualStartTime = params.actualTime[0]
    params.actualEndTime = params.actualTime[1]
    delete params.actualTime
  }
  params.linkType = "scheme_associated_project"
  return request.get({url: '/common/entity-link/project-page', params}).then((res) => {
    const list = res.list
    for (let i = 0; i < list.length; i++) {
      list[i].index = i + 1
    }
    return res
  })
}
export const getAddOrder = (params: PlanPageReqVO) => {
  params.primaryObjectId = parseInt(<string>sessionStorage.getItem("marketId"))
  //判断是否有部门查询
  const deptId = sessionStorage.getItem('deptId')
  if (deptId !== null && deptId !== undefined) {
    params.deptId = deptId
  }
  //判断一下日期框条件
  if (params.planTime !== null && params.planTime !== undefined) {
    const planType = typeof params.planTime
    if (planType === 'string') {
      //路由查询，转换格式
      params.planTime = params.planTime.split(',')
    }
    params.planStartTime = params.planTime[0]
    params.planEndTime = params.planTime[1]
    delete params.planTime
  }
  if (params.actualTime !== null && params.actualTime !== undefined) {
    const actualTime = typeof params.actualTime
    if (actualTime === 'string') {
      //路由查询，转换格式
      params.actualTime = params.actualTime.split(',')
    }
    params.actualStartTime = params.actualTime[0]
    params.actualEndTime = params.actualTime[1]
    delete params.actualTime
  }
  params.linkType = "scheme_associated_project_add"
  return request.get({url: '/common/entity-link/project-page', params}).then((res) => {
    const list = res.list
    for (let i = 0; i < list.length; i++) {
      list[i].index = i + 1
    }
    return res
  })
}
export const getLinkProjectPageApi = (params: PlanPageReqVO) => {
  params.primaryObjectId = parseInt(<string>sessionStorage.getItem("marketId"))
  //判断是否有部门查询
  const deptId = sessionStorage.getItem('deptId')
  if (deptId !== null && deptId !== undefined) {
    params.deptId = deptId
  }
  //判断一下日期框条件
  if (params.planTime !== null && params.planTime !== undefined) {
    const planType = typeof params.planTime
    if (planType === 'string') {
      //路由查询，转换格式
      params.planTime = params.planTime.split(',')
    }
    params.planStartTime = params.planTime[0]
    params.planEndTime = params.planTime[1]
    delete params.planTime
  }
  if (params.actualTime !== null && params.actualTime !== undefined) {
    const actualTime = typeof params.actualTime
    if (actualTime === 'string') {
      //路由查询，转换格式
      params.actualTime = params.actualTime.split(',')
    }
    params.actualStartTime = params.actualTime[0]
    params.actualEndTime = params.actualTime[1]
    delete params.actualTime
  }
  params.linkType = "solution_associated_project"
  return request.get({url: '/common/entity-link/project-page', params}).then((res) => {
    const list = res.list
    for (let i = 0; i < list.length; i++) {
      list[i].index = i + 1
    }
    return res
  })
}

export const createLinkProjectPlanApi = (data: PlanPageReqVO) => {
  return request.post({url: '/common/entity-link/create-project', data})
}
export const getProjectTaskPageApi = (params: MarketPageReqVO) => {
  //判断是否有部门查询
  // const deptId = sessionStorage.getItem('deptId')
  // const creatorDeptId=sessionStorage.getItem('creatorDeptId')
  // if(deptId !== null && deptId !== undefined) {
  //   params.chargePersonDeptId = deptId
  // }
  // if(creatorDeptId !== null && creatorDeptId !== undefined) {
  //   params.creatorDeptId = creatorDeptId
  // }
  //判断一下日期框条件
  if (params.planTime !== null && params.planTime !== undefined) {
    const planType = typeof params.planTime
    if (planType === 'string') {
      //路由查询，转换格式
      params.planTime = params.planTime.split(',')
    }
    params.planStartTime = params.planTime[0] + ' 00:00:00'
    params.planEndTime = params.planTime[1] + ' 23:59:59'
    delete params.planTime
  }
  if (params.actualTime !== null && params.actualTime !== undefined) {
    const actualTime = typeof params.actualTime
    if (actualTime === 'string') {
      //路由查询，转换格式
      params.actualTime = params.actualTime.split(',')
    }
    params.actualStartTime = params.actualTime[0] + ' 00:00:00'
    params.actualEndTime = params.actualTime[1] + ' 23:59:59'
    delete params.actualTime
  }
  console.log(params)
  return request.get({url: '/project/task/page', params}).then((res) => {
    const list = res.list
    for (let i = 0; i < list.length; i++) {
      list[i].index = i + 1
    }
    return res
  })
}

/**
  * Author： 许祎婷
  * Time： 2025/04/25
  * Description：问题任务查询
*/
export const getQuestionTaskListApi = (id) => {
  let data = {
    entityCode : 's_question_task_create',
    objectId: id,
    taskCategory: 1
  }
  return request.post({url: '/project/task/list', data})
}

export const getProjectTemplatePageApi = (params: MarketPageReqVO) => {
  // params.projectCategoryList=[1,2]
  if (params.projectCategory) {
    params.projectCategoryList = [params.projectCategory]
  }
  params.isTemplate = 1
  return request.get({url: '/project/manager/page', params}).then((res) => {
    const list = res.list
    for (let i = 0; i < list.length; i++) {
      list[i].index = i + 1
    }
    return res
  })
}

export const getProjectOrderApi = async (projectId: number) => {
  return await request.get({url: '/project/manager/page-son?projectId=' + projectId })
}

export const getProjectQuestionPageApi = (params: MarketPageReqVO) => {
  params.projectCategoryList = [3]
  return request.get({url: '/project/manager/page', params}).then((res) => {
    const list = res.list
    for (let i = 0; i < list.length; i++) {
      list[i].index = i + 1
    }
    return res
  })
}
export const getHomeProjectPageApi = (params: MarketPageReqVO) => {
  if (params.planTime !== null && params.planTime !== undefined) {
    const planType = typeof params.planTime
    if (planType === 'string') {
      //路由查询，转换格式
      params.planTime = params.planTime.split(',')
    }
    params.planStartTime = params.planTime[0] + ' 00:00:00'
    params.planEndTime = params.planTime[1] + ' 23:59:59'
    delete params.planTime
  }
  // params.chargePersonId = parseInt(<string>localStorage.getItem("userId"))
  params.isTemplate = 2
  return request.get({url: '/project/manager/page', params}).then((res) => {
    const list = res.list
    for (let i = 0; i < list.length; i++) {
      list[i].index = i + 1
    }
    return res
  })
}
export const getHomeTaskNumberApi = () => {
  return request.get({url: '/project/task/task-overview'})
}

export const getProjectRiskPageApi = (params: MarketPageReqVO) => {
  params.projectStatusList = [995, 997]
  return request.get({url: '/project/manager/page', params}).then((res) => {
    const list = res.list
    for (let i = 0; i < list.length; i++) {
      list[i].index = i + 1
    }
    return res
  })
}
// 查询单条销售项目详情
export const getSaleProjectApi = async (id: number, isTemplate: number) => {
  return await request.get({url: '/project/manager/get?id=' + id + '&isTemplate=' + isTemplate})
}

export const getSaleProjectProcessApi = async (id: number, isTemplate: number) => {
  return await request.get({url: '/project/manager/get?id=' + id + '&isTemplate=' + isTemplate})
}
export const getSaleProjectTemplateApi = async (id: number, isTemplate: number) => {
  return await request.get({url: '/project/manager/get?id=' + id + '&isTemplate=' + isTemplate})
}
// // 发布
// export const releaseVersionApi = async (params: releaseVO) => {
//   return await request.post({ url: '/stuff/version/release' , params })
// }

//单条删除销售项目
export const delSaleProjectApipi = async (id: number) => {
  return await request.delete({url: '/project/manager/delete?id=' + id})
}
/**
  * Author： 许祎婷
  * Time： 2025/04/25
  * Description：删除
*/
//单条删除销售项目
export const delQusetionTaskApipi = async (str) => {
  return await request.delete({url: '/project/task/delete?' + str})
}

//删除关联方案
export const delLinkProjectPlanApi = async (id: number) => {
  return await request.delete({url: '/common/entity-link/delete?id=' + id})
}
export const delLinkProjectPlanListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({url: '/common/entity-link/delete-batch?ids=' + ids})
}
// 批量删除销售项目
export const delSaleProjectListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({url: '/project/manager/delete-batch?ids=' + ids})
}

//根据部门查询用户
export const getDeptUserApi = (params) => {
  return request.get({url: '/system/user/page', params}).then((res) => {
    const list = res.list
    for (let i = 0; i < list.length; i++) {
      list[i].index = i + 1
    }
    return res
  })
}

//获取负责人下拉框数据
export const getUserApi = async () => {
  return await request.get({url: '/system/user/list'})
}

// 导出所有项目
export const exportProjectApi = (params: ReportPageReqVO) => {
  params.isTemplate = 2
  return request.download({url: '/project/manager/export-excel', params})
}
export const getJuniorOrderApi = async () => {
  return await request.get({url: '/project/task/junior-order'})
}
export const getExportListApi = async (params) => {
  return await request.download({ url: "/project/task/export-excel", params })
}
export const getFileCountApi = async (params: ReportPageReqVO) => {
  return await request.get({url: '/project/manager/getFileCount', params })
}

//获取项目成本
export const getProjectPrice = async (id: number) => {
  return await request.get({url: '/project/manager/getProjectPrice?id=' + id})
}

// 获取项目成本
// 获取项目成本
export const getProjectPagePriceApi = async (params: MarketPageReqVO) => {
  if (params.planTime !== null && params.planTime !== undefined) {
    const planType = typeof params.planTime;
    if (planType === 'string') {
      // Convert routing query format
      params.planTime = params.planTime.split(',');
    }
    params.planStartTime = params.planTime[0] + ' 00:00:00';
    params.planEndTime = params.planTime[1] + ' 23:59:59';
    delete params.planTime;
  }
  params.isTemplate = 2;

  const response = await request.get({ url: '/project/manager/page', params });
  const list = response.list;

  // Use Promise.all to fetch project prices concurrently
  await Promise.all(
    list.map(async (project) => {
      const priceData = await getProjectPrice(project.id);
      project.estimatePrice = priceData.estimatePrice;
      project.actualPrice = priceData.actualPrice;
    })
  );

  return response;
};
