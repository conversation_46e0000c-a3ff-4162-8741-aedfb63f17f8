import request from "@/config/axios";

// @ts-ignore

export interface MeetVO {
  projectId:number
  objectId: number
  meetTitle:string
  meetTime:Date[]
  meetMc:number
  meetAddress:string
  meetInfo:string
  id:number
}
export interface FilePageReqVO extends PageParam {
  entityCode: string;
  objectId:number
  path?: string
  type?: string
  createTime?: Date[]
}
export interface MeetPageReqVO extends PageParam {
  projectId:number
}

//转化为经验教训
export const createMeetApi = (data: MeetVO) => {
  return request.post({url: '/project/meet/create', data})
}

//获取负责人下拉框数据
export const getUserApi = async () => {
  return await request.get({ url: '/system/user/list'})
}

// 获取下拉框数据
export const getSelectApi = async (typeCodes: string) => {
  return await request.get({ url: "/common/type/getMapTypeCodes?typeCodes=" + typeCodes })
}

//经验教训列表查询
export const getMeetPageApi = (params: MeetVO) => {
  return  request.get({ url: '/project/meet/page', params }).then((res)=>{
    const list = res.list
    for(let i = 0; i<list.length;i++){
      list[i].index=i + 1
    }
    return res
  })
}
//项目经验教训列表查询
export const getProjectMeetPageApi = (params: MeetPageReqVO) => {
  params.projectId=parseInt(<string>sessionStorage.getItem("marketId"))
  if(params.mcPeopleList!=null&&params.mcPeopleList!=undefined){
    let arr=[params.mcPeopleList]
    params.mcPeopleIdList=arr.map(item => item)
  }
  if (params.meetTime!=null||params.meetTime!=undefined){
    params.meetTimeStart=params.meetTime[0]+" 00:00:00"
    params.meetTimeEnd =params.meetTime[1]+" 23:59:59"
    delete params.meetTime[0]
    delete params.meetTime[1]

  }
  return  request.get({ url: '/project/meet/page', params }).then((res)=>{
    const list = res.list
    for(let i = 0; i<list.length;i++){
      list[i].index=i + 1
    }
    return res
  })

}

//经验教训修改
export const updateMeetApi = (data: MeetVO) => {
  return request.put({url: '/project/meet/update', data})
}
// 单条查询经验教训
export const getMeetApi = async (id: number,isTemplate: number) => {
  return await request.get({ url: '/project/meet/get?id=' + id +'&isTemplate='+isTemplate})
}

// 删除
export const deleteMeetApi = async (id: number) => {
  return await request.delete({ url: '/project/meet/delete?id=' + id })
}
// 查询文件列表
export const getFilePageApi = (params: FilePageReqVO) => {
  params.entityCode = 'p_project_meet_file_link'
  const objectId = sessionStorage.getItem(`meetId`)
  params.objectId = parseInt(<string>objectId)
  return request.post({ url: '/infra/file/list', params }).then((res)=>{
    const list = res.list
    let updateUrl = import.meta.env.VITE_BASE_URL?import.meta.env.VITE_BASE_URL+import.meta.env.VITE_SERVICE_PORT+import.meta.env.VITE_API_URL:'http://'+window.location.hostname+import.meta.env.VITE_SERVICE_PORT+import.meta.env.VITE_API_URL
    if(list){
      for(let i = 0; i<list.length;i++){
        list[i].url=updateUrl+list[i].url;
      }
    }
    return res
  })
}
export const deleteFileApi = (id: number) => {
  return request.delete({ url: '/infra/file/delete?id=' + id })
}
// 批量删除
export const delMeetListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/project/meet/delete-batch?ids=' + ids })
}
