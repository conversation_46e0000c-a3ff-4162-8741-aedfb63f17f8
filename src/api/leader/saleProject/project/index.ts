// 查询租户列表
import request from "@/config/axios";
// @ts-ignore


export interface ProjectPageReqVO  {
  isTemplate?:number
  objectId?: number;
  entityCode?: string
  pageSize: number
  pageNo: number
}

export interface AfterChargePersonPageReqVO  {
  customerId?: number;
  chargePersonId?: string
  pageSize: number
  pageNo: number
}

export interface ProjectVO {
  objectId: number
  entityCode: string
  partVersionId: number
  userId: number
  roleId: number
  pppDesc: string
}

export interface AfterSalesPerson {
  customerId: number
  chargePersonId: string
  roleId: number
}

export interface CitingOtherProjectVO {
  sourceId:number
  targetId:number
}
export interface ProjectOverViewTaskPageReqVO extends PageParam {
 projectId:number
  taskId?:number
}

export interface ProjectOverViewVO extends PageParam{
  entityCode:string
  objectId:number
  taskCode:string
  taskName:string
  taskCategory:number
  taskType:number
  taskDesc:string
  chargePersonId:number
  taskStatus:number
  planStartTime:Date[]
  planEndTime:Date[]
  actualStartTime:Date[]
  actualEndTime:Date[]
  receiveTime:Date[]
  sendTime:Date[]
  isMilestone:number
  parentTaskId:string
  preTaskData:string
  createTime:Date[]
  taskMainType:number
  projectCategory:number
  projectName:string
  projectCode:string
  projectCategoryList:string
  challengeName:string
  enjoyUserId:number
}

// 查询获取需要的项目code
export const getProjectCodeApi = async () => {
  return  await request.get({ url: 'sales/project/code' })
}

// 查询项目小组列表
export const getProjectGroupPageApi = async (params: ProjectPageReqVO) => {
    params.entityCode = 't_task_entity_participant_code'
    const objectId = parseInt(<string>sessionStorage.getItem(`marketId`))
    // @ts-ignore
    params.objectId = parseInt(objectId)
     return  request.get({ url: '/project/entity-participant/page', params })
}
export const getProjectGroupsPageApi = async (params: ProjectPageReqVO) => {

  return  request.get({ url: '/project/entity-participant/page', params })
}

// 查询售后负责人列表
export const getAfterSalesPersonPageApi = async (params: AfterChargePersonPageReqVO) => {
  return  request.get({ url: '/common/afterSalesPerson/page', params })
}

// 查询项目模版小组列表
export const getProjectTemplateGroupPageApi = async (params: ProjectPageReqVO) => {
  params.isTemplate=1
  params.entityCode = 't_task_entity_participant_code'
  const objectId = parseInt(<string>sessionStorage.getItem(`marketId`))
  // @ts-ignore
  params.objectId = parseInt(objectId)
  return  request.get({ url: '/project/entity-participant/page', params })
}
export const getProjectGroupPagesApi = async (params: ProjectPageReqVO) => {

     return  request.get({ url: '/project/entity-participant/page-list', params })
}

//获取用户下拉框数据
export const getUserApi = async () => {
  return await request.get({ url: '/system/user/list'})
}
// 获取下拉框数据
export const getSelectApi = async (typeCodes: string) => {
  return await request.get({ url: "/common/type/getMapTypeCodes?typeCodes=" + typeCodes })
}

//获取角色下拉框数据
export const getRoleApi = async (type: number) => {
  return await request.get({ url: '/system/role/list-all-simple?type=' + type })
}


// 新增项目小组信息
export const createProjectGroupApi = (data: ProjectVO) => {
  return request.post({ url: '/project/entity-participant/create', data })
}

// 新增售后人员信息
export const createAfterSalesPersonApi = (data: ProjectVO) => {
  return request.post({ url: '/common/afterSalesPerson/create', data })
}

// 修改售后人员信息
export const updateAfterSalesPersonApi = (data: ProjectVO) => {
  return request.put({ url: '/common/afterSalesPerson/update', data })
}

// 修改项目小组信息
export const updateProjectGroupApi = (data: ProjectVO) => {
  return request.put({ url: '/project/entity-participant/update', data })
}

//单条查询
export const getProjectGroupApi = async (id: number) => {
  return await request.get({ url: '/project/entity-participant/get?id=' + id })
}
//单条售后负责人查询
export const getCustomerUserApi = async (id: number) => {
  return await request.get({ url: '/common/afterSalesPerson/get?id=' + id })
}

//单条删除
export const delProjectGroupApi = async (id: number) => {
  return await request.delete({ url: '/project/entity-participant/delete?id=' + id })
}

//客户售后负责人删除
export const delCustomerUserApi = async (id: number) => {
  return await request.delete({ url: '/common/afterSalesPerson/delete?id=' + id })
}

// 批量删除
export const delCustomerUserListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/common/afterSalesPerson/delete-batch?ids=' + ids })
}

// 批量删除
export const delProjectGroupListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/project/entity-participant/delete-batch?ids=' + ids })
}
//引入其他项目
export const citingOtherProjectsApi = (data: CitingOtherProjectVO) => {
  return request.post({ url: '/project/task/moveGantt', data })
}

//项目总览列表查询
export const getProjectOverViewApi = async (data: ProjectOverViewVO) => {
  // @ts-ignore
  return  request.post({ url: '/project/task/overview', data })
}
//项目总览任务列表查询
export const getProjectViewTaskPageApi = async (data: ProjectOverViewTaskPageReqVO) => {
  // params.projectId=parseInt(<string>sessionStorage.getItem('taskProjectId'))
  return  request.post({ url: '/project/task/uncompleted', data })
}
export const getProjectViewTasksPageApi = async (params: ProjectOverViewTaskPageReqVO) => {
  // params.projectId=parseInt(<string>sessionStorage.getItem('taskProjectId'))
  return  request.get({ url: '/project/task/uncompleted-page', params })
}
export const getProjectFristTaskViewTaskPageApi = async (data: ProjectOverViewTaskPageReqVO) => {
  // params.projectId=parseInt(<string>sessionStorage.getItem('taskProjectId'))
  return  request.post({ url: '/project/task/task-uncompleted', data })
}

// 新增项目人员工时
export const createPersonWorkHourApi = (data: ProjectOverViewVO) => {
  return request.post({ url: '/project/task/createList', data })
}
