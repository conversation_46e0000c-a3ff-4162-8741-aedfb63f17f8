import request from '@/config/axios'

export interface RegisterVO {
  projectCode: string,
  projectPhase: number,
  projectName: string,
  projectType: number,
  orgId: number,
  taskId: number,
  estAmount: number,
  currency: string,
  projectManager: number,
  reportTo: number,
  remark: string,
  billingCode: string,
  processingCode: string,
  orderCode: string,
  projectNature: number,
  creator: string,
  createTime: Date[]
}

export interface FileVO {
  entityCodeStr: string
  objectId: number
  taskCode: string
  taskName: string
}


// 查询单条销售项目
export const getSalesProjectsApi = async (id: number, isTemplate: number) => {
  isTemplate = 1
  return await request.get({url: '/project/manager/get?id=' + id + '&isTemplate=' + isTemplate})
}

// 新增销售项目信息
export const createSalesProjectApi = async (data: RegisterVO) => {
  return await request.post({url: '/project/manager/create', data})
}

//方案转订单
export const schemeToOrderApi = async (data: RegisterVO) => {
  return await request.post({url: '/project/manager/schemeToOrder', data})
}

// 修改销售项目信息
export const updateSalesProjectApi = async (data: RegisterVO) => {
  if (data.planEndTime !== null && data.planEndTime !== undefined) {
    // 将时间戳转换为日期对象
    const date = new Date(data.planEndTime);
    // 将时间设置为指定日期的最后一秒（23:59:59）
    date.setHours(23, 59, 59 );
    data.planEndTime = date.getTime();
  }
  // if (data.actualEndTime !== null && data.actualEndTime !== undefined) {
  //   // 将时间戳转换为日期对象
  //   const date = new Date(data.actualEndTime);
  //   // 将时间设置为指定日期的最后一秒（23:59:59）
  //   date.setHours(23, 59, 59 );
  //   data.actualEndTime = date.getTime();
  // }
  return await request.put({url: '/project/manager/update', data});
}
// 复制创建项目下信息
export const copyProjectApi = async (id: String, type: number) => {
  return request.get({url: '/project/manager/copy?id=' + id + '&type=' + type})
}

// 复制创建项目下信息
export const copyProjectApi2 = async (id: String, type: number, planStartTime: number, planEndTime: number, isCopyTask: number) => {
  return request.get({url: '/project/manager/copy?id=' + id + '&type=' + type + '&planStartTime=' + planStartTime + '&planEndTime=' + planEndTime + '&isCopyTask=' + isCopyTask})
}

// 获取下拉框数据
export const getSelectApi = async (typeCodes: string) => {
  return await request.get({url: "/common/type/getMapTypeCodes?typeCodes=" + typeCodes})
}
//获取负责人下拉框数据
export const getUserApi = async () => {
  return await request.get({url: '/system/user/list'})
}

//查询供应商下拉框数据
export const getCustomerSelectListApi = (orgCategory: number) => {
  return request.get({url: '/common/org/list?orgCategory=' + orgCategory})
}

//获取客户列表
export const getOrgApi = async () => {
  return await request.get({url: '/common/org/list?orgCategory=0'})
}

// 查询文件列表
export const getFilePageApi = () => {
  const objectId = sessionStorage.getItem(`marketId`)
  let params = {
    entityCode: 'p_project_file',
    objectId: parseInt(<string>objectId)
  }
  return request.post({url: '/infra/file/list', params}).then((res) => {
    const list = res.list
    let updateUrl = 'http://' + window.location.hostname + import.meta.env.VITE_SERVICE_PORT + import.meta.env.VITE_API_URL
    if (list) {
      for (let i = 0; i < list.length; i++) {
        list[i].url = updateUrl + list[i].url;
        console.log(list)
      }
    }
    return res
  })
}

export const getProjectAndTaskFilePageApi = () => {
  const objectId = sessionStorage.getItem(`marketId`)
  let params = {
    entityCode: 'p_project_file',
    objectId: parseInt(<string>objectId)
  }
  return request.post({url: '/infra/file/project-task-list', params}).then((res) => {
    const list = res.list
    let updateUrl = 'http://' + window.location.hostname + import.meta.env.VITE_SERVICE_PORT + import.meta.env.VITE_API_URL
    if (list) {
      for (let i = 0; i < list.length; i++) {
        list[i].url = updateUrl + list[i].url;
        console.log(list)
      }
    }
    return res
  })
}

// 查询客户文件列表
export const getCustomerFilePageApi = () => {
  const objectId = sessionStorage.getItem(`CustomerId`)
  let params = {
    entityCode: 'g_org_file',
    objectId: parseInt(<string>objectId)
  }
  return request.post({url: '/infra/file/list', params}).then((res) => {
    const list = res.list
    let updateUrl = 'http://' + window.location.hostname + import.meta.env.VITE_SERVICE_PORT + import.meta.env.VITE_API_URL
    if (list) {
      for (let i = 0; i < list.length; i++) {
        list[i].url = updateUrl + list[i].url;
        console.log(list)
      }
    }
    return res
  })
}


// 查询文件列表
export const getChallengeFilePageApi = () => {
  const objectId = sessionStorage.getItem(`challengeId`)
  if (objectId == null || objectId == undefined || objectId == "NAN") {
    return false
  }
  let params = {
    entityCode: 'p_challenge_file',
    objectId: parseInt(<string>objectId)
  }
  return request.post({url: '/infra/file/list', params}).then((res) => {
    const list = res.list
    let updateUrl = 'http://' + window.location.hostname + import.meta.env.VITE_SERVICE_PORT + import.meta.env.VITE_API_URL
    if (list) {
      for (let i = 0; i < list.length; i++) {
        list[i].url = updateUrl + list[i].url;
        console.log(list)
      }
    }
    return res
  })
}


// 查询项目以及任务文件列表
export const getProjectTaskFilePageApi = (params: FileVO) => {
  const objectId = sessionStorage.getItem(`marketId`)
  params.entityCodeStr = 'p_project_file,p_task_file'
  params.objectId = parseInt(<string>objectId)
  return request.post({url: '/infra/file/project-task-list', params})
}

// 删除文件
export const deleteFileApi = (id: number) => {
  return request.delete({url: '/infra/file/delete?id=' + id})
}

