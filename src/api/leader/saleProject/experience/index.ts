import request from "@/config/axios";

// @ts-ignore

export interface ExperienceVO {
  projectId:number
  objectId: number
  projectSource:number
  chargePersonId:number
  partVersionId: number
  summarizeTime:Date[]
  experienceSummarize:string
  experienceCode:string
  experienceName:string
  questionDesc:string
  questionAnalyze:string
  solution:string
}
export interface FilePageReqVO extends PageParam {
  entityCode: string
  objectId:number
  path?: string
  type?: string
  createTime?: Date[]
}
export interface ExperiencePageReqVO extends PageParam {
  projectId:number
}

//转化为经验教训
export const createExperienceApi = (data: ExperienceVO) => {
  return request.post({url: '/project/experience/create', data})
}

//获取负责人下拉框数据
export const getUserApi = async () => {
  return await request.get({ url: '/system/user/list'})
}

// 获取下拉框数据
export const getSelectApi = async (typeCodes: string) => {
  return await request.get({ url: "/common/type/getMapTypeCodes?typeCodes=" + typeCodes })
}

//经验教训列表查询
export const getExperiencePageApi = (params: ExperienceVO) => {
  return  request.get({ url: '/project/experience/page', params }).then((res)=>{
    const list = res.list
    for(let i = 0; i<list.length;i++){
      list[i].index=i + 1
    }
    return res
  })
}
//项目经验教训列表查询
export const getProjectExperiencePageApi = (params: ExperiencePageReqVO) => {
  params.projectId=parseInt(<string>sessionStorage.getItem("marketId"))

  return  request.get({ url: '/project/experience/page', params }).then((res)=>{
    const list = res.list
    for(let i = 0; i<list.length;i++){
      list[i].index=i + 1
    }
    return res
  })

}

//经验教训修改
export const updateExperienceApi = (data: ExperienceVO) => {
  return request.put({url: '/project/experience/update', data})
}
// 单条查询经验教训
export const getExperienceApi = async (id: number,isTemplate: number) => {
  return await request.get({ url: '/project/experience/get?id=' + id +'&isTemplate='+isTemplate})
}

// 删除
export const deleteExperienceApi = async (id: number) => {
  return await request.delete({ url: '/project/experience/delete?id=' + id })
}
// 查询文件列表
export const getFilePageApi = (params: FilePageReqVO) => {
  const objectId = sessionStorage.getItem(`experienceId`)
  if (objectId === null) {
    return console.log('不发接口，没id')
  } else {
    params.objectId = parseInt(<string>objectId)
    params.entityCode = 'p_project_experienced_lesson_file'
    return request.post({url: '/infra/file/list', params}).then((res) => {
      const list = res.list
      let updateUrl = 'http://' + window.location.hostname + import.meta.env.VITE_SERVICE_PORT + import.meta.env.VITE_API_URL
      if (list) {
        for (let i = 0; i < list.length; i++) {
          list[i].url = updateUrl + list[i].url;
        }
      }
      return res
    })
  }
}
export const deleteFileApi = (id: number) => {
  return request.delete({ url: '/infra/file/delete?id=' + id })
}
// 批量删除
export const delExperienceListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/project/experience/delete-batch?ids=' + ids })
}
