import request from "@/config/axios";
export interface ReportPageReqVO  {
  projectNotNature:number;
  projectId: string | null;
  createTime: string
  createStartTime:string
  createEndTime:string
  chargePersonName:string;
  chargePersonDeptId:number;
  projectNature:number;
  deptId: string;
  creatorDeptId:number;
  actualStartTime: string;
  actualEndTime: string;
  actualTime: null;
  planTime: Boolean;
  planEndTime: string;
  planStartTime: string;
  isTemplate: number;
  projectStatusList: number[];
  projectCode?: string
  projectName?: string
  projectCategory:number
  projectCategoryList:any
  orgCode?: string
  orgName?: string
  chargePersonId?:number
  pageSize: number
  pageNo: number
  isUncompleted:number
  userId: string | null
  planDateStart: string;
  planDateEnd: string;
  actualDateStart: string;
  actualDateEnd: string;
  filed?:any,
  order?:any,
}

//查询生产报表列表
export const getReportPageApi = (params: ReportPageReqVO) => {
  if (params.planTime !== null && params.planTime !== undefined) {
    const planType = typeof params.planTime
    if(planType === 'string') {
      //路由查询，转换格式
      params.planTime = params.planTime.split(',')
    }
    params.planDateStart = params.planTime[0] + ' 00:00:00'
    params.planDateEnd = params.planTime[1] + ' 23:59:59'
    delete params.planTime
  }
  if (params.actualTime !== null && params.actualTime !== undefined) {
    const actualTime = typeof params.actualTime
    if(actualTime === 'string') {
      //路由查询，转换格式
      params.actualTime = params.actualTime.split(',')
    }
    params.actualDateStart = params.actualTime[0] + ' 00:00:00'
    params.actualDateEnd = params.actualTime[1] + ' 23:59:59'
    delete params.actualTime
  }

  return  request.get({ url: '/project/entity-participant/reportPage', params }).then((res)=>{
    const list = res.list
    for(let i = 0; i<list.length;i++){
      list[i].index=i + 1
    }
    return res
  })
}

//查询生产报表详情列表
export const getPeopleReportPageApi = (params: ReportPageReqVO) => {
  params.userId = sessionStorage.getItem('reportUserId')
  params.projectId = sessionStorage.getItem('reportId')
  if (params.planTime !== null && params.planTime !== undefined) {
    const planType = typeof params.planTime
    if(planType === 'string') {
      //路由查询，转换格式
      params.planTime = params.planTime.split(',')
    }
    params.planStartTime = params.planTime[0]+' 00:00:00'
    params.planEndTime = params.planTime[1]+' 23:59:59'
    delete params.planTime
  }
  if (params.actualTime !== null && params.actualTime !== undefined) {
    const actualTime = typeof params.actualTime
    if(actualTime === 'string') {
      //路由查询，转换格式
      params.actualTime = params.actualTime.split(',')
    }
    params.actualStartTime = params.actualTime[0]+' 00:00:00'
    params.actualEndTime = params.actualTime[1]+' 23:59:59'
    delete params.actualTime
  }
  return  request.get({ url: '/project/task/peopleTaskPage', params }).then((res)=>{
    const list = res.list
    for(let i = 0; i<list.length;i++){
      list[i].index=i + 1
    }
    return res
  })
}

// 导出生产报表
export const exportRefundApi = (params: ReportPageReqVO) => {
  return request.download({ url: '/project/entity-participant/export-report', params })
}

