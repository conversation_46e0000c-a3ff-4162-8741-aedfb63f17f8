import request from '@/config/axios'
import {VersionPageReqVO} from "@/api/leader/supplier";

export interface FileVO {
  id: number
  configId: number
  path: string
  name: string
  url: string
  size: string
  type: string
  createTime: Date
}

export interface FilePageReqVO extends PageParam {
  createTimeEnd: string;
  createTimeStart: string;
  path?: string
  type?: string
  createTime?: Date[]
  objectId?: number
  entityCode?: string
}


// 查询文件code字段
export const getFileCodeApi = () => {
  return request.get({ url: '/infra/file/code'})
}
// 查询文件列表
export const getFilePageApi = (params: FilePageReqVO) => {
    params.entityCode = 'g_org_file'
  const objectId = sessionStorage.getItem(`supplierId`)
    // @ts-ignore
  params.objectId = parseInt(objectId)
  //判断一下日期框条件
  if (params.createTime !== null && params.createTime !== undefined) {
    const planType = typeof params.createTime
    if(planType === 'string') {
      //路由查询，转换格式
      params.createTime = params.createTime.split(',')
    }
    params.createTimeStart = params.createTime[0] + ' 00:00:00'
    params.createTimeEnd = params.createTime[1] + ' 23:59:59'
    delete params.createTime
  }
  return request.post({ url: '/infra/file/list', params }).then((res)=>{
    const list = res.list
    let updateUrl = import.meta.env.VITE_BASE_URL?import.meta.env.VITE_BASE_URL+import.meta.env.VITE_SERVICE_PORT+import.meta.env.VITE_API_URL:'http://'+window.location.hostname+import.meta.env.VITE_SERVICE_PORT+import.meta.env.VITE_API_URL
    if(list){
      for(let i = 0; i<list.length;i++){
        list[i].url=updateUrl+list[i].url;
      }
    }
    return res
  })
}

// 查询文件列表
export const getCompanyFilePageApi = (params: FilePageReqVO) => {
  params.entityCode = 'g_org_file'
  const objectId = sessionStorage.getItem(`CompanyId`)
  params.objectId = parseInt(objectId)
  return request.post({ url: '/infra/file/list', params }).then((res)=>{
    const list = res.list
    let updateUrl = import.meta.env.VITE_BASE_URL?import.meta.env.VITE_BASE_URL+import.meta.env.VITE_SERVICE_PORT+import.meta.env.VITE_API_URL:'http://'+window.location.hostname+import.meta.env.VITE_SERVICE_PORT+import.meta.env.VITE_API_URL
    if(list){
      for(let i = 0; i<list.length;i++){
        list[i].url=updateUrl+list[i].url;
        console.log(list)
      }
    }
    return res
  })
}


// 查询索赔记录列表
export const getClaimPageApi = (params: FilePageReqVO) => {
  params.entityCode = null
  const objectId = sessionStorage.getItem(`supplierId`)
  // @ts-ignore
  params.orgId = parseInt(objectId)
  return request.get({ url: '/p/claim-org-link/page-org', params })
}


// 删除文件
export const deleteFileApi = (id: number) => {
  return request.delete({ url: '/infra/file/delete?id=' + id })
}
