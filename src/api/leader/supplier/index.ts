// 查询租户列表
import request from "@/config/axios";

export interface VersionPageReqVO extends PageParam {
  orgCategory: number;
  goodsCode?: string
  partVersionCode?: string
}

export interface releaseVO {
  id: number
}

export interface BrandVO {
  id: number
  partId: number
  goodsCode: string
  partVersionCode: string
  partVersionStatus: number
  partVersionName: string
  sapno: string
  chineseName: string
  englishName: string
  chineseQualityDesc: string
  englishQualityDesc: string
  chinesePackMode: string
  englishPackMode: string
  businessPersonId: number
  goodsPersonId: number
  isOwnBrand: number
  isOemSample: number
}

//查询供应商列表数据
export const getSupplierPageApi = (params: VersionPageReqVO) => {
  params.orgCategory = 1
  return request.get({ url: '/common/org/page', params })
}

// 查询供应商详情
export const getSupplierApi = async (id: number) => {
  return await request.get({ url: '/common/org/get?id=' + id })
}



//单条删除供应商
export const delSupplierApi = async (id: number) => {
  return await request.delete({ url: '/common/org/delete?id=' + id })
}

// // 批量删除供应商
export const delSupplierListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/common/org/delete-batch?ids=' + ids })
}

//查询供应商/客户列表数据（物料那边使用）
export const getOrgPageApi = (params: VersionPageReqVO) => {
  return request.get({ url: '/common/org/page', params })
}


