import request from '@/config/axios'

export interface BasicVO {
  orgCode?: String
  orgName?: String
  orgRank?: String
  orgCity?: String
  orgAddress?: String
  orgPv?: Number
  orgProductQuantityRan?: String
  orgTradePartner?: String
  orgCustomer?: String
  orgProductCert?: String
  orgBsci?: String
  orgDesc?: String
}



// 查询供应商详情
export const getSupplierBasicApi = async (id: number) => {
  return await request.get({ url: '/common/org/get?id=' + id })
}

// 新增供应商
export const createSupplierBasicApi = async (data: BasicVO) => {
  return await request.post({ url: '/common/org/create', data })
}
//
// 修改供应商
export const updateSupplierBasicApi = async (data: BasicVO) => {
  return await request.put({ url: '/common/org/update', data })
}

// 获取下拉框数据
export const getSelectApi = async (typeCodes: string) => {
  return await request.get({url: "/common/type/getMapTypeCodes?typeCodes=" + typeCodes})
}
//获取国家列表
export const getCountryListApi = async () => {
    return await request.get({url: "/country/config/list"})
}
//获取省份列表
export const getProvinceListApi = async (code: string) => {
    return await request.get({url: "/province/config/list?code=" + code})
}
// 查询供应商
export const getOrgApi = async () => {
  return await request.get({ url: '/common/org/list?orgCategory=1'})
}


