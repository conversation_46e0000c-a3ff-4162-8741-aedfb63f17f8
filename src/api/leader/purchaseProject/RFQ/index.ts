// 查询租户列表
import request from "@/config/axios";

export interface RFQVO {
  rfqCode: string
  rfqName: string
  rfqType: number
  rfqStatus: number
  rfqCurrentRound: number
  chargePersonId: number
  currency: number
  deptId: number
  cancelData: number
  rfqDesc: number
  createTime: number
}

export interface RFQPageReqVO extends PageParam {
  entityCode: string | null
  id?:number
}

export interface releaseVO {
  id: number
}

//查询rfq管理列表数据
export const getRFQPageApi = (params: RFQPageReqVO) => {
  let url = ''
  if(window.location.pathname == '/orgPortal/orgRFQ'){
    //发供应商RFQ接口
    params.entityCode = 'r_round_link'
    url =  '/project/rfq-round-inquiry/page-org'
  } else {
    params.entityCode = null
    url = '/project/request-for-quotation/page'
  }
  return  request.get({ url: url, params }).then((res)=>{
    const list = res.list
    if(list){
      for(let i = 0; i<list.length;i++){
        list[i].index=i + 1
      }
    }
    return res
  })
}

// 查询rfq基本信息详情
export const getRFQInfoApi = async (id: number) => {
  return await request.get({ url: '/project/request-for-quotation/get?id=' + id })
}

//rfq单条删除
export const delRFQApi = async (id: number) => {
  return await request.delete({ url: '/project/request-for-quotation/delete?id=' + id })
}

// rfq批量删除
export const delRFQListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/project/request-for-quotation/delete-batch?ids=' + ids })
}

// rfq新增
export const createRFQApi = async (data: RFQVO) => {
  return await request.post({ url: '/project/request-for-quotation/create', data })
}

// rfq修改
export const updateRFQApi = async (data: RFQVO) => {
  return await request.put({ url: '/project/request-for-quotation/update', data })
}

//获取负责人下拉框数据
export const getUserApi = async () => {
  return await request.get({ url: '/system/user/list'})
}

//获取供应商
export const getCustomerSelectListApi = (orgCategory:number) => {
  return request.get({ url: '/common/org/list?orgCategory=' + orgCategory})
}


