
import request from "@/config/axios";

export interface BidderListVO {
  rfqCode: string
  rfqName: string
  rfqType: number
  rfqStatus: number
  rfqCurrentRound: number
  chargePersonId: number
  currency: number
  deptId: number
  cancelData: number
  rfqDesc: number
  createTime: number
}

export interface BidderListPageReqVO extends PageParam {
  rfqId: number;
  id?:number
}



//查询供应商列表数据
export const getBidderListPageApi = (params: BidderListPageReqVO) => {
  // @ts-ignore
  params.rfqId = sessionStorage.getItem('RFQId')
  return  request.get({ url: '/project/bidder-list/page', params }).then((res)=>{
    const list = res.list
    if(list){
      for(let i = 0; i<list.length;i++){
        list[i].index=i + 1
      }
    }
    return res
  })
}
// 查询供应商基本信息详情
export const getBidderInfoApi = async (id: number) => {
  return await request.get({ url: '/project/bidder-list/get?id=' + id })
}


//单条供应商删除
export const delBidderApi = async (id: number) => {
  return await request.delete({ url: '/project/bidder-list/delete?id=' + id })
}

// 供应商批量删除
export const delBidderListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/project/bidder-list/delete-batch?ids=' + ids })
}

// 供应商新增
export const createBidderApi = async (data: BidderListVO) => {
  return await request.post({ url: '/project/bidder-list/create', data })
}

// 供应商修改
export const updateBidderApi = async (data: BidderListVO) => {
  return await request.post({ url: '/project/bidder-list/update', data })
}




