// 查询租户列表
import request from "@/config/axios";

export interface InquiryVO {
  inquiryName: string
  partVersionId: number
  factoryName: string
  outPort: string
  estimateQuantity: string
  chargePersonId: number
  publishData: string
  rfqId: number
}
export interface InquiryRfqVO {
  id: number
  rfqId: number
}

export interface linkVO {
  inquiryId: any
  projectId: number
  entityCode: string
}

export interface InquiryPageReqVO extends PageParam {
  entityCode: string | null
  objectId: number | null
  id?:number
}

export interface PurchaseItemPageReqVO extends PageParam {
  rfqId: string | null;
  id?:number
}


//查询询价任务列表数据
export const getInquiryTaskPageApi = (params: InquiryPageReqVO) => {
  params.entityCode = null
  params.objectId = null
  return  request.get({ url: '/project/inquiry/page', params }).then((res)=>{
    const list = res.list
    if(list){
      for(let i = 0; i<list.length;i++){
        list[i].index=i + 1
      }
    }
    return res
  })
}

//查询销售项目关联采购询价列表数据
export const getPurchaseInquiryRelatedToSalesItemsPageApi = (params: InquiryPageReqVO) => {
  params.entityCode = 'p_project_inquiry_link'
  params.objectId = parseInt(<string>sessionStorage.getItem('marketId'))
  return  request.get({ url: '/project/inquiry/page', params }).then((res)=>{
    const list = res.list
    if(list){
      for(let i = 0; i<list.length;i++){
        list[i].index=i + 1
      }
    }
    return res
  })
}

// 取消/关联项目
export const updateLinkApi = async (data) => {
  return await request.get({ url: '/project/inquiry/project-link' + data })
}


// 查询询价任务信息详情
export const getInquiryInfoApi = async (id: number) => {
  return await request.get({ url: '/project/inquiry/get?id=' + id })
}


//单条删除
export const delInquiryApi = async (id: number) => {
  return await request.delete({ url: '/project/inquiry/delete?id=' + id })
}

// 批量删除
export const delInquiryListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/project/inquiry/delete-batch?ids=' + ids })
}

// 新增
export const createInquiryApi = async (data: InquiryVO) => {
  return await request.post({ url: '/project/inquiry/create', data })
}

// 修改
export const updateInquiryApi = async (data: InquiryVO) => {
  return await request.put({ url: '/project/inquiry/update', data })
}

// 加入或移除rfq
export const addOrRemoveRFQApi = async (data: InquiryRfqVO) => {
  return await request.put({ url: '/project/inquiry/relevancy-rfq', data })
}

//获取负责人下拉框数据
export const getUserApi = async () => {
  return await request.get({ url: '/system/user/list'})
}
//获取供应商
export const getCustomerSelectListApi = (orgCategory:number) => {
  return request.get({ url: '/common/org/list?orgCategory=' + orgCategory})
}

//获取供应商
export const getRFQApi = () => {
  return request.get({ url: '/project/request-for-quotation/list'})
}

//查询清单列表数据
export const getPurchaseItemPageApi = (params: PurchaseItemPageReqVO) => {
  params.rfqId = sessionStorage.getItem('RFQId')
  return  request.get({ url: '/project/inquiry/page-rfq', params }).then((res)=>{
    const list = res.list
    if(list){
      for(let i = 0; i<list.length;i++){
        list[i].index=i + 1
      }
    }
    return res
  })
}


