// 查询租户列表
import request from "@/config/axios";

export interface SourcePlanVO {
  inquiryId:number
  planStartDate: string
  planEndDate: string
  actualStartDate: string
  actualEndDate: string
  status: number
}

export interface SourcePlanPageReqVO extends PageParam {
  inquiryId: string | null;
  id?:number
}

//查询寻源计划列表数据
export const getSourcePlanPageApi = (params: SourcePlanPageReqVO) => {
  params.inquiryId = sessionStorage.getItem('inquiryId')
  return  request.get({ url: '/project/sourcin-plan/page', params }).then((res)=>{
    const list = res.list
    if(list){
      for(let i = 0; i<list.length;i++){
        list[i].index=i + 1
      }
    }
    return res
  })
}
// 查询寻源计划信息详情
export const getSourcePlanInfoApi = async (id: number) => {
  return await request.get({ url: '/project/sourcin-plan/get?id=' + id })
}


//寻源计划信息单条删除
export const delSourcePlanApi = async (id: number) => {
  return await request.delete({ url: '/project/sourcin-plan/delete?id=' + id })
}

// 寻源计划信息批量删除
export const delSourcePlanListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/project/sourcin-plan/delete-batch?ids=' + ids })
}

// 寻源计划新增
export const createSourcePlanApi = async (data: SourcePlanVO) => {
  return await request.post({ url: '/project/sourcin-plan/create', data })
}

// 寻源计划信息修改
export const updateSourcePlanApi = async (data: SourcePlanVO) => {
  return await request.put({ url: '/project/sourcin-plan/update', data })
}




