// 查询租户列表
import request from "@/config/axios";
// @ts-ignore
import {BrandVO} from "@/api/leader/material/product/brand";


export interface VersionPageReqVO extends PageParam {
  ppvVersionStatus: any;
  partCategory: number;
  id?:number
}

export interface releaseVO {
  id: number
}

// export interface BrandVO {
//   id: number
//   partId: number
//   goodsCode: string
//   partVersionCode: string
//   partVersionStatus: number
//   partVersionName: string
//   sapno: string
//   chineseName: string
//   englishName: string
//   chineseQualityDesc: string
//   englishQualityDesc: string
//   chinesePackMode: string
//   englishPackMode: string
//   businessPersonId: number
//   goodsPersonId: number
//   isOwnBrand: number
//   isOemSample: number
// }

// let status = true

//查询物料版本列表数据
export const getPartVersionPageApi = (params: VersionPageReqVO) => {
  params.partCategory = 1
  return  request.get({ url: '/part/version/page', params }).then((res)=>{
    const list = res.list
    for(let i = 0; i<list.length;i++){
      list[i].index=i + 1
    }
    return res
  })
}
// 查询物料版本详情
export const getPartVersionApi = async (id: number) => {
  return await request.get({ url: '/part/version/get?id=' + id })
}
//
// // 发布
// export const releaseVersionApi = async (params: releaseVO) => {
//   return await request.post({ url: '/stuff/version/release' , params })
// }
//
// //单条删除
// export const delVersionApi = async (id: number) => {
//   return await request.delete({ url: '/stuff/version/delete?id=' + id })
// }
//
// // 批量删除
// export const delVersionListApi = (ids: string) => {
//   ids = ids.toString()
//   return request.delete({ url: '/stuff/version/delete-batch?ids=' + ids })
// }
//
// // 复制
// export const copyVersionApi = (data: BrandVO) => {
//   return request.post({ url: '/stuff/version/copy', data })
// }
//
// //升版
// export const upgradeVersionApi = async (data: releaseVO) => {
//   return await request.post({ url: '/stuff/version/upgrade' , data })
// }
