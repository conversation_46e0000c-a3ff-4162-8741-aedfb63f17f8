// 查询租户列表
import request from "@/config/axios";
import {RFQVO} from "@/api/leader/purchaseProject/RFQ";

export interface RfqRoundVO {
  id:number
  rfqId: number
  endTime: string
  closeTime: string
  status: number
  isNeedDetail: number
}

export interface ProductPageReqVO extends PageParam {
  objectId?: number | null
  entityCode?: string | null
}

export interface RfqRoundPageReqVO extends PageParam {
  objectId: string | null;
  entityCode: string;
  rfqId: number
  id?:number
  distributeTime:Date[]
  endTime:Date[]
  createTime:Date[]
  closeTime:Date[]
  status:number
  roundNumber:number
}

//查询RFQ列表数据
export const getRFQPageApi = (params: RfqRoundPageReqVO) => {
  let url = ''
  params.objectId = sessionStorage.getItem('RFQId')
  params.entityCode = 'r_round_link'
  if(window.location.pathname === '/orgPortal/RFQView') {
    //供应商，发供应商接口
    url = 'project/rfq-round/page-org'
  } else {
    url = 'project/rfq-round/page'
  }
  // @ts-ignore
  return  request.get({ url: url, params }).then((res)=>{
    const list = res.list
    if(list){
      for(let i = 0; i<list.length;i++){
        list[i].index=i + 1
      }
    }
    return res
  })
}
//查询销售报价列表数据
export const getMarketPageApi = (params: RfqRoundPageReqVO) => {
  // @ts-ignore
  params.objectId = sessionStorage.getItem('marketId')
  params.entityCode = 'p_round_project_link'
  return  request.get({ url: '/project/rfq-round/page', params }).then((res)=>{
    const list = res.list
    if(list){
      for(let i = 0; i<list.length;i++){
        list[i].index=i + 1
      }
    }
    return res
  })
}

// 查询报价基本信息详情
export const getBasicApi = async (id: number) => {
  return await request.get({ url: '/project/rfq-round-quota/get?entityCode=p_round_project_link&id=' + id })
}

//查询多轮次报价历史列表数据
export const getOldRFQPageApi = (params: RfqRoundPageReqVO) => {
  // @ts-ignore
  params.objectId = sessionStorage.getItem('RFQId')
  params.entityCode = 'r_round_link'
  params.status=3
  return  request.get({ url: '/project/rfq-round/page', params }).then((res)=>{
    const list = res.list
    if(list){
      for(let i = 0; i<list.length;i++){
        list[i].index=i + 1
      }
    }
    return res
  })
}

// 查询項目产品清单列表
export const getProductListPageApi = async (params: ProductPageReqVO) => {
  params.entityCode = 's_sale_version_code'
  const objectId = sessionStorage.getItem(`marketId`)
  // @ts-ignore
  params.objectId = parseInt(objectId)
  return  request.get({url: '/project/part-item/page', params})
}

// 查询基本信息详情
export const getRfqRoundInfoApi = async (id: number) => {
  return await request.get({ url: '/project/rfq-round/get?id=' + id })
}


//单条删除
export const delRfqRoundApi = async (id: number) => {
  return await request.delete({ url: '/project/rfq-round/delete?id=' + id })
}

// 批量删除
export const delRfqRoundListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/project/rfq-round/delete-batch?ids=' + ids })
}

// 新增
export const createRfqRoundApi = async (data: RfqRoundVO) => {
  return await request.post({ url: '/project/rfq-round/create', data })
}

// 修改
export const updateRfqRoundApi = async (data: RfqRoundVO) => {
  return await request.put({ url: '/project/rfq-round/update', data })
}

// 查询发放询价状态
export const getIssueInquiryStatusApi = (id: number) => {
  return request.get({ url: '/project/rfq-round-inquiry/check-status?rfqId=' + id })
}

// 报价状态判断
export const getRFQRoundCheckStatusApi = async (data: RfqRoundVO) => {
  return await request.post({ url: '/project/rfq-round/check-status', data })
}

// 查询RFQ基本信息详情
export const getRFQInfoApi = async (id: number) => {
  return await request.get({ url: '/project/request-for-quotation/get?id=' + id })
}


// 修改RFQ信息
export const updateRFQApi = async (data: RFQVO) => {
  return await request.put({ url: '/project/request-for-quotation/update', data })
}
