// 查询租户列表
import request from "@/config/axios";
// @ts-ignore
import {BrandVO} from "@/api/leader/material/product/brand";


export interface VersionPageReqVO extends PageParam {
  id?:number
  partVersionCode: string
}

export interface releaseVO {
  id: number
}

export interface BrandVO {
  id: number
  partId: number
  goodsCode: string
  partVersionCode: string
  partVersionStatus: number
  partVersionName: string
  sapno: string
  chineseName: string
  englishName: string
  chineseQualityDesc: string
  englishQualityDesc: string
  chinesePackMode: string
  englishPackMode: string
  businessPersonId: number
  goodsPersonId: number
  isOwnBrand: number
  isOemSample: number
}

//查询物料版本列表数据
export const getVersionPageApi = (params: VersionPageReqVO) => {
  params.isOemSample = 1
  if (params.partVersionCode === 'WIP') {
    params.partVersionCode = 0
  }
  return  request.get({ url: '/part/version/page', params }).then((res)=>{
    const list = res.list
    for(let i = 0; i<list.length;i++){
      list[i].index=i + 1
    }
    return res
  })
}
// 查询物料版本详情
export const getVersionApi = async (id: number) => {
  return await request.get({ url: '/part/version/get?id=' + id })
}
