import request from '@/config/axios'

export interface DeptTypeLinkVO {
  id: number
  typeId: number
  deptId: number
  isEstimate: number
  isActual: number
  isTop: number
}

export interface DeptTypeLinkPageReqVO extends PageParam {
  typeId?: number
  deptId?: number
  isEstimate?: number
  isActual?: number
  isTop?: number
  createTime?: Date[]
}

export interface DeptTypeLinkExcelReqVO {
  typeId?: number
  deptId?: number
  isEstimate?: number
  isActual?: number
  isTop?: number
  createTime?: Date[]
}

// 查询关联列表
export const getDeptTypeLinkPageApi = async (params: DeptTypeLinkPageReqVO) => {
  return await request.get({ url: '/cost/dept-type-link/page', params })
}

// 查询关联列表
export const getDeptTypeLinkListApi = async (params: DeptTypeLinkPageReqVO) => {
  return await request.get({ url: '/cost/dept-type-link/list', params })
}

// 查询关联详情
export const getDeptTypeLinkApi = async (id: number) => {
  return await request.get({ url: '/cost/dept-type-link/get?id=' + id })
}

// 新增关联
export const createDeptTypeLinkApi = async (data: DeptTypeLinkVO) => {
  return await request.post({ url: '/cost/dept-type-link/create', data })
}

// 修改关联
export const updateDeptTypeLinkApi = async (data: DeptTypeLinkVO) => {
  return await request.put({ url: '/cost/dept-type-link/update', data })
}

// 删除关联
export const deleteDeptTypeLinkApi = async (id: number) => {
  return await request.delete({ url: '/cost/dept-type-link/delete?id=' + id })
}

// 导出关联 Excel
export const exportDeptTypeLinkApi = async (params: DeptTypeLinkExcelReqVO) => {
  return await request.download({ url: '/cost/dept-type-link/export-excel', params })
}
