// 查询租户列表
import request from "@/config/axios";

export interface EntityPageReqVO extends PageParam {
  entityCode:string
  objectId:number
}

export interface EntityVO {
  id:number
  entityCode:string
  objectId:number
  partVersionId:number
  partCode:string
  eplDesc:string
  eplSpec:string
  eplQuantity:string
  eplDeliveryTime:Date[]
  eplUnitPrice:number
  eplUnit:number
  eplAmount:number
  eplContainerQuanlity:string
  eplPackMode:string
  eplQuanlity:string

}


//查询订单明细列表数据
export const getSalesOrderDetailsPageApi = (params: EntityPageReqVO) => {
  params.entityCode='b_sale_order_list'
  // @ts-ignore
  params.objectId=sessionStorage.getItem("purchaseOrderId")
  return request.get({ url: '/project/part-item/page', params })
}
//查询客户销售订单明细列表数据
export const getConnectSaleOrderPageApi = (params: EntityPageReqVO) => {
  params.entityCode='b_sale_order_list'
  // @ts-ignore
  params.objectId=sessionStorage.getItem("purchaseOrderId")
  return request.get({ url: '/project/part-item/page', params })
}

// // 查询订单明细详情（单条）
export const getSalesOrderDetailsApi = async (id: number) => {
  return await request.get({ url: '/project/part-item/get?id=' + id })
}

// 获取下拉框数据
export const getSelectApi = async (typeCodes: string) => {
  return await request.get({ url: "/common/type/getMapTypeCodes?typeCodes=" + typeCodes })
}

//订单明细信息新增
export const createSalesOrderDetailsApi = (data: EntityVO) => {
  return request.post({ url: '/project/part-item/create', data })
}

// // 订单明细信息修改
export const updateSalesOrderDetailsApi = (data: EntityVO) => {
  return request.put({ url: '/project/part-item/update', data })
}

//订单明细单条删除
export const delSalesOrderDetailsApi = async (id: number) => {
  return await request.delete({ url: '/project/part-item/delete?id=' + id })
}

// 订单明细批量删除
export const delSalesOrderDetailsListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/project/part-item/delete-batch?ids=' + ids })
}

// // 获取下拉框数据
// export const getSelectApi = async (typeCodes: string) => {
//   return await request.get({ url: "/common/type/getMapTypeCodes?typeCodes=" + typeCodes })
// }

// //获取负责人下拉框数据
// export const getUserApi = async () => {
//   return await request.get({ url: '/system/user/list'})
// }
