import request from '@/config/axios'
import {ProjectVO} from "@/api/leader/saleProject/project";

export interface TaskVO{
  id:number
  batchUpdate: number
  entityCode:string
  objectId: number
  taskCode: string
  taskName: string
  taskCategory: number
  taskType: number
  taskDesc: string
  chargePersonId: number
  taskStatus: number
  planEndTime: Date
  planStartTime: Date
  actualEndTime: Date
  actualStartTime: Date
  parentTaskId: number
  taskMainType:number
  isSendMail:number
  noticePersonId:string
  planDateType:number
}
export interface PersonVO{
  entityCode:string
  objectId: number
  userId:number
  roleId:number
}

export interface SalePageReqVO extends PageParam {
  taskName?: string
  taskCategory?: string
  taskStatus?: string
  objectId?:number
  entityCode?:string
  isTemplate?:number
}

export interface TaskPageReqVO extends PageParam {
  id:number
  taskCode: string
  taskName: string
  nickname:string
  planEndTime: Date[]
  planStartTime: Date[]
  actualEndTime: Date[]
  actualStartTime: Date[]
  receiveTime:Date[]
  sendTime:Date[]
  createTime:Date[]
}

export interface TaskLinkVO{
  source:number
  target:number
  type:number
  lag:number
}

export interface TaskLinkPageReqVO extends PageParam {
  source:number
  target:number
  type:number
  lag:number
}
export interface MyWorkPageReqVO extends PageParam {
  taskName?: string
  taskCode?: string
  chargePersonId?: number
  type?:number
  taskStatus?: number
}

// 查询项目任务列表
export const getSatePageApi = (params: SalePageReqVO) => {
  // params.objectId=sessionStorage.getItem("marketId")
  // params.entityCode="s_task_create"
  return request.get({ url: '/project/task/page', params })
}

//查询gantt图列表接口
export const getGanttPageApi = (params:SalePageReqVO) => {
  // console.log(888,params)
  return request.get({ url: '/project/task/gantt-page',params })
}

//查询项目模版gantt图列表接口
export const getProjectTemplateGanttPageApi = (params:SalePageReqVO) => {
  if(isNaN(params.objectId)){
    return false
  }
  console.log(isNaN(params.objectId))
  params.isTemplate=1
  return request.get({ url: '/project/task/gantt-page',params })
}
export const getProjectGanttPageApi = (params:SalePageReqVO) => {
  params.isTemplate=2
  return request.get({ url: '/project/task/gantt-page',params }).then(res=>{
    for (let i = 0; i < res.length; i ++) {
    if(res[i].parentTaskId == 0 && res[i].projectChargePersonName !== null) {
      res[i].chargePersonId = res[i].projectChargePersonName
    }
  }
    return res
  })
}

export const getProjectTaskPageApi = (params:SalePageReqVO) => {
  params.isTemplate=2
  params.entityCode= "s_task_create"
  params.objectId = parseInt(<string>sessionStorage.getItem("taskId"))
  // console.log(params.objectId,"2211")
  return request.get({ url: '/project/task/select-task',params }).then((res)=>{
    return res
  })
}
export const getProjectTabTaskPageApi = (params:SalePageReqVO) => {
  params.isTemplate=2
  params.entityCode= "s_task_create"
  params.objectId = parseInt(<string>sessionStorage.getItem("marketId"))
  // console.log(params.objectId,"2211")
  return request.get({ url: '/project/task/select-task',params }).then((res)=>{
    return res
  })
}
//查询收到任务
export const getReceiveMyWorkPageApi = (params: MyWorkPageReqVO) => {
  params.type=1
  return request.get({ url: '/project/task/my-order', params })
}

//查询发出任务
export const getSendMyWorkPageApi = (params: MyWorkPageReqVO) => {
  params.type=2
  return request.get({ url: '/project/task/my-order', params })
}

//查询完成任务
export const getOverMyWorkPageApi = (params: MyWorkPageReqVO) => {
  params.type=3
  return request.get({ url: '/project/task/my-order', params })
}
//获取项目小组人员
export const GetProjectGroupApi = (data: ProjectVO) => {
  data.entityCode = 't_task_entity_participant_code'
  return request.post({ url: '/project/entity-participant/list', data })
}
//获取项目小组人员-无权限
export const GetProjectGroupNoRoleApi = (data: ProjectVO) => {
  data.entityCode = 't_task_entity_participant_code'
  return request.post({ url: '/project/entity-participant/list-no', data })
}
//创建项目小组
export const createProjectGroupApi = (data: PersonVO) => {
  return request.post({ url: '/project/entity-participant/create', data })
}
// 项目任务信息保存 --模板
export const createTaskTemplateApi = (data: TaskVO) => {
  return request.post({ url: '/project/task/create', data })
}
// 项目任务信息保存 --任务分配
export const createTaskApi = (data: TaskVO) => {
  if (data.planStartTime !== null && data.planStartTime !== undefined) {
    // 将毫秒时间戳转换为 Date 对象
    const date = new Date(data.planStartTime)
    // 设置时间为 08:30:00
    date.setHours(8, 30, 0 , 0)
    // 更新 data.planStartTime 为新的时间戳
    data.planStartTime = date.getTime()
  }
  if (data.planEndTime !== null && data.planEndTime !== undefined) {
    // 将毫秒时间戳转换为 Date 对象
    const date = new Date(data.planEndTime)
    // 设置时间为 17:30:00
    date.setHours(17, 30, 0, 0)
    // 更新 data.planStartTime 为新的时间戳
    data.planEndTime = date.getTime()
  }
  return request.post({ url: '/project/task/create', data })
}

/**
  * Author： 许祎婷
  * Time： 2025/04/25
  * Description：问题任务新增
*/
export const createQuestionTaskApi = (data: TaskVO) => {
  if (data.planStartTime !== null && data.planStartTime !== undefined) {
    // 将毫秒时间戳转换为 Date 对象
    const date = new Date(data.planStartTime)
    // 设置时间为 08:30:00
    date.setHours(8, 30, 0 , 0)
    // 更新 data.planStartTime 为新的时间戳
    data.planStartTime = date.getTime()
  }
  if (data.planEndTime !== null && data.planEndTime !== undefined) {
    // 将毫秒时间戳转换为 Date 对象
    const date = new Date(data.planEndTime)
    // 设置时间为 17:30:00
    date.setHours(17, 30, 0, 0)
    // 更新 data.planStartTime 为新的时间戳
    data.planEndTime = date.getTime()
  }
  return request.post({ url: '/project/task/create', data })
}
//查询项目任务信息
export const getTaskViewApi = (id: number) => {
  return request.get({ url: '/project/task/get?isTemplate=2&id=' + id })
}
// //查询品牌信息页面下拉框数据
// export const getBrandSeletListApi = (typeCodes: string) => {
//   return request.get({ url: '/common/type/getMapTypeCodes?typeCodes=' + typeCodes })
// }
// 项目任务修改
export const updateSaleApi = (data: TaskVO) => {
  return request.put({ url: '/project/task/update', data })
}
//  甘特图任务修改
export const updateGanttSaleApi = (data: any) => {
  console.log('Received data:', data)
  // 确保 ganttUpdateVo 是数组
  if (Array.isArray(data.ganttUpdateVo)) {
    data.ganttUpdateVo.forEach(task => {
      // 对每个任务对象进行处理
      if (task.planStartTime !== null && task.planStartTime !== undefined) {
        const date = new Date(task.planStartTime)
        date.setHours(8, 30, 0, 0)
        task.planStartTime = date.getTime()
      }

      if (task.planEndTime !== null && task.planEndTime !== undefined) {
        const date = new Date(task.planEndTime)
        date.setHours(17, 30, 0, 0)
        task.planEndTime = date.getTime()
      }
    })
  } else {
    console.error('Expected ganttUpdateVo to be an array, but received:', typeof data.ganttUpdateVo);
  }
  return request.put({ url: '/project/task/update-gantt', data })
}

// 问题任务时间修改
//  甘特图任务修改
export const updatePlanTimeApi = (data: any) => {
  // 确保 ganttUpdateVo 是数组
  return request.put({ url: '/project/task/update-gantt', data })
}

export const updatePeopleApi = (data: TaskVO) => {
  return request.post({ url: '/project/task/update-people', data })
}

// 删除项目任务
export const deleteSaleApi = async (id: number, isTemplate:number,confirm:boolean,isMyOrder:boolean) => {
  return await request.delete({ url: '/project/task/delete?id=' + id + '&isTemplate=' + isTemplate + '&confirm=' + confirm + '&isMyOrder=' + isMyOrder})
}

// 删除问题任务
export const deleteQuestionTaskApi = async (id: number, isTemplate:number,confirm:boolean,isMyOrder:boolean) => {
  const entityCode = 's_question_task_create';
  return await request.delete({ url: '/project/task/delete?id=' + id + '&isTemplate=' + isTemplate + '&confirm=' + confirm + '&isMyOrder=' + isMyOrder + '&entityCode=' + entityCode})
}

// 批量删除项目任务
export const delSaleListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/project/task/delete-batch?ids=' + ids })
}
