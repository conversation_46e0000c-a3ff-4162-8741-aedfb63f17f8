import request from "@/config/axios";

// @ts-ignore

export interface ExperienceVO  {
  entityCode?: String;
  objectId?: number
  proposer?:number
  deptId?:number
  creatorDeptId?:number
  projectSource?:number
  chargePersonId?:number
  partVersionId?: number
  summarizeTime?:Date[]
  createTime?:string
  experienceSummarize?:string
  experienceCode?:string
  experienceName?:string
  type?:number
  isAll?:boolean
  taskCode?:String
  taskStatusList?:[]
  taskCategory?:number
  isQuestion?:number
  status?:number
  isExpire?:string
  challengeStatus?:number
  isUnComplete?:string,
  pageSize:number,
  pageNo:number,
  index?:number,
  isFirst?: number | undefined,
  isThird?:number
  createStartTime?:string
  createEndTime?:string
  taskStatus?:number
  statusStr?:string
  isNotDraft?:number,
  filed?:any,
  order?:any,
  a: number,
  estimateSign: number
  projectStatus: number
}
export interface ExperiencePageVO extends PageParam  {
  entityCode?: String;
  objectId?: number
  projectSource?:number
  chargePersonId?:number
  partVersionId?: number
  summarizeTime?:Date[]
  createTime?:string
  experienceSummarize?:string
  experienceCode?:string
  experienceName?:string
  type?:number
  taskCode?:String
  taskStatusList?:[]
  taskCategory?:number
  isQuestion?:number
  status?:number
  isExpire?:string
  challengeStatus?:number
  isUnComplete?:string,
  pageSize:number,
  pageNo:number,
  index?:number,
  isFirst?: number | undefined,
  isThird?:number
  createStartTime?:string
  createEndTime?:string
  taskStatus?:number
  statusStr?:string
  isNotDraft?:number,
  filed?:any,
  order?:any,
}

//转化为经验教训
export const createExperienceApi = (data: ExperienceVO) => {
  return request.post({url: '/project/experience/create', data})
}


//获取负责人下拉框数据
export const getUserApi = async () => {
  return await request.get({ url: '/system/user/list'})
}

// 获取任务子集
export const getTaskChildListApi = async (id: number) => {
  const response = await request.get({ url: '/project/task/son-task?id=' + id })
  // 处理返回的数据
  return response.map(task => ({
    ...task,
    planTime: [task.planStartTime, task.planEndTime]
  }))
}

//我的任務列表查询
export const getMyTaskPageApi = (params:ExperiencePageVO) => {
  // 收到的任务查询
  if(!params.taskStatus){
    delete params.taskStatus
  }
  if(!params.isUnComplete){
    delete params.isUnComplete
  }
  if(!params.isExpire){
    delete params.isExpire
  }
  if (params.createTime !== null && params.createTime !== undefined) {
  const createTimeType = typeof params.createTime
  if(createTimeType === 'string') {
    //路由查询，转换格式
    params.createTime = params.createTime.split(',')
  }
  params.createStartTime = params.createTime[0]+ ' 00:00:00'
  params.createEndTime = params.createTime[1]+ ' 23:59:59'
  delete params.createTime
  }
  // 判断一下日期框条件
  if (params.planTime !== null && params.planTime !== undefined) {
    const planType = typeof params.planTime
    if(planType === 'string') {
      //路由查询，转换格式
      params.planTime = params.planTime.split(',')
    }
    params.planStartTime = params.planTime[0]+ ' 00:00:00'
    params.planEndTime = params.planTime[1]+ ' 23:59:59'
    delete params.planTime
  }
  if (params.actualTime == 1) {
    delete params.actualStartTime
    delete params.actualEndTime
  }
  // 判断一下日期框条件
  else if (params.actualTime !== null && params.actualTime !== undefined) {
    const actualType = typeof params.actualTime
    if(actualType === 'string') {
      //路由查询，转换格式
      params.actualTime = params.actualTime.split(',')
    }
    params.actualStartTime = params.actualTime[0]+ ' 00:00:00'
    params.actualEndTime = params.actualTime[1]+ ' 23:59:59'
    delete params.actualTime
  }
  if(params.isExpire==1){
    params.taskCategory = 4
  }
  if(params.isUnComplete==1){
    params.taskCategory = 4
  }
  if(params.taskStatus==1){
    params.taskCategory = 4
  }
  //判断来源
  // if(window.location.search === '?index=1&taskStatus=1&taskCategory=4&pageSize=10&pageNo=1'||window.location.search === ''){
  //   params.taskStatus = 1
  //   params.taskCategory = 4
  // }

  params.type = 1
  // params.pageSize = 10
  // params.pageNo = 1
  return  request.get({ url: '/project/task/my-order', params }).then((res)=>{
    const list = res.list
    for(let i = 0; i<list.length;i++){
      list[i].index=i + 1
      list[i].hasChild = true
      list[i].parentId = null
      // 将 planStartTime 和 planEndTime 设置为 planTime
      if (list[i].planStartTime && list[i].planEndTime) {
        list[i].planTime = [list[i].planStartTime, list[i].planEndTime];
      } else {
        list[i].planTime = [];
      }
    }
    return res
  })
}

export const getMyProposeTAskPageApi = (params: ExperiencePageVO) => {
  // 收到的任务查询
  params.type = 2
  if(params.taskStatus){
    if(params.taskStatus==1){
      params.taskCategory=4
    }
  }
  if (params.createTime !== null && params.createTime !== undefined) {
    const createTimeType = typeof params.createTime
    if(createTimeType === 'string') {
      //路由查询，转换格式
      params.createTime = params.createTime.split(',')
    }
    params.createStartTime = params.createTime[0]+ ' 00:00:00'
    params.createEndTime = params.createTime[1]+ ' 23:59:59'
    delete params.createTime
    if(params.taskStatus==null&&params.taskStatus==undefined&&params.taskStatus==''){
      delete params.taskStatus
    }
    if(params.isUnComplete==null&&params.isUnComplete==undefined&&params.isUnComplete==''){
      delete params.isUnComplete
    }
    if(params.isExpire==null&&params.isExpire==undefined&&params.isExpire==''){
      delete params.isExpire
    }
  }
  //判断一下日期框条件
  if (params.planTime !== null && params.planTime !== undefined) {
    const planType = typeof params.planTime
    if(planType === 'string') {
      //路由查询，转换格式
      params.planTime = params.planTime.split(',')
    }
    params.planStartTime = params.planTime[0]+ ' 00:00:00'
    params.planEndTime = params.planTime[1]+ ' 23:59:59'
    delete params.planTime
    if(params.taskStatus==null&&params.taskStatus==undefined){
      delete params.taskStatus
    }
    if(params.isUnComplete==null&&params.isUnComplete==undefined){
      delete params.isUnComplete
    }
    if(params.isExpire==null&&params.isExpire==undefined){
      delete params.isExpire
    }
  }
  //判断一下日期框条件
  if (params.actualTime !== null && params.actualTime !== undefined) {
    const actualType = typeof params.actualTime
    if(actualType === 'string') {
      //路由查询，转换格式
      params.actualTime = params.actualTime.split(',')
    }
    params.actualStartTime = params.actualTime[0]+ ' 00:00:00'
    params.actualEndTime = params.actualTime[1]+ ' 23:59:59'
    delete params.actualTime
    if(params.taskStatus==null&&params.taskStatus==undefined){
      delete params.taskStatus
    }
    if(params.isUnComplete==null&&params.isUnComplete==undefined){
      delete params.isUnComplete
    }
    if(params.isExpire==null&&params.isExpire==undefined){
      delete params.isExpire
    }
  }
  //判断来源
  if(window.location.search === '?index=1&taskStatus=1&taskCategory=4&pageSize=10&pageNo=1'||window.location.search === ''){
    params.taskStatus = 1
    params.taskCategory = 4
  }
  params.type = 2

  if(params.isUnComplete==1){
    params.taskCategory=4
  }
  if(params.isExpire === 1) {
    params.taskCategory=4
  }
  return request.get({ url: '/project/task/my-order', params }).then((res)=>{
    const list = res.list
    for(let i = 0; i<list.length;i++){
      list[i].index=i + 1
      list[i].hasChild = true
      list[i].parentId = null
      // 将 planStartTime 和 planEndTime 设置为 planTime
      if (list[i].planStartTime && list[i].planEndTime) {
        list[i].planTime = [list[i].planStartTime, list[i].planEndTime];
      } else {
        list[i].planTime = [];
      }
    }
    res.list = list
    return res
  })
}

//我的任務列表查询
export const getQuestionPageApi = (params: ExperienceVO) => {
  // params.entityCode = 'p_task_challenge'
  // 收到的任务查询
  params.type = 1
  //去掉草稿的项目
  // params.isNotDraft = 1
  if(params.isQuestion==1048){
    params.isQuestion=1
  }if(params.isQuestion==1049){
    params.isQuestion=2
  }
  if(params.challengeStatus){
    params.status=params.challengeStatus
    delete params.challengeStatus
  }
  // //判断一下日期框条件
  if (params.proposalDate !== null && params.proposalDate !== undefined) {
    const proposalType = typeof params.proposalDate
    if(proposalType === 'string') {
      //路由查询，转换格式
      params.proposalDate = params.proposalDate.split(',')
    }
    params.proposalDateStart = params.proposalDate[0] + ' 00:00:00'
    params.proposalDateEnd = params.proposalDate[1] + ' 23:59:59'
    delete params.proposalDate
  }
  //
  // //判断一下日期框条件
  if (params.planFinishTime !== null && params.planFinishTime !== undefined) {
    const planFinishType = typeof params.planFinishTime
    if(planFinishType === 'string') {
      //路由查询，转换格式
      params.planFinishTime = params.planFinishTime.split(',')
    }
    params.planFinishTimeStart = params.planFinishTime[0] + ' 00:00:00'
    params.planFinishTimeEnd = params.planFinishTime[1] + ' 23:59:59'
    delete params.planFinishTime
  }
  // //判断一下日期框条件
  if (params.actualFinishTime !== null && params.actualFinishTime !== undefined) {
    const actualFinishType = typeof params.actualFinishTime
    if(actualFinishType === 'string') {
      //路由查询，转换格式
      params.actualFinishTime = params.actualFinishTime.split(',')
    }
    params.actualFinishTimeStart = params.actualFinishTime[0] + ' 00:00:00'
    params.actualFinishTimeEnd = params.actualFinishTime[1] + ' 23:59:59'
    delete params.actualFinishTime
  }
  return  request.get({ url: '/project/task/taskQuestionPage', params }).then((res)=>{
    const list = res.list
    for(let i = 0; i<list.length;i++){
      list[i].index=i + 1
    }
    return res
  })
}

//我的任務提出的任务列表查询
export const getProposeQuestionPageApi = (params: ExperienceVO) => {
  // params.entityCode = 'p_task_challenge'
  // 收到的任务查询
  params.type = 2
  //去掉草稿的项目
  // params.isNotDraft = 1
  if(params.isQuestion==1048){
    params.isQuestion=1
  }if(params.isQuestion==1049){
    params.isQuestion=2
  }
  if(params.challengeStatus){
    params.status=params.challengeStatus
    delete params.challengeStatus
  }
  //判断一下日期框条件
  if (params.proposalDate !== null && params.proposalDate !== undefined) {
    const proposalType = typeof params.proposalDate
    if(proposalType === 'string') {
      //路由查询，转换格式
      params.proposalDate = params.proposalDate.split(',')
    }
    params.proposalDateStart = params.proposalDate[0]
    params.proposalDateEnd = params.proposalDate[1]
    delete params.proposalDate
  }
  //判断一下日期框条件
  if (params.planFinishTime !== null && params.planFinishTime !== undefined) {
    const planFinishType = typeof params.planFinishTime
    if(planFinishType === 'string') {
      //路由查询，转换格式
      params.planFinishTime = params.planFinishTime.split(',')
    }
    params.planFinishTimeStart = params.planFinishTime[0]
    params.planFinishTimeEnd = params.planFinishTime[1]
    delete params.planFinishTime
  }
  //判断一下日期框条件
  if (params.actualFinishTime !== null && params.actualFinishTime !== undefined) {
    const actualFinishType = typeof params.actualFinishTime
    if(actualFinishType === 'string') {
      //路由查询，转换格式
      params.actualFinishTime = params.actualFinishTime.split(',')
    }
    params.actualFinishTimeStart = params.actualFinishTime[0]
    params.actualFinishTimeEnd = params.actualFinishTime[1]
    delete params.actualFinishTime
  }
  return  request.get({ url: '/project/task/taskQuestionPage', params }).then((res)=>{
    const list = res.list
    for(let i = 0; i<list.length;i++){
      list[i].index=i + 1
    }
    return res
  })
}

//任务开始
export const updateTaskStartApi = (data) => {
  data.isMyOrder = true
  return request.put({url: '/project/task/update',data })
}
//任务状态修改
export const updateTaskStatusApi = (id,status) => {
  return request.put({url: '/project/task/taskStatus?id='+id+'&status='+status})
}
