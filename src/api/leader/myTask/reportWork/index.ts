import request from "@/config/axios";

// @ts-ignore

export interface ReportWorkVO {
  id:number
}
export interface ReportWorkPageReqVO extends PageParam {
  id:number
}

// 创建报工审批
export const createReportWorkApi = (data: ReportWorkVO) => {
  return request.post({url: '/bpm/reportWork/create', data})
}

// 驳回报工审批
export const rejectReportWorkApi = (data: ReportWorkVO) => {
  return request.post({url: '/bpm/reportWork/reject', data})
}
