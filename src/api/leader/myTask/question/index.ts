import request from "@/config/axios";

// @ts-ignore

export interface QuestionVO {
  projectId:number
  objectId: number
  projectSource:number
  chargePersonId:number
  partVersionId: number
  summarizeTime:Date[]
  experienceSummarize:string
  experienceCode:string
  experienceName:string
  questionDesc:string
  questionAnalyze:string
  solution:string
}
export interface QuestionPageReqVO extends PageParam {
  objectId:number
}

//转化为经验教训
export const createQuestionApi = (data: QuestionVO) => {
  return request.post({url: '/project/experience/create', data})
}

//获取负责人下拉框数据
export const getUserApi = async () => {
  return await request.get({ url: '/system/user/list'})
}

// 获取下拉框数据
export const getSelectApi = async (typeCodes: string) => {
  return await request.get({ url: "/common/type/getMapTypeCodes?typeCodes=" + typeCodes })
}

//经验教训列表查询
export const getQuestionPageApi = (params: QuestionVO) => {
  return  request.get({ url: '/project/experience/page', params }).then((res)=>{
    const list = res.list
    for(let i = 0; i<list.length;i++){
      list[i].index=i + 1
    }
    return res
  })
}
//项目经验教训列表查询
export const getProjectQuestionPageApi = (params: QuestionPageReqVO) => {
  params.objectId=parseInt(<string>sessionStorage.getItem("marketId"))
  return  request.get({ url: '/project/experience/page', params }).then((res)=>{
    const list = res.list
    for(let i = 0; i<list.length;i++){
      list[i].index=i + 1
    }
    return res
  })

}

//问题处理修改
export const updateQuestionApi = (data: QuestionVO) => {
  return request.put({url: '/project/challenge/update', data})
}

//问题处理修改状态
export const updatechallengeStatusApi = (status, id) => {
  return request.put({url: '/project/challenge/challengeStatus?status=' + status + '&id=' + id})
}


//问题处理提交审批
export const rejectchallengeStatusApi = async (data) => {
  return await request.post({ url: '/bpm/myChallenge/reject-init',  data: data })
}

// 单条查询问题处理
export const getQuestionApi = async (id: number,isTemplate: number) => {
  // return await request.get({ url: 'project/challenge/get?id=' + id +'&isTemplate='+isTemplate})
  return request.get({ url: 'project/challenge/get?id=' + id +'&isTemplate='+isTemplate}).then((res)=>{
    let updateUrl = import.meta.env.VITE_BASE_URL?import.meta.env.VITE_BASE_URL+import.meta.env.VITE_SERVICE_PORT:'http://'+window.location.hostname+import.meta.env.VITE_SERVICE_PORT
    if(res.reason){
      let start=res.reason.indexOf("http://")
      let end=res.reason.indexOf(import.meta.env.VITE_API_URL)
      if(start!=-1&&end!=-1){
        let str=res.reason.substring(start,end)
        res.reason=res.reason.replaceAll(str,updateUrl)
      }
    }
    console.log(res,'打印图片地址转化333333')
    return res
  })
}



// 删除
export const deleteQuestionApi = async (id: number) => {
  return await request.delete({ url: '/project/experience/delete?id=' + id })
}

// 批量删除
export const delQuestionListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/project/experience/delete-batch?ids=' + ids })
}


//问题反馈新增
export const createFeedbackApi = (data: QuestionVO) => {
  return request.post({url: '/project/challenge/create', data})
}

//问题反馈单条查询
export const getFeedbackApi = (id: number) => {
  return request.get({url: '/project/challenge/get?entityCode=p_task_challenge&projectId=' + id })
}

// 查询文件列表
export const getFilePageApi = () => {
  const objectId = sessionStorage.getItem(`projectId`)
  // console.log(objectId)
  let params = {
    entityCode: 'p_challenge_file',
    objectId: parseInt(<string>objectId)
  }
  // @ts-ignore
  if(params.objectId=='NAN'||params.objectId==undefined){
    let res=[]
    return res
  }else {
    return request.post({ url: '/infra/file/list', params }).then((res)=>{
      const list = res.list
      let updateUrl = import.meta.env.VITE_BASE_URL?import.meta.env.VITE_BASE_URL+import.meta.env.VITE_SERVICE_PORT+import.meta.env.VITE_API_URL:'http://'+window.location.hostname+import.meta.env.VITE_SERVICE_PORT+import.meta.env.VITE_API_URL
      if(list){
        for(let i = 0; i<list.length;i++){
          list[i].url=updateUrl+list[i].url;
          console.log(list)
        }
      }
      return res
    })
  }
}

//查询项目任务信息
export const getTaskViewApi = (id: number) => {
  return request.get({ url: '/project/task/get?isTemplate=2&id=' + id }).then((res)=>{
    let updateUrl = import.meta.env.VITE_BASE_URL?import.meta.env.VITE_BASE_URL+import.meta.env.VITE_SERVICE_PORT:'http://'+window.location.hostname+import.meta.env.VITE_SERVICE_PORT
    if(res.scheme){
      let start=res.scheme.indexOf("http://")
      let end=res.scheme.indexOf(import.meta.env.VITE_API_URL)
      if(start!=-1&&end!=-1){
        let str=res.scheme.substring(start,end)
        res.scheme=res.scheme.replaceAll(str,updateUrl)
      }
    }

    if(res.taskDesc){
      let start=res.taskDesc.indexOf("http://")
      let end=res.taskDesc.indexOf(import.meta.env.VITE_API_URL)
      if(start!=-1&&end!=-1){
        let str=res.taskDesc.substring(start,end)
        console.log(str,'str');
        res.taskDesc=res.taskDesc.replaceAll(str,updateUrl)
      }
    }


    console.log(res,'打印图片地址转化1')
    return res
  })
}

//查询问题任务信息
export const getQuestionTaskApi = (id: number) => {
  return request.get({ url: '/project/task/get?isTemplate=2&entityCode=s_question_task_create&id=' + id }).then((res)=>{
    let updateUrl = import.meta.env.VITE_BASE_URL?import.meta.env.VITE_BASE_URL+import.meta.env.VITE_SERVICE_PORT:'http://'+window.location.hostname+import.meta.env.VITE_SERVICE_PORT
    if(res.scheme){
      let start=res.scheme.indexOf("http://")
      let end=res.scheme.indexOf(import.meta.env.VITE_API_URL)
      if(start!=-1&&end!=-1){
        let str=res.scheme.substring(start,end)
        res.scheme=res.scheme.replaceAll(str,updateUrl)
      }
    }

    if(res.taskDesc){
      let start=res.taskDesc.indexOf("http://")
      let end=res.taskDesc.indexOf(import.meta.env.VITE_API_URL)
      if(start!=-1&&end!=-1){
        let str=res.taskDesc.substring(start,end)
        console.log(str,'str');
        res.taskDesc=res.taskDesc.replaceAll(str,updateUrl)
      }
    }


    console.log(res,'打印图片地址转化1')
    return res
  })
}


//查询模板项目任务信息
export const getTemplateTaskApi = (id: number) => {
  return request.get({ url: '/project/task/get?isTemplate=1&id=' + id }).then((res)=>{
    let updateUrl = import.meta.env.VITE_BASE_URL?import.meta.env.VITE_BASE_URL+import.meta.env.VITE_SERVICE_PORT:'http://'+window.location.hostname+import.meta.env.VITE_SERVICE_PORT
    if(res.scheme){
      let start=res.scheme.indexOf("http://")
      let end=res.scheme.indexOf(import.meta.env.VITE_API_URL)
      if(start!=-1&&end!=-1){
        let str=res.scheme.substring(start,end)
        res.scheme=res.scheme.replaceAll(str,updateUrl)
      }
    }
    if(res.taskDesc){
      let start=res.taskDesc.indexOf("http://")
      let end=res.taskDesc.indexOf(import.meta.env.VITE_API_URL)
      if(start!=-1&&end!=-1){
        let str=res.taskDesc.substring(start,end)
        console.log(str,'str');
        res.taskDesc=res.taskDesc.replaceAll(str,updateUrl)
      }
    }
    console.log(res,'打印图片地址转化2')
    return res
  })
}

//获取人员信息
export const getPersonApi = async (id: number) => {
  return await request.get({ url: '/system/user/get?id=' + id })
}
//获取人员信息
export const getUserListApi = async (id: number) => {
  return await request.get({ url: '/project/manager/getUserList?id=' + id })
}
