import request from "@/config/axios";

export interface OrderContentPageReqVO extends PageParam {
  orderId: string | null;
  entityCode:string
  objectId:number

}

export interface OrderContentVO {
  orgId:number
  id:number
  orderDesc:string
  actualDate:Date[]
  orderCategory: string
}


//入库申请单列表数据
export const getStorageApplicationFormPageApi = (params: OrderContentPageReqVO) => {
  return request.get({ url: '/project/wms-order/page', params })
}

//获取采购订单关联的入库申请单列表
export const getPurchaseLinkStorageApplicationFormPageApi = (params: OrderContentPageReqVO) => {
  // @ts-ignore
  params.orderId = parseInt(<string>sessionStorage.getItem(`purchaseOrderId`))
  return request.get({ url: '/project/wms-order/page-order', params })
}

//采购订单入库申请单列表
export const getPurchaseOrderPageApi = (params: OrderContentPageReqVO) => {
  params.objectId = parseInt(<string>sessionStorage.getItem(`purchaseOrderId`))
  return request.get({ url: '/project/wms-order-item/inorderitempage', params }).then((res)=>{
    const list = res.list
    for(let i = 0; i<list.length;i++){
      list[i].index=i + 1
    }
    return res
  })
}



// // 查询入库申请单详情（单条）
export const getStorageApplicationFormApi = async (id: number) => {
  return await request.get({ url: '/project/wms-order/get?id=' + id })
}

// // 查询供应商
// export const getOrgApi = async () => {
//   return await request.get({ url: '/common/org/list?orgCategory=1'})
// }


// 入库申请单信息新增
export const createStorageApplicationFormApi = (data: OrderContentVO) => {
  return request.post({ url: '/project/wms-order/create', data })
}

// 入库申请单信息修改
export const updateStorageApplicationFormApi = (data: OrderContentVO) => {
  return request.put({ url: '/project/wms-order/update', data })
}

//入库申请单单条删除
export const delStorageApplicationFormApi = async (id: number) => {
  return await request.delete({ url: '/project/wms-order/delete?id=' + id })
}

// 入库申请单批量删除
export const delStorageApplicationFormListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/project/wms-order/delete-batch?ids=' + ids })
}

//入库申请单明细单条删除
export const delStorageApplicationFormDetailsApi = async (id: number) => {
  return await request.delete({ url: '/project/wms-order-item/delete?id=' + id })
}

// 入库申请单明细批量删除
export const delStorageApplicationFormDetailsListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/project/wms-order-item/delete-batch?ids=' + ids })
}
