import request from "@/config/axios";

export interface OrderPageReqVO extends PageParam {
  entityCode: string
  objectId: number

}

export interface ConnectOrderPageReqVO extends PageParam {
  orgCategory: number
  orgId: number

}

export interface OrderVO {
  id: number
  entityCode: string
  objectId: number
  orderCategory: number
  orderCode: string
  contractCode: string
  customerOrderCode: string
  orgId: number
  orgFactoryId: number
  orgAddress: string
  chargePersonId: number
  deptId: number
  startTime: Date[]
  endTime: Date[]
  signAddress: string
  signTime: Date[]
  orderDesc: string
}


//查询采购订单列表数据
export const getPurchaseOrderPageApi = (params: OrderPageReqVO) => {
  // params.partVersionId = sessionStorage.getItem(`materialId`)
  // params.orgCategory = 1
  return request.get({url: '/project/order/buyPage', params})
}
//查询客户销售订单列表数据
export const getConnectSaleOrderPageApi = (params: ConnectOrderPageReqVO) => {
  // params.partVersionId = sessionStorage.getItem(`materialId`)
  // @ts-ignore
  params.orgId = sessionStorage.getItem("CustomerId")
  params.orgCategory = 0
  // params.orgCategory = 1
  return request.get({url: '/project/order/page', params})
}

// // 查询采购/销售订单详情（单条）
export const getOrderApi = async (id: number) => {
  return await request.get({url: '/project/order/get?id=' + id})
}

// 查询供应商
export const getOrgApi = async () => {
  return await request.get({url: '/common/org/list?orgCategory=1'})
}

//获取负责人下拉框数据
export const getUserApi = async () => {
  return await request.get({url: '/system/user/list'})
}

// 采购/销售订单信息新增
export const createOrderApi = (data: OrderVO) => {
  return request.post({url: '/project/order/create', data})
}

// 采购/销售订单信息修改
export const updateOrderApi = (data: OrderVO) => {
  return request.put({url: '/project/order/update', data})
}

//采购/销售订单信息单条删除
export const delOrderApi = async (id: number) => {
  return await request.delete({url: '/project/order/delete?id=' + id})
}

// 采购/销售订单信息批量删除
export const delOrderListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({url: '/project/order/delete-batch?ids=' + ids})
}

// // 获取下拉框数据
// export const getSelectApi = async (typeCodes: string) => {
//   return await request.get({ url: "/common/type/getMapTypeCodes?typeCodes=" + typeCodes })
// }

// //获取负责人下拉框数据
// export const getUserApi = async () => {
//   return await request.get({ url: '/system/user/list'})
// }
