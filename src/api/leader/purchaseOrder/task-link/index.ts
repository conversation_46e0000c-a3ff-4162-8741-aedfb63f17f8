import request from "@/config/axios";


export interface TaskLinkVO{
  source:number
  target:number
  type:number
  lagStatus:number
}
export interface TaskLinkPageVO extends PageParam{
  id:number
  objectId:number
  taskNo:string
  isTemplate:number
}
//甘特图任务关系数据
export const getTaskLinkPageApi = (params:TaskLinkPageVO) => {
  return request.get({ url: '/project/task-link/page',params})
}
//获取单个项目任务关系
export const getProjectTaskLinkPageApi = (params:TaskLinkPageVO) => {
  // params.objectId=parseInt(<string>sessionStorage.getItem('marketId'))
  params.isTemplate=1
  return request.get({ url: '/project/task-link/page',params})
}
// 新增任务之间关系
export const createTaskLinkApi = async (data: TaskLinkVO) => {
  return await request.post({ url: '/project/task-link/create', data })
}
// 修改任务之间关系
export const updateTaskLinkApi = async (data: TaskLinkVO) => {
  return await request.put({ url: '/project/task-link/update', data })
}
// 更新任务序号
export const updateTaskNoApi = async (data: TaskLinkVO) => {
  return await request.put({ url: '/project/task/update-taskNo', data })
}

// 删除项目任务关系
export const deleteTaskLinkApi = async (id: number) => {
  return await request.delete({ url: '/project/task-link/delete?id=' + id })
}


// 导出Markdown
export const exportApi = (obejctId) => {
  return request.download({ url: '/project/task/export-gantt?entityCode=s_task_create&isTemplate=2&objectId=' + obejctId})
}
