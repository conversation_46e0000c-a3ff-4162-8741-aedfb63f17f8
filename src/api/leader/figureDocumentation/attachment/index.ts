import request from '@/config/axios'

export interface FileVO {
  id: number
  configId: number
  path: string
  name: string
  url: string
  size: string
  type: string
  createTime: Date
}

export interface FilePageReqVO extends PageParam {
  entityCode: string;
  path?: string
  type?: string
  createTime?: Date[]
}


// 查询文件列表
export const getFilePageApi = (params: FilePageReqVO) => {
  params.entityCode = 'g_document_file'
  const objectId = sessionStorage.getItem(`documentId`)
  // @ts-ignore
  params.objectId = objectId
  return request.post({ url: '/infra/file/list', params }).then((res)=>{
    const list = res.list
    let updateUrl = import.meta.env.VITE_BASE_URL?import.meta.env.VITE_BASE_URL+import.meta.env.VITE_SERVICE_PORT+import.meta.env.VITE_API_URL:'http://'+window.location.hostname+import.meta.env.VITE_SERVICE_PORT+import.meta.env.VITE_API_URL
    if(list){
      for(let i = 0; i<list.length;i++){
        list[i].url=updateUrl+list[i].url;
        console.log(list)
      }
    }
    return res
  })
}

// 查询文件code字段
export const getFileCodeApi = () => {
  return request.get({ url: '/infra/file/code'})
}

// 删除文件
export const deleteFileApi = (id: number) => {
  return request.delete({ url: '/infra/file/delete?id=' + id })
}
