import request from '@/config/axios'

export interface DocumentPageVO extends PageParam {
partVersionId:number
}

export interface ModelPageVO extends PageParam {
  primaryObjectId:number,
  linkType:string
}

export interface linkVO {
  documentVersionId: number ,
  entityCode: string,
  objectId: number
}

export interface ModelPartVersionLinkVO {
  primaryObjectId: number ,
  secondaryObjectId: number,
  linkType: string
}


// 查询关联物料列表
export const getEntityDocumentVersionPageApi = (params: DocumentPageVO) => {
  params.partVersionId= parseInt(<string>sessionStorage.getItem("documentId"))
  return request.get({ url: '/document/entity-document-version-link/page', params })
}

// 查询模具关联物料列表
export const getModelPartVersionPageApi = (params: ModelPageVO) => {
  params.primaryObjectId= parseInt(<string>sessionStorage.getItem("modelId"))
  params.linkType= 'p_mold_part_version_link'
  return request.get({ url: '/common/entity-link/page', params })
}

// 查询物料版本详情（单条）
export const getLinkApi = async (id: number) => {
  return await request.get({ url: '/document/entity-document-version-link/get?id=' + id })
}

// 关联物料信息新增
export const createMaterialLinkApi = (data: linkVO) => {
  return request.post({ url: '/document/entity-document-version-link/create', data })
}

// 摸具关联信息新增
export const createModelPartVersionLinkApi = (data: ModelPartVersionLinkVO) => {
  return request.post({ url: '/common/entity-link/create', data })
}

// 关联物料信息修改
export const updateMaterialLinkApi = (data: linkVO) => {
  return request.put({ url: '/document/entity-document-version-link/update', data })
}

//单条删除
export const delMaterialLinkApi = async (id: number) => {
  return await request.delete({ url: '/document/entity-document-version-link/delete?id=' + id })
}

//单条删除模具关联物料
export const delModelPartVersionApi = async (id: number) => {
  return await request.delete({ url: '/common/entity-link/delete?id=' + id })
}

// 批量删除中间表
export const delMaterialLinkListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/document/entity-document-version-link/delete-batch?ids=' + ids })
}

// 批量删除模具关联物料中间表
export const delModelPartVersionLinkListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/common/entity-link/delete-batch?ids=' + ids })
}
