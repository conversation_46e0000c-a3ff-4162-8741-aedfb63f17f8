// 查询租户列表
import request from "@/config/axios";
import {EntityVO} from "@/api/leader/myWork/entity-part-link";

export interface OrderPageReqVO extends PageParam {
  orderId: any;
  objectId: number;
  entityCode?: string
}

export interface releaseVO {
  id: number
}

export interface DetailVO {
  entityCode: string,
  objectId: number,
  partVersionId: number,
  partCode: string,
  description: string,
  specifications: string,
  quantity: string,
  deliveryTime: string,
  unitPrice: number,
  unit: number,
  totalPrice: number,
  outerBoxQuantity: string,
  mediumBoxQuantity: string,
  packMode: string,
  quanlity: string,
}

//查询销售订单明细列表数据
export const getDetailPageApi = (params: OrderPageReqVO) => {
  if (params.orderId) {
    let orderId = params.orderId
    params = {
      objectId: orderId,
      entityCode: 'p_sale_order_list'
    }
  } else {
    // @ts-ignore
    params = {
      objectId: parseInt(<string>sessionStorage.getItem("orderId")),
      entityCode: 'p_sale_order_list'
    }
  }
  return request.get({url: '/project/part-item/page', params})
}

// 新增
export const createSaleOrderDetailApi = (data: DetailVO) => {
  return request.post({ url: '/project/part-item/create', data })
}
// // 订单明细信息修改
export const updateSaleOrderDetailApi = (data: EntityVO) => {
  return request.put({ url: '/project/part-item/update', data })
}


// 查询订单详情
export const getSaleOrderDetailApi = async (id: number) => {
  return await request.get({url: '/project/part-item/get?id=' + id})
}

//单条删除
export const delSaleOrderDetailApi = async (id: number) => {
  return await request.delete({url: '/project/part-item/delete?id=' + id})
}

// // 批量删除
export const delSaleOrderDetailListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({url: '/project/part-item/delete-batch?ids=' + ids})
}

// 获取下拉框数据
export const getSelectApi = async (typeCodes: string) => {
  return await request.get({ url: "/common/type/getMapTypeCodes?typeCodes=" + typeCodes })
}
