import request from '@/config/axios'

export interface OrderVO {
  // entityCode?: string
  // objectId?: number
  orderCategory?: number
  orderCode?: string
  contractCode?: string
  customerOrderCode?: string
  orgId?: number
  orgFactoryId?: number
  orgAddress?: string
  chargePersonId?: number
  deptId?: number
  startTime?: string
  endTime?: string
  signAddress?: number
  signTime?: string
  orderDesc?: number
}

export interface VersionPageReqVO extends PageParam {
  versionManageId?: number
  partId?: number
  goodsCode?: string
  partVersionCode?: string
  partVersionName?: string
  sapno?: string
  chineseName?: string
  englishName?: string
  chineseQualityDesc?: string
  englishQualityDesc?: string
  chinesePackMode?: string
  englishPackMode?: string
  businessPersonId?: number
  goodsPersonId?: number
  isOwnBrand?: number
  isOemSample?: number
  createTime?: Date[]
}

// 查询销售订单详情
export const getSaleOrderApi = async (id: number) => {
  return await request.get({ url: '/project/order/get?id=' + id })
}

// 新增销售订单信息
export const createSaleOrderApi = async (data: OrderVO) => {
  return await request.post({ url: '/project/order/create', data })
}

// 修改销售订单
export const updateSaleOrderApi = async (data: OrderVO) => {
  return await request.put({ url: '/project/order/update', data })
}

// 获取下拉框数据
export const getSelectApi = async (typeCodes: string) => {
  return await request.get({ url: "/common/type/getMapTypeCodes?typeCodes=" + typeCodes })
}
//获取负责人下拉框数据
export const getUserApi = async () => {
  return await request.get({ url: '/system/user/list'})
}

// 查询部门列表
export const listSimpleDeptApi = async () => {
  return await request.get({ url: '/system/dept/list' })
}
export const listSimpleDeptsApi = async (searchStatus:number) => {
  return await request.get({ url: '/system/dept/list?searchStatus='+ searchStatus })
}
// 获取客户工厂信息
export const getOrgFactoryMsg = async (orgId: number) => {
  return await request.get({ url: '/common/factory/list?orgId=' +  orgId })
}

//获取客户下拉框数据
export const getOrgSelectApi = async () => {
  return await request.get({ url: '/common/org/list?orgCategory=0'})
}
