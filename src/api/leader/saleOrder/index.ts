// 查询租户列表
import request from "@/config/axios";

export interface OrderPageReqVO extends PageParam {
  objectId: string | null;
  entityCode: string | null;
  orgCategory: number;
  goodsCode?: string
  partVersionCode?: string
  chineseName?: string
  englishName?: string
  isOwnBrand?: boolean
}

export interface releaseVO {
  objectIds: object
  projectId: number
}


//查询销售订单列表数据
export const getSaleOrderPageApi = (params: OrderPageReqVO) => {
  if(window.location.pathname == '/purchaseOrder/infoIndex'){
    params.objectId = sessionStorage.getItem('marketId')
    params.entityCode = 'p_order_project_link'
  } else {
    params.objectId = null
    params.entityCode = null
  }
  return request.get({ url: '/project/order/page', params })
}

//查询订单列表数据
export const getOrderSaleApi = (params: OrderPageReqVO) => {
  params.objectId = null
  params.entityCode = null
  return request.get({ url: '/project/order/page', params })
}



// 查询订单详情
export const getSaleOrderApi = async (id: number) => {
  return await request.get({ url: '/project/order/get?id=' + id })
}



//单条删除
export const delSaleOrderApi = async (id: number) => {
  return await request.delete({ url: '/project/order/delete?id=' + id })
}

// // 批量删除
export const delSaleOrderListApi = (ids: string) => {
  ids = ids.toString()
  return request.delete({ url: '/project/order/delete-batch?ids=' + ids })
}

//关联销售项目
export const updateSaleProjectLinkApi = async (data: releaseVO) => {
  return await request.post({ url: '/project/order/project-link' , data })
}

//查询客户订单
export const getOrderPageOrgApi = (params: OrderPageReqVO) => {
  if(window.location.pathname == '/purchaseOrder/infoIndex'){
    params.objectId = sessionStorage.getItem('marketId')
    params.entityCode = 'p_order_project_link'
  } else {
    params.objectId = null
    params.entityCode = null
  }
  return request.get({ url: '/project/order/page-org', params })
}
