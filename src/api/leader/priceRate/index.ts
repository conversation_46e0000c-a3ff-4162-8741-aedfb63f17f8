import request from '@/config/axios'

export interface PriceRateVO {
  id: number
  typeId: number
  projectId: number
  estimateTypeUnit: number
  actualTypeUnit: number
  deptId: number
  status: number
  processInstanceId: string
  entityCode: string
  processStatus: number
}

export interface PriceRatePageReqVO extends PageParam {
  typeId?: number
  projectId?: number
  estimateTypeUnit?: number
  actualTypeUnit?: number
  deptId?: number
  status?: number
  processInstanceId?: string
  entityCode?: string
  processStatus?: number
  createTime?: Date[]
}

export interface PriceRateExcelReqVO {
  typeId?: number
  projectId?: number
  estimateTypeUnit?: number
  actualTypeUnit?: number
  deptId?: number
  status?: number
  processInstanceId?: string
  processStatus?: number
  createTime?: Date[]
}

// 查询单价/费率列表
export const getPriceRatePageApi = async (params: PriceRatePageReqVO) => {
  return await request.get({ url: '/cost/price-rate/page', params })
}

// 查询单价/费率列表
export const getRatiosByParentId = async (params: PriceRatePageReqVO) => {
  return await request.get({ url: '/cost/price-rate/byProject', params })
}

// 查询单价/费率详情
export const getPriceRateApi = async (id: number) => {
  return await request.get({ url: '/cost/price-rate/get?id=' + id })
}

// 新增单价/费率
export const createPriceRateApi = async (data: PriceRateVO) => {
  return await request.post({ url: '/cost/price-rate/create', data })
}

// 修改单价/费率
export const updatePriceRateApi = async (data: PriceRateVO) => {
  return await request.put({ url: '/cost/price-rate/update', data })
}

// 删除单价/费率
export const deletePriceRateApi = async (id: number) => {
  return await request.delete({ url: '/cost/price-rate/delete?id=' + id })
}

// 批量删除单价/费率
export const deleteBatchPriceRateApi = async (ids: string) => {
  ids = ids.toString()
  return await request.delete({ url: '/cost/price-rate/delete-batch?ids=' + ids })
}

// 导出单价/费率 Excel
export const exportPriceRateApi = async (params: PriceRateExcelReqVO) => {
  return await request.download({ url: '/cost/price-rate/export-excel', params })
}

export const getSelectApi = async (typeCodes: string) => {
  return await request.get({ url: "/common/type/getMapTypeCodes?typeCodes=" + typeCodes })
}
//获取多层级
export const getSelectMoreApi = async (typeCode:string) => {
  typeCode = "'" + typeCode + "'"
  return await request.get({ url: '/common/type/getChildrenTypeCodes?typeCode='+typeCode })
}
export const getCostApi = async (projectId:number,typeId:number,costType:number) => {
  return await request.get({ url: '/cost/price-rate/getPrice?projectId='+projectId+'&typeId='+typeId+'&costType='+costType })
}
