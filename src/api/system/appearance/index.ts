import request from '@/config/axios'

export interface FileVO {
  id: number
  configId: number
  path: string
  name: string
  url: string
  size: string
  type: string
  createTime: Date
}

export interface FilePageReqVO extends PageParam {
  entityCodeStr: string
  path?: string
  isUse: number
  type?: string
  createTime?: Date[]
  objectId?: number
  entityCode?: string
}


// 查询文件code字段
export const getFileCodeApi = () => {
  return request.get({ url: '/infra/file/code'})
}
// 查询文件列表
export const getFilePageApi = (params: FilePageReqVO) => {
  params.entityCodeStr = 'system,system_logo,system_background,system_ico'
  // return request.post({ url: '/infra/file/logo-list', params })
  return request.post({ url: '/infra/file/logo-list', params }).then((res)=>{
    const list = res.list
    let updateUrl = import.meta.env.VITE_BASE_URL?import.meta.env.VITE_BASE_URL+import.meta.env.VITE_SERVICE_PORT+import.meta.env.VITE_API_URL:'http://'+window.location.hostname+import.meta.env.VITE_SERVICE_PORT+import.meta.env.VITE_API_URL
    if(list){
      for(let i = 0; i<list.length;i++){
        if(list[i].url){
          list[i].url=updateUrl+list[i].url;
        }else {
          list[i].url=null;
        }

      }
    }
    return res
  })
}

// 查询背景列表
export const getSearchFilePageApi = (params: FilePageReqVO) => {
  // return request.post({ url: '/infra/file/logo-list', params })
  return request.post({ url: '/infra/file/logo-list', params }).then((res)=>{
    const list = res.list
    let updateUrl = import.meta.env.VITE_BASE_URL?import.meta.env.VITE_BASE_URL+import.meta.env.VITE_SERVICE_PORT+import.meta.env.VITE_API_URL:'http://'+window.location.hostname+import.meta.env.VITE_SERVICE_PORT+import.meta.env.VITE_API_URL
    if(list){
      for(let i = 0; i<list.length;i++){
        if(list[i].url){
          list[i].url=updateUrl+list[i].url;
        }else {
          list[i].url=null;
        }

        console.log(list)
      }
    }
    return res
  })
}


// 删除文件
export const deleteFileApi = (id: number) => {
  return request.delete({ url: '/infra/file/delete?id=' + id })
}


// 修改部门
export const updateFileApi = async (data) => {
  return await request.post({ url: '/infra/file/update', data })
}
