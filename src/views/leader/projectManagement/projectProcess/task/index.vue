<!-- 甘特图任务管理组件 -->
<template style="height: 100% !important; width: 100%; overflow: hidden">
  <div style="padding: 10px; height: 100% !important; width: 100%">
    <!-- 工具栏 -->
    <div style="display: flex; justify-content: space-between">
      <div class="toolbar-buttons">
        <!-- 视图切换按钮 -->
        <button
          v-if="searchVisible"
          type="button"
          class="gantt-button"
          :disabled="status == 0"
          title="显示甘特图表视图"
          @click="toGantt()"
        >
          显示图表
        </button>
        <button
          v-if="searchVisible"
          type="button"
          class="gantt-button"
          :disabled="status == 0"
          title="显示列表视图"
          @click="toRight()"
        >
          显示列表
        </button>
        <button
          v-if="searchVisible"
          type="button"
          class="gantt-button"
          :disabled="status == 0"
          title="显示任务图视图"
          @click="toLeft()"
        >
          显示任务图
        </button>

        <!-- 项目引用按钮 -->
        <button
          v-if="searchVisible"
          type="button"
          class="gantt-button"
          :disabled="status == 0"
          title="引用其他项目的任务"
          @click="citingOtherProjects()"
        >
          引用其他项目
        </button>
        <button
          v-if="searchVisible"
          type="button"
          class="gantt-button"
          :disabled="status == 0"
          title="引用模板项目的任务"
          @click="citingTemplateProjects()"
        >
          引用模板项目
        </button>

        <!-- 数据操作按钮 -->
        <button
          v-if="searchVisible"
          type="button"
          class="gantt-button"
          :disabled="status == 0"
          title="导出项目数据"
          @click="export_data()"
        >
          导出数据
        </button>
        <button
          v-if="searchVisible"
          type="button"
          class="gantt-button"
          :disabled="status == 0"
          title="导入员工工时数据"
          @click="import_data()"
        >
          导入工时
        </button>
        <button
          v-if="searchVisible"
          type="button"
          class="gantt-button"
          :disabled="status == 0"
          title="跳转到定额工时表"
          @click="to_quotation()"
        >
          定额工时表
        </button>
      </div>
    </div>

    <!-- 主容器 -->
    <div class="app-container" style="height: 90% !important">
      <!-- 项目选择弹窗 -->
      <XModal v-model="projectVisible" title="选择项目" width="80%" height="98%" draggable>
        <div
          v-loading="templateStatus"
          class="el-dialog-div"
          element-loading-text="努力加载中，请稍后..."
        >
          <project :key="timer" @get-value="getSonValue" />
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button :loading="actionLoading" @click="projectVisible = false"> 关闭 </el-button>
          </span>
        </template>
      </XModal>

      <!-- 员工工时弹窗 -->
      <XModal v-model="personFormVisible" title="员工工时" width="60%" height="60%" draggable>
        <div class="el-dialog-div">
          <person-modal
            :key="timer"
            :task-id="detailTaskId"
            :dept-id="detailDeptId"
            @get-value="getClose"
          />
        </div>
        <template #footer />
      </XModal>

      <!-- 项目模板选择弹窗 -->
      <XModal
        v-if="templateVisible"
        v-model="templateVisible"
        title="选择项目模板"
        width="80%"
        height="98%"
        draggable
      >
        <div
          v-loading="templateStatus"
          class="el-dialog-div"
          element-loading-text="努力加载中，请稍后..."
        >
          <project-template :key="timer" :is-check="isCheck" @get-value="getSonValue" />
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button :loading="actionLoading" @click="templateVisible = false"> 关闭 </el-button>
          </span>
        </template>
      </XModal>

      <!-- 任务详情弹窗 -->
      <XModal v-model="attachmentVisible" title="任务详情" width="90%" height="95%">
        <div class="el-dialog-div">
          <attachment
            :key="timer"
            :datas="datas"
            :object-id="detailTaskId"
            :parent-task-id="parentTaskId"
            :type="typeAdd"
            @get-value="getDetailSonValue"
          />
        </div>
        <template #footer>
          <span class="dialog-footer" />
        </template>
      </XModal>

      <!-- 甘特图容器 -->
      <div class="gantt-wrapper" style="height: 78vh; width: 100%">
        <div
          ref="ganttContainer"
          class="gantt-container"
          style="width: 100% !important; height: 93% !important"
        />
      </div>
    </div>
  </div>
</template>
<script>
import { computed, onMounted, onUnmounted, onBeforeUnmount } from 'vue'
import '@/assets/font_4094121_h65ir5ymlgd/iconfont.css'
import gantt from 'dhtmlx-gantt'
import '../../../../../../public/gantt/codebase/dhtmlxgantt.js'
import '../../../../../../public/gantt/codebase/dhtmlxgantt.css'
import * as GanttLinkApi from '@/api/leader/purchaseOrder/task-link'
import { ref, defineProps } from 'vue'
import * as GanttApi from '@/api/leader/myWork/task/index'
import moment from 'moment'
import { defaultProps, handleTree } from '@/utils/tree'
import { useRoute, useRouter } from 'vue-router'
import * as RoleApi from '@/api/leader/saleProject/project/index'
import * as SaleApi from '@/api/leader/myWork/task'
import download from '@/utils/download'
import * as OrderApi from '@/api/leader/saleOrder/basic'
import Project from '@/views/leader/projectManagement/projectProcess/task/project.vue'
import ProjectTemplate from '@/views/leader/projectManagement/projectProcess/task/template.vue'
import PersonModal from '@/views/leader/projectManagement/projectProcess/task/personModal.vue'
import Attachment from '@/views/leader/projectManagement/projectProcess/task/taskModal.vue'
import timer from '@element-plus/icons/lib/Timer'
import { defineEmits } from 'vue'
import dayjs from 'dayjs'
import router from '@/router'
import * as RegisterApi from '@/api/leader/saleProject/register'
import * as QuestionApi from '@/api/leader/myTask/question'
import * as UserApi from '@/api/system/user'
import { debounce } from 'lodash-es' // 添加 lodash-es 的引入

//获取父页面传来的数据
export default {
  computed: {
    defaultProps: computed(() => {
      return defaultProps
    })
  },
  components: { Attachment, PersonModal, ProjectTemplate, Project },
  setup() {
    const props = defineProps({
      id: Number,
      status: Number
    })
    const message = useMessage() // 消息弹窗
    const route = useRoute()
    const ganttContainer = ref(null)
    // 定义甘特图事件处理器数组，确保在整个组件中可用
    const ganttEventHandlers = ref([])
    const datas = ref({})
    const emit = defineEmits(['${entity}-updated'])
    const isActive = ref(false)
    const isOff = ref(true)
    const isCheck = ref(0)
    const templateStatus = ref(false)
    const projectId = ref(null)
    const actionLoading = ref(false)
    const personFormVisible = ref(false)
    const projectVisible = ref(false)
    const attachmentVisible = ref(false)
    const searchVisible = ref(true)
    const templateVisible = ref(false)
    const detailDeptId = ref(null)
    const projectStatus = ref(null)
    const projectNature = ref(null)
    const formInline = ref({
      taskName: '',
      taskCode: '',
      workOrderNumber: '',
      deptId: ''
    })
    const form = ref({
      name: '',
      region: ''
    })
    const loadingbut = ref(false)
    const taskStatusCode = ref('')
    const loadingbuttext = ref('保存')
    const endTimeVisible = ref(false)
    const deptList = ref([])
    const isMilestoneList = ref([
      { value: 1, label: '是' },
      { value: 0, label: '否' }
    ])
    const taskStatusOption = ref([
      { value: 1, label: '未开始' },
      { value: 2, label: '进行中' },
      { value: 3, label: '已完成' },
      { value: 4, label: '已驳回' },
      { value: 5, label: '已中止' }
    ])
    const attachments = ref('附件')
    const task = ref(null)
    const { push } = useRouter()
    const startTime = ref(null)
    const endTime = ref(null)
    const userOption = ref([]) // 定义用户选项数组
    const task_category = ref([])
    const projectCode = ref('')
    const projectPhase = ref('')
    const projectName = ref(1)
    const orgName = ref(null)
    const taskID = ref(null)
    const deptOption = ref([])
    const taskStatus = ref([])
    const attachment = ref(null)
    const frontTaskOption = ref([])
    const taskId = ref(null)
    const wideColumnDisplay = ref(false)
    const tasks = ref({
      data: [],
      links: []
    })

    const linkTaskId = ref(null)
    const detailTaskId = ref(null)
    const parentTaskId = ref(null)
    const typeAdd = ref(null)
    const sameVisible = ref(true) //判断是否为同一项目，用来关闭多余的弹出框 true；是， false:不是
    const showTodayButton = ref(false) // 默认隐藏“今天”按钮

    onMounted(async () => {
      // 获取用户列表
      // const gantt = window.gantt

      try {
        QuestionApi.getUserListApi(route.query.id).then((data) => {
          userOption.value = data // 直接赋值，而不是 push
        })
      } catch (error) {
        console.error('获取用户列表失败:', error)
      }

      // 设置全局错误处理，防止甘特图操作导致的未捕获错误
      window.addEventListener(
        'error',
        function (event) {
          if (
            event.error &&
            event.error.message &&
            (event.error.message.includes('getService') ||
              event.error.message.includes('Cannot read properties of undefined'))
          ) {
            console.warn('捕获到甘特图相关错误:', event.error.message)
            // 防止错误向上冒泡
            event.preventDefault()
            event.stopPropagation()
          }
        },
        true
      )

      try {
        // 确保gantt已正确初始化
        if (gantt && gantt.clearAll) {
          gantt.clearAll()
        }
      } catch (error) {
        console.error('清除甘特图数据失败:', error)
      }

      // $_initDataProcessor()
      sessionStorage.removeItem('taskId')
      if (
        sessionStorage.getItem('projectId') === null ||
        sessionStorage.getItem('projectId') != route.query.id
      ) {
        //说明是首次打开任务分配或者切换了新的项目
        sameVisible.value = false
        sessionStorage.setItem('projectId', route.query.id) //存入新的projectId
      } else {
        //说明之前打开过
        sameVisible.value = true
      }
      let objectId = parseInt(route.query.id)
      let params = {}
      params.entityCode = 't_task_entity_participant_code'
      params.objectId = objectId
      if (!objectId || objectId == 'NAN' || objectId == undefined) {
        push({ path: '/home/<USER>' })
        return false
      }
      //新增任务
      gantt.attachEvent('onBeforeTaskAdd', function (id, task) {
        // 阻止默认的添加行为
        return false
      })
      gantt.attachEvent('onBeforeTaskDelete', function (id, task) {
        //any custom logic here
        return false
      })

      gantt.attachEvent('onTaskCreated', function (task) {
        // 阻止默认的lightbox
        const parent = gantt.getTask(task.parent)

        // 检查父任务状态
        if (parent.taskStatusCode == 3 || parent.taskStatusCode == 6) {
          message.error('已结束的任务不能新增子任务，请新建一级任务')
          return false
        }

        // 打开自定义弹窗
        addAttachment(task.parent)
        return false // 返回 false 阻止默认行为
      })

      // 1. 禁用所有原生弹出框相关配置
      gantt.config.show_lightbox = false
      gantt.config.details_on_create = false
      gantt.config.show_quick_info = false
      gantt.config.details_on_dblclick = false
      gantt.config.quick_info_detached = false

      // 2. 添加任务层来处理项目任务的点击
      gantt.config.readonly = false // 先设置为全局只读

      // 3. 自定义任务渲染
      gantt.templates.task_class = function (start, end, task) {
        if (task.parent === 0) {
          return 'project-task-readonly'
        }
        return ''
      }

      // 4. 双击事件处理
      gantt.attachEvent('onTaskDblClick', async function (id, e) {
        try {
          const task = gantt.getTask(id)

          // 修改安全检查逻辑，即使找不到任务对象也允许打开编辑页面
          if (!task) {
            console.warn('甘特图中未找到任务对象:', id, '但仍将尝试打开编辑页面')
            // 继续处理，不要返回false
          } else if (task.parent === 0) {
            // 如果是项目任务，不允许打开弹框
            console.log('项目不可以弹框')
            return false
          }

          // 如果在特定路径下，打开自定义弹框
          if (window.location.pathname === '/projectManagement/projectProcess/infoIndex') {
            sessionStorage.removeItem('taskId')
            openAttachment(id)
            try {
              if (gantt && gantt.clearAll) {
                gantt.clearAll()
                await getListData(-1)
                if (ganttContainer.value && gantt.init) {
                  gantt.init(ganttContainer.value)
                  if (tasks.value) {
                    gantt.parse(tasks.value)
                  }
                }
              }
            } catch (error) {
              console.error('刷新甘特图失败:', error)
            }
            return false
          }

          // 其他情况也打开自定义弹框
          openAttachment(id)
          return false // 阻止默认的双击行为
        } catch (error) {
          console.error('双击任务处理出错:', error, '但仍将尝试打开编辑页面')
          // 即使出现错误，也尝试打开编辑页面
          openAttachment(id)
          return false
        }
      })

      // 5. 禁用项目任务的编辑
      gantt.attachEvent('onBeforeLightbox', function (id) {
        const task = gantt.getTask(id)
        if (task.parent === 0) {
          return false
        }
        return true
      })

      // gantt.config.show_chart = false;
      gantt.config.work_time = false
      gantt.config.min_duration = 0
      gantt.config.round_dnd_dates = false
      gantt.config.auto_scheduling_initial = true
      gantt.config.fit_tasks = true
      gantt.config.show_progress = true
      gantt.config.show_task_cells = true
      // gantt.config.smart_rendering = true;
      gantt.config.smart_scales = true

      gantt.config.drag_links = true
      gantt.config.drag_project = true
      gantt.config.drag_move = true // 允许拖动任务条来调整时间线
      gantt.config.drag_progress = true // 允许拖动进度条
      gantt.config.drag_resize = true
      gantt.config.wide_form = true
      gantt.config.xml_date = '%YYYY-%mm-%dd %H:%i:%s'

      gantt.config.scale_height = 50 // 设置时间轴头部高度
      gantt.config.row_height = 80 // 增加行高，为线段留出更多空间
      gantt.config.min_column_width = 20 // 设置最小列宽
      gantt.config.scale_offset_minimal = true // 优化时间轴显示
      // 加载插件 - 确保所有需要的插件都被加载
      gantt.plugins({
        marker: true,
        fullscreen: true,
        tooltip: false,
        layers: true
      })

      // 添加今天的标记
      const today = new Date()
      const marker = gantt.addMarker({
        start_date: today,
        css: 'today'
      })

      // 设置时间范围
      // gantt.config.start_date = start_date
      // gantt.config.end_date = end_date
      gantt.config.min_column_width = wideColumnDisplay.value ? 60 : 20 // 根据宽格显示设置列宽

      gantt.config.scale_height = 50 // 设置时间轴头部高度
      gantt.config.row_height = 80 // 增加行高，为线段留出更多空间
      gantt.config.min_column_width = 20 // 设置最小列宽
      gantt.config.scale_offset_minimal = true // 优化时间轴显示
      gantt.config.step = 1
      gantt.config.date_scale = '%Y'
      gantt.config.order_branch = true //允许在父分支内拖动任务，并允许您的用户在任何地方创建里程碑
      gantt.config.drag_links = true
      gantt.config.drag_project = true
      gantt.config.drag_move = true // 允许拖动任务条来调整时间线
      gantt.config.drag_progress = true // 允许拖动进度条
      gantt.config.drag_resize = true
      gantt.config.wide_form = true
      gantt.config.xml_date = '%YYYY-%mm-%dd %H:%i:%s'
      gantt.config.scale_unit = 'year'
      gantt.config.step = 1
      gantt.config.date_scale = '%Y'
      gantt.config.order_branch = true //允许在父分支内拖动任务，并允许您的用户在任何地方创建里程碑
      gantt.config.row_height = 24 //设置行
      gantt.config.scale_height = 50 //设置列
      gantt.templates.scale_cell_class = function (date) {
        if (date.getDay() == 0 || date.getDay() == 6) {
          return 'weekend'
        }
      }
      gantt.templates.timeline_cell_class = function (item, date) {
        if (date.getDay() == 0 || date.getDay() == 6) {
          return 'weekend'
        }
      }
      // gantt.config.details_on_dblclick = false;
      //当右侧不止显示年份时，可以添加展示月日，添加一个就加一行
      gantt.config.subscales = [
        { unit: 'month', step: 1, format: '%m月' },
        { unit: 'day', step: 1, date: '%d' }
        // {unit: "hour", step: 1, format: "%G"},
        // {unit: "minute", step: 30, format: "%i"}
      ]
      gantt.setWorkTime({ hours: ['8:30-12:00', '13:00-17:30'] })
      gantt.setWorkTime({ day: 6 })
      gantt.setWorkTime({ day: 0 })
      // gantt.ignore_time = function (date) {
      //   if (date.getHours() < 8 || date.getHours() > 17) {
      //     return true;
      //   }
      //   return false;
      // };
      // gantt.i18n.setLocale("cn");//中文版
      gantt.config.skip_off_time = true
      gantt.config.auto_scheduling = true
      gantt.config.scrollX = true
      gantt.plugins({
        click_drag: true,
        drag_timeline: true, // 拖动图
        marker: true, // 时间标记
        export_api: true,
        auto_scheduling: true,
        wbs: true,
        fullscreen: true, // 全屏
        tooltip: false, // 鼠标经过时信息
        undo: true,
        multiselect: true
      }) // 开启marker插件
      gantt.config.show_errors = false
      // gantt.config.details_on_dblclick = true;
      gantt.config.auto_scheduling = true
      gantt.locale.labels.section_task = '任务信息'
      gantt.locale.labels.section_time = '时间范围'
      gantt.config.date_picker = '%Y-%m-%d'
      gantt.config.month_day = '%d日 %M'
      gantt.config.day_date = '%Y年 %m月 %d日 '
      gantt.config.scale_cell_date = '%d日 %M'
      gantt.config.grid_resizer_column_attribute = 'data-column-index'
      gantt.config.min_column_width = 20
      // gantt.config.duration_unit = "hour";
      gantt.config.scroll_size = 40
      gantt.config.duration_unit = 'minute'
      const local = 'zh'
      gantt.i18n.setLocale(local)
      const dayFormatter = gantt.ext.formatters.durationFormatter({
        enter: 'hour',
        store: 'minute',
        format: 'hour',
        hoursPerDay: 8,
        hoursPerWeek: 56,
        daysPerMonth: 30,
        short: false,
        labels: {
          minute: { full: '分钟', plural: '分钟', short: '分' },
          hour: { full: '小时', plural: '小时', short: '时' },
          day: { full: '天', plural: '天', short: '天' },
          week: { full: '周', plural: '周', short: '周' },
          month: { full: '月', plural: '月', short: '月' },
          year: { full: '年', plural: '年', short: '年' }
        }
      })
      // dayFormatter._getLabelForConvert = function (value, unit, short) {
      //   const labels = this._config.labels;
      //   const label = labels[unit];
      //   console.log(value,'value')
      //   if (short) {
      //
      //     return '' + value + label.short;
      //   }
      //   let time=value;
      //   if (local === 'zh') {
      //     // 每天工作 8 小时的周期计算
      //     if (value / 1440 <= 9) {
      //       if (value / 1440 === 9) {
      //         time = 8 + ' ' + (value !== 1 ? label.plural : label.full);
      //       } else {
      //         time = 0;
      //       }
      //     } else {
      //       time = Number.isInteger(value / (1440 * 8))
      //         ? (value / (1440 * 8)).toFixed(0)
      //         : (value / (1440 * 8)).toFixed(1);
      //       time += ' ' + (value === 0 || value === 1 ? label.full : label.plural);
      //     }
      //     return time;
      //   } else {
      //     time = Number.isInteger(value / 1440)
      //       ? (value / 1440).toFixed(0)
      //       : (value / 1440).toFixed(1);
      //     return time + ' ' + (value !== 1 ? label.plural : label.full);
      //   }}
      dayFormatter._getLabelForConvert = function (value, unit, short) {
        const labels = this._config.labels
        const label = labels[unit]
        let time

        if (unit === 'hour') {
          time = value / 60 // 转为小时
        } else {
          time = value / (60 * 8) // 转为工作日
        }

        time = time.toFixed(1) // 保留一位小数

        return short
          ? `${time}${label.short}`
          : `${time} ${value === 1 ? label.full : label.plural}`
      }

      gantt.config.layout = {
        css: 'gantt_container',
        cols: [
          {
            width: 720,
            min_width: 300,
            rows: [
              { view: 'grid', scrollX: 'gridScroll', scrollable: true, scrollY: 'scrollVer' },
              { view: 'scrollbar', id: 'gridScroll', group: 'horizontal' }
            ]
          },
          { resizer: true, width: 1 },
          {
            rows: [
              { view: 'timeline', scrollX: 'scrollHor', scrollY: 'scrollVer' },
              { view: 'scrollbar', id: 'scrollHor', group: 'horizontal' }
            ]
          },
          { view: 'scrollbar', id: 'scrollVer' }
        ]
      }
      var gridDateToStr = gantt.date.date_to_str('%Y-%m-%d')
      gantt.templates.grid_date_format = function (date, column) {
        if (column === 'planEndTime') {
          return gridDateToStr(new Date(date.valueOf() - 1))
        } else {
          return gridDateToStr(date)
        }
      }

      function calculateWorkingHours(start_date, end_date) {
        let workingHours = 0
        let current = new Date(start_date)

        // 设置工作时间段
        const morningStartHour = 8.5 // 8:30 AM
        const morningEndHour = 12.0 // 12:00 PM
        const afternoonStartHour = 13.0 // 1:00 PM
        const afternoonEndHour = 17.5 // 5:30 PM

        while (current < end_date) {
          const dayStart = new Date(current)

          // 早上工作时间段
          const morningStart = new Date(dayStart.setHours(8, 30, 0, 0))
          const morningEnd = new Date(dayStart.setHours(12, 0, 0, 0))

          // 下午工作时间段
          const afternoonStart = new Date(dayStart.setHours(13, 0, 0, 0))
          const afternoonEnd = new Date(dayStart.setHours(17, 30, 0, 0))

          // 如果当前时间是工作日
          if (gantt.isWorkTime(current)) {
            // 计算上午工作时间
            if (current < morningStart) {
              current = morningStart
            }

            if (current < morningEnd && end_date > morningStart) {
              let diffHours = (Math.min(morningEnd, end_date) - current) / 36e5
              workingHours += diffHours
              current = morningEnd
            }

            // 计算下午工作时间
            if (current < afternoonStart) {
              current = afternoonStart
            }

            if (current < afternoonEnd && end_date > afternoonStart) {
              let diffHours = (Math.min(afternoonEnd, end_date) - current) / 36e5
              workingHours += diffHours
              current = afternoonEnd
            }
          }

          // 移动到下一天
          current.setDate(current.getDate() + 1)
          current.setHours(0, 0, 0, 0) // 设置为午夜
        }

        return workingHours
      }

      // 将 userOption 转换为所需格式
      const formattedUserOptions = computed(() => {
        return userOption.value.map((user) => ({
          key: user.id, // 假设原数据中 id 字段名为 id
          label: user.nickname // 假设原数据中 name 字段名为 name
        }))
      })

      // 使用转换后的数据
      gantt.serverList('chargePersonName', formattedUserOptions.value)
      gantt.config.inline_editors_multiselect_open = true
      //表格字段设置
      gantt.config.columns = [
        {
          name: 'wbs',
          label: '序号',
          align: 'left',
          width: 55,
          resize: true,
          template: gantt.getWBSCode
        },
        {
          name: 'overdue',
          label: '  ',
          width: 30,
          template: function (obj) {
            let planEndTime = gantt.date.parseDate(obj.planEndTime, 'xml_date')
            if (planEndTime) {
              let now = new Date()
              let overdue = 0
              //任务状态; 1未开始、2进行中、3已完成 4 已驳回 5 审批中
              if (
                !obj.taskStatusCode ||
                obj.taskStatusCode == 1 ||
                obj.taskStatusCode == 2 ||
                obj.taskStatusCode == 4 ||
                obj.taskStatusCode == 5
              ) {
                //对于未完成的任务，需要判断当前时间语结束时间的关系
                if (now > planEndTime) {
                  overdue = Math.ceil(
                    Math.abs((now.getTime() - planEndTime.getTime()) / (24 * 60 * 60 * 1000))
                  )
                }
              } else if (obj.taskStatusCode == 3 && obj.actualEndTime) {
                //完成的项目
                let actualEndTime = gantt.date.parseDate(obj.actualEndTime, 'xml_date')
                if (actualEndTime > planEndTime) {
                  overdue = Math.ceil(
                    Math.abs(
                      (actualEndTime.getTime() - planEndTime.getTime()) / (24 * 60 * 60 * 1000)
                    )
                  )
                }
              }
              overdue = overdue - 1
              if (overdue > 0) {
                //逾期
                return '<div class="overdue-indicator">!</div>'
              }
            }
            return '<div></div>'
          }
        },
        { name: 'add', width: 44, min_width: 44, max_width: 44, resize: false },
        {
          name: 'delete',
          label: '',
          width: 60,
          align: 'center',
          template: function (task) {
            return '<span class="delete-btn" style="color: red;cursor: pointer">X</span>'
          }
        },
        {
          name: 'taskStatus',
          label: '状态',
          width: 80,
          align: 'center',
          resize: true
        },
        {
          name: 'text',
          label: '任务名称',
          width: '280',
          tree: true,
          align: 'left',
          template: function (item) {
            let content = item.text || ''
            if (item.parent === 0) {
              return `<div style="font-weight: bold; font-size: 14px;">${content}</div>`
            }
            return content
          },
          resize: true
        },
        {
          name: 'chargePersonName',
          label: '负责人',
          width: '100',
          align: 'center',
          resize: true,
          template: function (item) {
            let content
            if (!item.chargePersonCodes) {
              content = ''
            } else if (item.parent == 0 && typeof item.chargePersonCodes == 'string') {
              content = item.chargePersonCodes
            } else {
              const user = userOption.value.find((user) => user.id === item.chargePersonCodes)
              content = user ? user.nickname : '未知人员'
            }
            if (item.parent === 0) {
              return `<div style="font-weight: bold; font-size: 14px;">${content}</div>`
            }
            return content
          }
        },
        {
          name: 'planStartTime',
          label: '计划开始',
          width: '100',
          align: 'center',
          template: function (item) {
            if (!item.planStartTime || item.planStartTime === 'Invalid date') {
              return ''
            }
            return dayjs(item.planStartTime).format('YYYY-MM-DD')
          },
          resize: true
        },
        {
          name: 'planEndTime',
          label: '计划结束',
          width: '100',
          align: 'center',
          template: function (item) {
            if (!item.planEndTime || item.planEndTime === 'Invalid date') {
              return ''
            }
            return dayjs(item.planEndTime).format('YYYY-MM-DD')
          },
          resize: true
        },
        {
          name: 'actualStartTime',
          label: '实际开始',
          width: '100',
          align: 'center',
          template: function (item) {
            if (!item.actualStartTime || item.actualStartTime === 'Invalid date') {
              return ''
            }
            return dayjs(item.actualStartTime).format('YYYY-MM-DD')
          },
          resize: true
        },
        {
          name: 'actualEndTime',
          label: '实际结束',
          width: '100',
          align: 'center',
          resize: true,
          template: function (item) {
            if (!item.actualEndTime || item.actualEndTime === 'Invalid date') {
              return ''
            }
            return dayjs(item.actualEndTime).format('YYYY-MM-DD')
          }
        },
        { name: 'deptName', label: '负责人部门', width: '100', align: 'center', resize: true },
        {
          name: 'progress',
          label: '当前进度',
          width: 80,
          align: 'center',
          template: function (item) {
            if (item.taskStatusCode == 3 || item.progress >= 1) return '100%'
            if (item.progress === 0) return '0'
            return Math.round(item.progress * 100) + '%'
          },
          resize: true
        },

        {
          name: 'predecessors',
          label: '前置条件',
          width: 100,
          align: 'left',
          resize: true,
          template: function (task) {
            var links = task.$target
            var labels = []
            for (var i = 0; i < links.length; i++) {
              var link = gantt.getLink(links[i])
              var target = gantt.getTask(link.source)
              if (target) {
                if (target.$wbs) {
                  labels.push(target.$wbs)
                }
              }
            }
            return labels.join(', ')
          }
        },
        { name: 'creator', label: '创建人', width: '130', align: 'center', resize: true },
        {
          name: 'postTaskStartDays',
          label: '后置任务几天后开始',
          width: '130',
          align: 'center',
          resize: true
        },
        {
          name: 'postTaskEndDays',
          label: '后置任务几天后结束',
          width: '130',
          align: 'center',
          resize: true
        },
        {
          name: 'importantPath',
          label: '是否关键路径',
          width: '100',
          align: 'center',
          resize: true,
          template: function (item) {
            let content
            if (!item.importantPath) {
              content = ''
            } else if (item.importantPath == 1) {
              content = '是'
            } else {
              content = '否'
            }
            return content
          }
        },
        {
          name: 'flagBit',
          label: '是否受上级时间约束',
          width: '100',
          align: 'center',
          resize: true,
          template: function (item) {
            let content
            if (!item.flagBit) {
              content = ''
            } else if (item.flagBit == 2) {
              content = '是'
            } else {
              content = '否'
            }
            return content
          }
        },
        {
          name: 'createTime',
          label: '创建时间',
          width: '170',
          align: 'center',
          resize: true,
          template: function (item) {
            if (!item.createTime || item.createTime === 'Invalid date') {
              return ''
            }
            return dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss')
          }
        }
      ]
      //从后端获取数据
      await getTree() //获取部门
      await getListData(-1)
      await getProjectTaskLinkPageApi(-1)
      gantt.init(ganttContainer.value)
      gantt.parse(tasks.value)

      // 保存事件ID列表，以便在组件卸载时可以正确移除所有事件监听器
      // 此注释保留，但移除下面的变量定义，因为已经在组件顶层定义了ganttEventHandlers

      // 如果已经绑定了onTaskClick事件，先移除它
      if (gantt.$_taskClickHandler) {
        gantt.detachEvent(gantt.$_taskClickHandler)
      }

      // 绑定删除按钮点击事件
      gantt.$_taskClickHandler = gantt.attachEvent('onTaskClick', function (id, e) {
        // 检查点击的是否是删除按钮
        if (e.target && e.target.classList.contains('delete-btn')) {
          console.log('点击了删除按钮')
          handleDeleteTask(id)
          e.preventDefault()
          return false // 阻止事件继续传播
        }
        return true
      })

      // 保存事件ID用于清理
      ganttEventHandlers.value.push(gantt.$_taskClickHandler)

      $_initGanttEvents()

      //父子任务分级展示
      function createBox(sizes, class_name) {
        var box = document.createElement('div')
        box.style.cssText = [
          'height:' + sizes.height + 'px',
          'line-height:' + sizes.height + 'px',
          'width:' + sizes.width + 'px',
          'top:' + sizes.top + 'px',
          'left:' + sizes.left + 'px',
          'position:absolute'
        ].join(';')
        box.className = class_name
        return box
      }

      gantt.templates.grid_row_class = gantt.templates.task_class = function (start, end, task) {
        var css = []
        if (gantt.hasChild(task.id)) {
          css.push('task-parent')
        }
        if (!task.$open && gantt.hasChild(task.id)) {
          css.push('task-collapsed')
        }
        return css.join(' ')
      }
      if (gantt && typeof gantt.addTaskLayer === 'function' && ganttContainer.value) {
        if (ganttContainer.value) {
          gantt.init(ganttContainer.value)

          // 检查 addTaskLayer 方法是否存在
          if (typeof gantt.addTaskLayer === 'function') {
            gantt.addTaskLayer(function show_hidden(task) {
              if (!task.$open && gantt.hasChild(task.id)) {
                var sub_height = gantt.config.row_height - 5,
                  el = document.createElement('div'),
                  sizes = gantt.getTaskPosition(task)
                var sub_tasks = gantt.getChildren(task.id)
                var child_el
                for (var i = 0; i < sub_tasks.length; i++) {
                  var child = gantt.getTask(sub_tasks[i])
                  var child_sizes = gantt.getTaskPosition(child)
                  child_el = createBox(
                    {
                      height: sub_height,
                      top: sizes.top,
                      left: child_sizes.left,
                      width: child_sizes.width
                    },
                    'child_preview gantt_task_line'
                  )
                  child_el.innerHTML = child.text
                  el.appendChild(child_el)
                }
                return el
              }
              return false
            })
          } else {
            console.warn(
              'addTaskLayer method is not available in the current version of dhtmlx-gantt.'
            )
          }
        }
      }

      //---------逾期-----------
      gantt.templates.rightside_text = function (start, end, task) {
        if (task.planEndTime) {
          let planEndTime = gantt.date.parseDate(task.planEndTime, 'xml_date')
          let now = new Date()
          let overdue = 0
          //任务状态 1未开始  2进行中  3已完成
          if (
            !task.taskStatusCode ||
            task.taskStatusCode == 1 ||
            task.taskStatusCode == 2 ||
            task.taskStatusCode == 4 ||
            task.taskStatusCode == 5
          ) {
            //对于未完成的任务，需要判断当前时间语结束时间的关系
            if (now > planEndTime) {
              overdue =
                Math.ceil(
                  Math.abs((now.getTime() - planEndTime.getTime()) / (24 * 60 * 60 * 1000))
                ) - 1
            }
          } else if (task.taskStatusCode == 3 && task.actualEndTime) {
            //完成的项目
            let actualEndTime = gantt.date.parseDate(task.actualEndTime, 'xml_date')
            if (actualEndTime > planEndTime) {
              overdue =
                Math.ceil(
                  Math.abs(
                    (actualEndTime.getTime() - planEndTime.getTime()) / (24 * 60 * 60 * 1000)
                  )
                ) - 1
            }
          }
          if (overdue > 0) {
            //逾期
            if (task.type == gantt.config.types.milestone) {
              let text =
                "<b style='color: red;font-size: 10px;line-height: 20px;'>逾期: " +
                overdue +
                ' 天</b>' +
                '<span style="font-size=10px;line-height: 20px">' +
                task.text +
                '</span>'
              return text
            } else {
              let text =
                "<b style='color: red;font-size: 10px;line-height: 20px;'>逾期: " +
                overdue +
                ' 天</b>'
              return text
            }
          }
        }
        if (task.type == gantt.config.types.milestone) {
          return '<span style="font-size=10px;line-height: 20px">' + task.text + '</span>'
        }
        return ''
      }

      gantt.attachEvent('onRowDragEnd', async function (id, target) {
        let ganttUpdateVo = []
        if (tasks.value.data.length > 1) {
          gantt.eachTask(function (task) {
            ganttUpdateVo.push({
              id: task.id,
              taskNo: task.$wbs
              // taskName:task.text,
            })
          })
          let data = { ganttUpdateVo }
          let res = await GanttLinkApi.updateTaskNoApi(data)
          if (res) {
            // gantt.clearAll();
            await getListData(-1)
            // gantt.init(ganttContainer.value);
            gantt.parse(tasks.value)
            message.success('操作成功')
          }
        }
      })
      //选择任务
      gantt.attachEvent('onMultiSelect', function (id) {
        // var task = gantt.getTask(id);
        let taskId = gantt.getLastSelectedTask()
        var task = gantt.getTask(taskId)
        return true
      })
      //更新任务
      gantt.attachEvent('onAfterTaskUpdate', async function (id, item) {
        gantt.refreshData()
        let ganttUpdateVo = [] //请求参数
        // 执行相应的操作...
        let data = {}
        data.id = id
        data.entityCode = 's_task_create'
        data.objectId = item.objectId
        data.parentTaskId = parseInt(item.parent)
        data.taskName = item.text
        data.taskDesc = item.taskDesc
        data.chargePersonId = parseInt(item.chargePersonId)
        data.preTaskData = item.frontTask
        data.isThreeReview = parseInt(item.isThreeReview[0]) == 1 ? 1 : 0
        data.isTwoReview = parseInt(item.isTwoReview[0]) == 1 ? 1 : 0
        data.isProjectMeet = parseInt(item.isProjectMeet[0]) == 1 ? 1 : 0
        data.scheme = item.scheme
        data.priority = parseInt(item.priority)
        // data.taskStatus=data.taskStatusName
        data.chargePersonId = parseInt(item.chargePersonId)
        data.planStartTime = item.start_date.getTime()
        if (item.type != 'milestone') {
          //不是里程碑
          const date = new Date(item.start_date)
          date.setDate(date.getDate() + (item.duration - 1))
          data.planEndTime = date.getTime()
        } else {
          data.planEndTime = item.planEndTime.getTime()
        }
        data.duration = item.duration
        data.linkType = item.type === 'task' ? 1 : item.parent === 0 ? 4 : 2
        data.progress = item.progress || 0
        data.taskCategory = item.type === 'task' ? 1 : item.parent === parent ? 4 : 2
        ganttUpdateVo.push(data) //存入弹出框页面的数据
        //以下是获取所有父级的数据，如果子级的周期 开始时间、结束时间、状态等发生变化那么父级也要联动
        // 获取该数据所有父级的数据
        for (var i = 0; i < 100; i++) {
          var parentId = gantt.getParent(id)
          if (parentId) {
            let parentData = gantt.getTask(parentId)
            let paramData = {}
            paramData.id = parentData.id
            paramData.entityCode = 's_task_create'
            paramData.objectId = item.objectId
            paramData.parentTaskId = parentData.parent
            paramData.taskName = parentData.text
            paramData.taskDesc = parentData.taskDesc
            paramData.chargePersonId = parentData.chargePersonId
            paramData.preTaskData = parentData.frontTask
            paramData.isThreeReview = parentData.isThreeReview
            paramData.isTwoReview = parentData.isTwoReview
            paramData.isProjectMeet = parentData.isProjectMeet
            paramData.scheme = parentData.scheme
            paramData.taskStatus = parentData.taskStatusCode
            paramData.chargePersonId = parentData.chargePersonId
            paramData.planStartTime = parentData.start_date.getTime()
            paramData.planEndTime = parentData.end_date.getTime()
            paramData.duration = parentData.duration
            paramData.linkType = parentData.type === 'task' ? 1 : parentData.parent === 0 ? 4 : 2
            paramData.progress = parentData.progress || 0
            paramData.taskCategory =
              parentData.type === 'task' ? 1 : parentData.parent === 0 ? 4 : 2
            ganttUpdateVo.push(paramData) //存入父级的数据
            id = parentId
            if (parentData.parent == 0) {
              paramData.planEndTime = parentData.end_date.getTime() - 24 * 60 * 60 * 1000
              break
            }
          } else {
            break
          }
        }
        let datas = { ganttUpdateVo }
        let res = await SaleApi.updateGanttSaleApi(datas)
        gantt.updateTask(id)
        if (res) {
          await that.getListData(-1)
          await that.getProjectTaskLinkPageApi(-1)
          message.success('修改任务成功')
          return true
        } else {
          return false
        }
      })

      // 监听任务更新事件
      gantt.attachEvent('onAfterTaskUpdate', async (id, item) => {
        // 如果不允许更新或者是初始加载，直接返回
        if (!allowUpdate) {
          return
        }

        // 获取原始任务数据进行比较
        const originalTask = tasks.value.data.find((task) => task.id === id)
        if (!originalTask) {
          return
        }

        // 检查是否有实际的修改
        const hasChanges =
          new Date(originalTask.planStartTime).getTime() !==
            new Date(item.planStartTime).getTime() ||
          new Date(originalTask.planEndTime).getTime() !== new Date(item.planEndTime).getTime() ||
          originalTask.chargePersonCodes !== item.chargePersonCodes

        if (!hasChanges) {
          return
        }

        // 构造更新数据
        const ganttUpdateVo = [
          {
            id: id,
            planStartTime: new Date(item.planStartTime).getTime(),
            planEndTime: new Date(item.planEndTime).getTime(),
            entityCode: 's_task_create',
            taskStatus: item.taskStatusCode,
            chargePersonId: item.chargePersonCodes,
            objectId: item.objectId
          }
        ]

        try {
          // 临时禁用更新避免重复调用
          allowUpdate = false

          if (item.parent === 0) {
            await getListData(-1)
            gantt.parse(tasks.value)
            return message.error('项目信息不允许修改')
          }

          const res = await SaleApi.updateGanttSaleApi({ ganttUpdateVo })
          if (res) {
            message.success('修改成功')
            await getListData(-1)
            gantt.parse(tasks.value)
          }
        } catch (error) {
          message.error('修改失败')
          await getListData(-1)
          gantt.parse(tasks.value)
        }
      })

      // 监听编辑器关闭事件，用于处理取消编辑的情况
      gantt.attachEvent('onLightboxCancel', async () => {
        // 重新加载数据，还原修改
        await getListData(-1)
        gantt.parse(tasks.value)
      })

      // 在初始化配置中添加
      gantt.attachEvent('onBeforeLightbox', function (id) {
        const task = gantt.getTask(id)
        if (task.parent === 0) {
          return false // 禁止项目类型任务的编辑
        }
        return true
      })

      // 禁止项目任务的编辑器
      gantt.attachEvent('onBeforeTaskChanged', function (id, mode, task) {
        const originalTask = gantt.getTask(id)
        if (originalTask.parent === 0) {
          return false // 阻止项目类型任务的修改
        }
        return true
      })

      // 添加一个变量来控制是否允许更新
      let allowUpdate = false
      let isInitialLoad = true

      // 在数据加载完成后允许更新
      gantt.attachEvent('onParse', () => {
        if (isInitialLoad) {
          isInitialLoad = false
          return
        }
        setTimeout(() => {
          allowUpdate = true
        }, 500)
      })

      // 在数据清空时禁止更新
      gantt.attachEvent('onBeforeClear', () => {
        allowUpdate = false
        return true
      })
      // 修改1: 在数据加载前设置初始布局为表格视图
      toRight() // 先设置布局配置

      await getListData(-1)

      try {
        // 确保DOM元素存在且gantt没有被销毁
        if (ganttContainer.value && gantt && gantt.init) {
          gantt.init(ganttContainer.value)
          if (tasks.value) {
            gantt.parse(tasks.value)
          }
        } else {
          console.error('gantt初始化失败：容器不存在或gantt实例未正确加载')
        }
      } catch (error) {
        console.error('gantt初始化或数据加载失败:', error)
      }
    })
    onUnmounted(() => {
      // 重置确认框状态标志
      window.isConfirmDialogOpen = false

      // 清理所有甘特图事件监听器
      if (ganttEventHandlers.value && ganttEventHandlers.value.length > 0) {
        ganttEventHandlers.value.forEach((handlerId) => {
          if (handlerId && gantt.detachEvent) {
            try {
              gantt.detachEvent(handlerId)
            } catch (e) {
              console.error('移除事件监听器失败:', e)
            }
          }
        })
        // 清空事件ID列表
        ganttEventHandlers.value = []
      }

      // 特别移除onTaskClick事件监听器
      if (gantt.$_taskClickHandler && gantt.detachEvent) {
        try {
          gantt.detachEvent(gantt.$_taskClickHandler)
          gantt.$_taskClickHandler = null
        } catch (e) {
          console.error('移除onTaskClick事件监听器失败:', e)
        }
      }

      // 重置事件初始化标志
      gantt.$_eventsInitialized = false

      // 使用 gantt 而不是 Gantt
      try {
        // 检查gantt是否存在且是否已初始化
        if (gantt && gantt._tasks && gantt.destructor) {
          gantt.destructor()
        }
      } catch (error) {
        console.error('销毁gantt实例出错:', error)
      }

      // 取消未执行的防抖函数
      debouncedDeleteTask.cancel()
    })

    onBeforeUnmount(() => {
      // 重置确认框状态标志
      window.isConfirmDialogOpen = false

      // 移除window事件监听器
      window.removeEventListener('resize', updateScrollbarPosition)

      // 移除甘特图渲染处理器
      if (gantt.$_renderHandler) {
        gantt.detachEvent(gantt.$_renderHandler)
      }

      // 清空甘特图数据
      if (gantt && gantt.clearAll) {
        gantt.clearAll()
      }
    })

    //获取项目信息
    const getProjectDate = () => {
      if (tasks.value.data.length > 1) {
        let startTime = tasks.value.data[1].planStartTime
        let endTime = new Date(tasks.value.data[1].planEndTime).getTime()
        const regex = /^\d{10}$|^\d{13}$/
        let endTimeValue = null
        if (regex.test(String(endTime))) {
          endTimeValue = endTime
        } else {
          endTimeValue = new Date(endTime).getTime()
        }
        for (let i = 0; i < tasks.value.data.length; i++) {
          const item = tasks.value.data[i]
          if (item.parent == 0) {
            continue
          } else {
            if (new Date(item.planStartTime).getTime() < new Date(startTime).getTime()) {
              startTime = item.planStartTime
            }
            if (new Date(item.planEndTime).getTime() > endTimeValue) {
              endTime = item.planEndTime
            }
          }
        }
        tasks.value.data.forEach(async (item) => {
          if (item.parent === 0) {
            item.planStartTime = startTime
            if (regex.test(String(endTime))) {
              const date = new Date(endTime)
              const year = date.getFullYear()
              const month = date.getMonth() + 1
              const day = date.getDate()
              const final = year + '-' + month + '-' + day
              item.planEndTime = final
            } else {
              item.planEndTime = endTime
            }
            let ganttUpdateVo = [] //请求参数
            let datas = {}
            datas.id = item.id
            datas.entityCode = 's_task_create'
            datas.objectId = parseInt(route.query.id)
            if (regex.test(String(endTime))) {
              datas.planEndTime = endTime
            } else {
              datas.planEndTime = new Date(endTime).getTime()
            }
            datas.planStartTime = new Date(startTime).getTime()
            datas.chargePersonId = parseInt(item.chargePersonCodes)
            ganttUpdateVo.push(datas)
            datas.value = { ganttUpdateVo }
            // let res = await SaleApi.updateGanttSaleApi(data)
          }
        })
      }
      // gantt.clearAll()
      // gantt.init(this.$refs.gantt)
      gantt.init(ganttContainer.value)
      gantt.parse(tasks.value)
    }
    //初始化事件
    const $_initGanttEvents = () => {
      // 首先检查是否已经初始化过事件，如果是，先清理所有已注册的事件
      if (gantt.$_eventsInitialized) {
        // 移除所有已注册的事件监听器
        if (ganttEventHandlers.value && ganttEventHandlers.value.length > 0) {
          ganttEventHandlers.value.forEach((handlerId) => {
            if (handlerId && gantt.detachEvent) {
              try {
                gantt.detachEvent(handlerId)
              } catch (e) {
                console.error('移除事件监听器失败:', e)
              }
            }
          })
          // 清空事件ID列表
          ganttEventHandlers.value = []
        }
      }

      //自动调度完成后触发
      const autoScheduleHandler = gantt.attachEvent(
        'onAfterAutoSchedule',
        async function (taskId, updatedTasks) {
          let ganttUpdateVo = [] //请求参数
          if (updatedTasks.length > 0) {
            for (let i of updatedTasks) {
              const task = gantt.getTask(taskId) // 获取当前任务
              const parentTask = gantt.getTask(task.parent) // 获取父级任务

              if (parentTask) {
                const taskStart = task.start_date.getTime()
                const taskEnd = task.end_date.getTime()
                const parentStart = parentTask.start_date.getTime()
                const parentEnd = parentTask.end_date.getTime()

                if (taskStart < parentStart || taskEnd > parentEnd) {
                  message.error('任务时间不能超出父任务的时间范围')
                  return false
                }
              }
              let changeTask = gantt.getTask(i)
              let datas = {}
              datas.id = changeTask.id
              datas.planStartTime = changeTask.start_date.getTime()
              datas.planEndTime = changeTask.end_date.getTime()
              datas.entityCode = 's_task_create'
              datas.taskStatus = changeTask.taskStatusCode
              datas.chargePersonId = changeTask.chargePersonCodes
              datas.objectId = changeTask.objectId
              ganttUpdateVo.push(datas)
            }
            let data = { ganttUpdateVo }
            await SaleApi.updateGanttSaleApi(data).then(async (res) => {
              // 使用try-catch保护关键操作
              try {
                await getListData(-1)
                // 确保数据加载成功且gantt实例有效
                if (tasks.value && gantt && gantt.parse) {
                  gantt.parse(tasks.value)
                }
              } catch (error) {
                console.error('更新甘特图后重新加载数据失败:', error)
              }
            })
          }
        }
      )

      // 保存事件ID用于清理
      ganttEventHandlers.value.push(autoScheduleHandler)

      // 其他事件处理程序...

      let allowTaskDrag = true // 全局标志变量
      let allowTaskLink = true // 全局标志变量
      // 任务拖动开始前检查是否超出父任务时间范围
      const beforeTaskDragHandler = gantt.attachEvent('onBeforeTaskDrag', function (id, mode, e) {
        const task = gantt.getTask(id) // 获取当前任务
        const parentTask = gantt.getTask(task.parent) // 获取父级任务

        if (parentTask) {
          const taskStart = task.start_date.getTime()
          const taskEnd = task.end_date.getTime()
          const parentStart = parentTask.start_date.getTime()
          const parentEnd = parentTask.end_date.getTime()

          if (taskStart < parentStart || taskEnd > parentEnd) {
            message.error('任务时间不能超出父任务的时间范围')
            allowTaskDrag = false // 阻止拖动
            return false
          }
        }

        allowTaskDrag = true // 允许拖动
        return true
      })

      // 保存事件ID用于清理
      ganttEventHandlers.value.push(beforeTaskDragHandler)

      // 设置事件已初始化的标志
      gantt.$_eventsInitialized = true
    }
    //初始化数据
    const $_initDataProcessor = () => {
      if (!gantt.$_dataProcessorInitialized) {
        gantt.createDataProcessor((entity, action, data, id) => {
          emit(`${entity}-updated`, id, action, data)
        })
        gantt.$_dataProcessorInitialized = true
      }
    }
    // 新增打开详情弹出框
    const addAttachment = (parentId) => {
      if (!parentId) return

      parentTaskId.value = parentId
      detailTaskId.value = null
      typeAdd.value = 1
      attachmentVisible.value = true
    }
    //保存任务序号
    const saveTaskNo = async () => {
      let ganttUpdateVo = [] //请求参数
      if (tasks.value.data.length > 1) {
        tasks.value.data.forEach((item) => {
          let datas = {}
          datas.id = item.id
          // datas.entityCode = "s_task_create"
          // datas.objectId=parseInt(route.query.id)
          datas.taskNo = item.$wbs
          ganttUpdateVo.push(datas)
        })
        let data = { ganttUpdateVo }
        const ganttData = gantt.serialize()
        let res = await GanttLinkApi.updateTaskNoApi(data)
        if (res) {
          message.success('操作成功')
        }
      }
    }

    const saveDragTaskNo = async () => {
      let ganttUpdateVo = [] //请求参数
      let ganttUpdateVo1 = [] //请求参数
      if (tasks.value.data.length > 1) {
        gantt.eachTask(function (task) {
          ganttUpdateVo.push({
            id: task.id,
            taskNo: task.$wbs
          })
        })
        gantt.eachTask(function (task) {
          ganttUpdateVo1.push({
            id: task.id,
            taskNo: task.$wbs,
            taskName: task.text
          })
        })
        let data = { ganttUpdateVo }
        let res = await GanttLinkApi.updateTaskNoApi(data)
        if (res) {
          // gantt.clearAll();
          await getListData(-1)
          // gantt.init(ganttContainer.value);
          gantt.parse(tasks.value)
          message.success('操作成功')
        }
      }
    }
    //获取部门数据
    const getTree = async () => {
      deptList.value = []
      await OrderApi.listSimpleDeptApi().then((res) => {
        deptList.value = handleTree(res) // 将从后端获取的部门数据赋值给deptList
      })
    }
    // 切换 年 季 月 周 日视图
    const ganttChangeDateView = (type) => {
      switch (type) {
        case 'y':
          gantt.config.scale_unit = 'year'
          gantt.config.step = 1
          gantt.config.subscales = null
          gantt.config.date_scale = '%Y年'
          gantt.templates.date_scale = true
          // gantt.templates.date_scale = null;
          break
        case 'm':
          gantt.config.subscales = null
          gantt.config.scale_unit = 'month'
          gantt.config.step = 1
          gantt.config.date_scale = '%m月'
          gantt.templates.date_scale = true
          // gantt.templates.date_scale = null;
          break
        case 'w':
          gantt.config.scale_unit = 'week'
          gantt.config.step = 1
          gantt.config.date_scale = '第%w周'
          gantt.config.subscales = [{ unit: 'day', step: 1, date: '%m.%d' }]
          break
        case 'd':
          gantt.config.scale_unit = 'year'
          gantt.config.step = 1
          gantt.config.day_width = 10
          gantt.config.date_scale = '%Y年'
          gantt.config.subscales = [{ unit: 'day', step: 1, date: '%m.%d' }]
          break
      }
      gantt.render()
    }
    //日期格式化
    const formatDate = (dateString) => {
      const dateObj = new Date(dateString)
      const year = dateObj.getFullYear()
      const month = String(dateObj.getMonth() + 1).padStart(2, '0')
      const day = String(dateObj.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    }
    //获取任务状态
    const getTaskStatus = (taskStatus) => {
      if (taskStatus == 2) {
        return "<button style='color:#FFC107'>进行中</button>"
        // return "进行中";
      } else if (taskStatus == 3) {
        return "<button style='color:#007BFF'>已完成</button>"
        // return "已完成";
      } else if (taskStatus == 1) {
        return "<button style='color:#ff003b'>待处理</button>"
        // return '待处理';
      } else if (taskStatus == 4) {
        return "<button style='color:#000'>已驳回</button>"
      } else if (taskStatus == 5) {
        return "<button style='color:#e86e30'>审批中</button>"
        // return '已驳回';
      } else if (taskStatus == 6) {
        return "<button style='color:#0000ff'>已中止</button>"
        // return '已中止';
      } else {
        return ' '
      }
    }
    //获取项目状态
    const getProjectStatus = (taskStatus) => {
      if (taskStatus == 2) {
        return "<button style='color:#FFC107'>在制</button>"
        // return "进行中";
      } else if (taskStatus == 3) {
        return "<button style='color:#ff003b'>内部验收</button>"
        // return "已完成";
      } else if (taskStatus == 1) {
        return "<button style='color:#007BFF'>草稿</button>"
        // return '待处理';
      } else if (taskStatus == 4) {
        return "<button style='color:#000'>暂停</button>"
      } else if (taskStatus == 5) {
        return "<button style='color:#e86e30'>客户验收</button>"
        // return '已驳回';
      } else if (taskStatus == 6) {
        return "<button style='color:#0000ff'>客户端调试</button>"
      } else if (taskStatus == 7) {
        return "<button style='color:rgba(124,122,122,0.37)'>关闭</button>"
      } else if (taskStatus == 8) {
        return "<button style='color:#31645f'>终止</button>"
      } else {
        return ' '
      }
    }
    //时间戳转化为日期
    const timestampToTime = (timestamp) => {
      return moment(timestamp).format('YYYY-MM-DD HH:mm:ss')
    }
    //获取任务关系数据
    const getProjectTaskLinkPageApi = async (projectId) => {
      if (!projectId || projectId == -1) {
        projectId = parseInt(route.query.id)
      }
      isCheck.value = 1
      //根据项目id查询前置信息
      let projectTaskLinkParam = {}
      projectTaskLinkParam.objectId = projectId
      if (
        !projectTaskLinkParam.objectId ||
        projectTaskLinkParam.objectId == 'NAN' ||
        projectTaskLinkParam.objectId == undefined
      ) {
        await push({ path: '/home/<USER>' })
        return false
      }
      let link = await GanttLinkApi.getProjectTaskLinkPageApi(projectTaskLinkParam)
      tasks.value.links = []
      if (link) {
        link.forEach((item) => {
          tasks.value.links.push(item)
        })
      }
    }
    // //打开弹出框时初始化甘特图
    // const getListDatas = async (projectId) => {
    //   if (!projectId || projectId == -1) {
    //     projectId = parseInt(route.query.id);
    //   }
    //   isCheck.value = 1
    //   let data = {}
    //   data.entityCode = "s_task_create";
    //   data.objectId = projectId;
    //   if (!data.objectId || data.objectId == 'NAN' || data.objectId == undefined) {
    //     await push({path: "/home/<USER>"});
    //     return false
    //   }
    //   if (formInline.value.taskName) {
    //     data.taskName = formInline.value.taskName;
    //   }
    //   if (formInline.value.workOrderNumber) {
    //     data.workOrderNumber = formInline.value.workOrderNumber;
    //   }
    //   if (formInline.value.taskCode) {
    //     data.taskCode = formInline.value.taskCode;
    //   }
    //   if (formInline.value.deptId) {
    //     data.deptId = formInline.value.deptId;
    //   }
    // }
    //获取gantt数据
    const getListData = async (projectId) => {
      try {
        if (!projectId || projectId == -1) {
          projectId = parseInt(route.query.id)
        }
        isCheck.value = 1
        let data = {}
        data.entityCode = 's_task_create'
        data.objectId = projectId
        if (!data.objectId || data.objectId == 'NAN' || data.objectId == undefined) {
          await push({ path: '/home/<USER>' })
          return false
        }
        if (formInline.value.taskName) {
          data.taskName = formInline.value.taskName
        }
        if (formInline.value.workOrderNumber) {
          data.workOrderNumber = formInline.value.workOrderNumber
        }
        if (formInline.value.taskCode) {
          data.taskCode = formInline.value.taskCode
        }
        if (formInline.value.deptId) {
          data.deptId = formInline.value.deptId
        }
        // if(this.formInline.taskCategory){
        //   data.taskCategory= this.formInline.taskCategory;
        // }

        console.log('开始获取甘特图数据, 项目ID:', data.objectId)
        const fetchData = async (retryCount = 0) => {
          try {
            let res = await GanttApi.getProjectGanttPageApi(data)
            if (!res && retryCount < 2) {
              console.warn(`获取甘特图数据为空，第${retryCount + 1}次重试...`)
              await new Promise((resolve) => setTimeout(resolve, 1000)) // 等待1秒后重试
              return fetchData(retryCount + 1)
            }
            return res
          } catch (error) {
            console.error('获取甘特图数据失败:', error)
            if (retryCount < 2) {
              console.warn(`获取甘特图数据出错，第${retryCount + 1}次重试...`)
              await new Promise((resolve) => setTimeout(resolve, 1000)) // 等待1秒后重试
              return fetchData(retryCount + 1)
            }
            message.error('获取任务数据失败，请刷新页面重试')
            return null
          }
        }

        const res = await fetchData()
        if (res) {
          await configData(res, data.objectId)
          console.log('甘特图数据加载成功，共有任务:', res.length)
          return true
        } else {
          console.error('获取甘特图数据失败或为空')
          return false
        }
      } catch (error) {
        console.error('getListData函数执行出错:', error)
        message.error('获取任务数据失败，请刷新页面重试')
        return false
      }
    }
    //条件查询
    const submitForm = async (formName) => {
      tasks.value.data = []
      await getListData(-1)
      await getProjectTaskLinkPageApi(-1)
      gantt.parse(tasks.value)
    }
    //搜索重置
    const resetForm = async (formName) => {
      formInline.value.taskName = ''
      formInline.value.taskCode = ''
      formInline.value.deptId = ''
      // this.formInline.taskCategory=null
      await getListData(-1)
      await getProjectTaskLinkPageApi(-1)
    }
    //导出数据
    const export_data = async () => {
      const res = await GanttLinkApi.exportApi(route.query.id)
      download.excel(res, '项目任务分配.xls')
    }
    //导入员工工时
    const import_data = () => {
      // const gantt = window.gantt
      const taskId = gantt.getLastSelectedTask()
      const selectedTask = gantt.getTask(taskId)
      if (!taskId) {
        return message.error('请选择一个任务')
      }
      const deptId = selectedTask.deptId
      personFormVisible.value = true
      detailTaskId.value = taskId
      detailDeptId.value = deptId
    }
    //跳转到对应的定额工时表
    const to_quotation = async () => {
      try {
        const res = await RegisterApi.getSalesProjectsApi(route.query.id, 1)
        projectStatus.value = res.projectStatus
        projectNature.value = res.projectNature
        if (projectStatus.value === 1) {
          message.error('项目处于草稿状态，无法跳转到定额工时表')
          return // 如果项目状态是草稿，直接返回
        }
        const query = {
          id: route.query.id,
          marketStatus: 0,
          marketType: projectNature.value,
          projectStatus: projectStatus.value
        }
        await router.push({ path: '/cost/projectCost/estimateDetail', query: query })
      } catch (error) {
        console.error('Error fetching sales projects:', error)
        message.error('获取项目失败')
      }
    }

    const getDetailSonValue = async (value) => {
      if (value.status === 'delete') {
        //删除，关闭弹出框，刷新
        attachmentVisible.value = false
        gantt.clearAll()
        await getListData(-1)
        gantt.parse(tasks.value)
        await saveTaskNo() // 调用修改序号的方法
      } else if (value.status === 'update') {
        //修改，关闭弹框，刷新
        attachmentVisible.value = false
        await getListData(-1)
        // await getProjectTaskLinkPageApi(-1)
        // await saveDragTaskNo()
        gantt.parse(tasks.value)
        // await this.getProjectDate()
        await saveTaskNo() // 调用修改序号的方法
      } else if (value.status === 'create') {
        //刷新
        attachmentVisible.value = false
        await getListData(-1)
        // await getProjectTaskLinkPageApi(-1)
        // await saveDragTaskNo()
        gantt.parse(tasks.value)
        // await this.getProjectDate()
        await saveTaskNo() // 调用修改序号的方法
      }
    }
    const selectId = ref(null)
    // 修改打开详情弹出框
    const openAttachment = (id) => {
      try {
        // 确保id有效 - 放宽检查条件，不要阻止有效的数字ID
        if (id === undefined || id === null || id === '') {
          console.error('完全无效的任务ID:', id)
          message.error('无法打开任务，无效的任务ID')
          return
        }

        // 即使ID看起来不是预期格式，也尝试打开编辑页面
        console.log('尝试打开任务ID:', id)
        detailTaskId.value = id
        parentTaskId.value = null
        sessionStorage.setItem('taskId', id)
        typeAdd.value = null
        attachmentVisible.value = true
      } catch (error) {
        console.error('打开任务详情失败:', error)
        message.error('打开任务详情失败，请重试')
      }
    }
    const getOrgName = (orgId) => {
      for (let item of userOption.value) {
        if (item.value == orgId) {
          return item.label
        }
      }
    }
    const configData = (res, objectId) => {
      //封装数据
      try {
        // 重置任务数据
        tasks.value.data = []

        // 检查res是否存在且有数据
        if (!res || !Array.isArray(res) || res.length === 0) {
          console.warn('甘特图数据为空或格式不正确:', res)
          // 创建一个空的项目条目，确保甘特图能够初始化
          tasks.value.data.push({
            id: objectId,
            text: '项目',
            parent: 0,
            progress: 0,
            open: true,
            type: 'task',
            planStartTime: new Date().getTime(),
            planEndTime: new Date().getTime() + 24 * 60 * 60 * 1000,
            objectId: objectId
          })
          return
        }

        let projectPlanStartTime = null //项目的计划开始时间
        let projectPlanEndTime = null //项目的计结束时间

        // 确保排序过程不会导致错误
        try {
          function compareVersions(version1, version2) {
            if (!version1 || !version2) return 0

            try {
              const v1Parts = String(version1).split('.')
              const v2Parts = String(version2).split('.')
              const maxLength = Math.max(v1Parts.length, v2Parts.length)
              for (let i = 0; i < maxLength; i++) {
                const part1 = parseInt(v1Parts[i] || 0, 10)
                const part2 = parseInt(v2Parts[i] || 0, 10)

                if (part1 < part2) {
                  return -1
                } else if (part1 > part2) {
                  return 1
                }
              }
              return 0 // 两个版本号相等
            } catch (error) {
              console.error('比较版本号出错:', error)
              return 0
            }
          }

          function taskObjectFuc(item) {
            if (item.parentTaskId == 0) {
              projectPlanStartTime = item.planStartTime
              projectPlanEndTime = item.planEndTime
            }
            let taskObject = {
              objectId: objectId,
              id: item.id,
              text: item.taskName,
              parentTaskName: item.parentTaskName,
              taskCode: item.taskCode,
              // duration:(item.planStartTime-item.planEndTime)/60,
              // duration: item.duration ? item.duration: null,
              // start_date: moment(item.planStartTime).format('YYYY-MM-DD hh-mm-ss')||null,
              start_date: item.planStartTime
                ? moment(item.planStartTime).format('YYYY-MM-DD HH-mm-ss') || null
                : null,
              end_date: item.planEndTime
                ? moment(item.planEndTime).format('YYYY-MM-DD HH-mm-ss') || null
                : null,
              orgName: item.orgName,
              role: item.roleId,
              importantPath: item.importantPath,
              flagBit: item.flagBit,
              progress: item.progress ? item.progress : 0,
              taskStatus:
                item.parentTaskId == 0
                  ? getProjectStatus(item.taskStatus)
                  : getTaskStatus(item.taskStatus),
              taskStatusCode: item.taskStatus,
              taskDesc: item.taskDesc,
              chargePersonName: item.username,
              creator: item.creatorName,
              createTime: item.createTime,
              postTaskEndDays: item.postTaskEndDays,
              postTaskStartDays: item.postTaskStartDays,
              chargePersonId: getOrgName(item.chargePersonId),
              chargePersonIds: getOrgName(item.chargePersonId),
              chargePersonCodes: item.chargePersonId,
              roleName: item.roleName,
              taskNo2: item.taskNo2,
              deptName: item.deptName,
              deptId: item.deptId,
              taskType: item.taskCategory && item.taskCategory == 2 ? '是' : '否',
              isThreeReview: item.isThreeReview,
              isTwoReview: item.isTwoReview,
              isProjectMeet: item.isProjectMeet,
              priority: item.priority,
              scheme: item.scheme,
              rollup: item.parentTaskId == 0 ? false : true,
              rollup_baseline: true,
              open: item.parentTaskId == 0 ? true : true,
              planStartTime: timestampToTime(item.planStartTime),
              planEndTime: timestampToTime(item.planEndTime),
              projectPlanStartTime: timestampToTime(projectPlanStartTime),
              projectPlanEndTime: timestampToTime(projectPlanEndTime),
              actualStartTime: item.actualStartTime ? timestampToTime(item.actualStartTime) : '',
              actualEndTime: item.actualEndTime ? timestampToTime(item.actualEndTime) : '',
              parent: item.parentTaskId == 0 ? 0 : item.parentTaskId,
              // type: item.parentTaskId == 0 ? "project" : item.taskCategory && item.taskCategory == 2 ? "milestone" : "task",
              type:
                item.parentTaskId == 0
                  ? 'task'
                  : item.taskCategory && item.taskCategory == 2
                  ? 'milestone'
                  : 'task',
              projectName: item.projectName,
              projectCode: item.projectCode,
              attachment: '附件'
            }
            return taskObject
          }
          // 使用try-catch保护排序过程
          try {
            res.sort(function (a, b) {
              // 如果 taskNo 存在，或者 taskNo 为空字符串，优先按 taskNo 升序排序
              if (a.taskNo !== null && a.taskNo !== '') {
                if (b.taskNo !== null && b.taskNo !== '') {
                  return compareVersions(a.taskNo, b.taskNo)
                } else {
                  return -1 // a 有值，b 为 null，a 应该排在前面
                }
              }
              // 如果 taskNo 为 null，按 taskNo2 升序排序
              return compareVersions((a.taskNo2 || '').toString(), (b.taskNo2 || '').toString())
            })
            res.forEach((item, index) => {
              let taskObjectData = taskObjectFuc(item)
              tasks.value.data.push(taskObjectData)
            })
          } catch (error) {
            console.error('排序过程中出错:', error)
            // 如果排序失败，尝试使用原始排序逻辑
            res.sort(function (a, b) {
              // 如果 taskNo 存在，或者 taskNo 为空字符串，优先按 taskNo 升序排序
              if (a.taskNo !== null && a.taskNo !== '') {
                if (b.taskNo !== null && b.taskNo !== '') {
                  return compareVersions(a.taskNo, b.taskNo)
                } else {
                  return -1 // a 有值，b 为 null，a 应该排在前面
                }
              }
              // 如果 taskNo 为 null，按 taskNo2 升序排序
              return compareVersions((a.taskNo2 || '').toString(), (b.taskNo2 || '').toString())
            })

            res.forEach((item, index) => {
              let taskObjectData = taskObjectFuc(item)
              tasks.value.data.push(taskObjectData)
            })
          }
        } catch (error) {
          console.error('排序过程中出错:', error)
          // 如果排序失败，尝试使用原始排序逻辑
          res.sort(function (a, b) {
            // 如果 taskNo 存在，或者 taskNo 为空字符串，优先按 taskNo 升序排序
            if (a.taskNo !== null && a.taskNo !== '') {
              if (b.taskNo !== null && b.taskNo !== '') {
                return compareVersions(a.taskNo, b.taskNo)
              } else {
                return -1 // a 有值，b 为 null，a 应该排在前面
              }
            }
            // 如果 taskNo 为 null，按 taskNo2 升序排序
            return compareVersions((a.taskNo2 || '').toString(), (b.taskNo2 || '').toString())
          })
          res.forEach((item, index) => {
            let taskObjectData = taskObjectFuc(item)
            tasks.value.data.push(taskObjectData)
          })
        }
      } catch (error) {
        console.error('封装数据过程中出错:', error)
        // 如果封装数据失败，尝试使用原始数据
        tasks.value.data = res
        tasks.value.links = []
      }
    }

    const toGantt = () => {
      showTodayButton.value = true // 显示“今天”按钮
      // 修改布局配置，仅显示图表视图
      gantt.config.layout = {
        css: 'gantt_container',
        cols: [
          {
            width: 420,
            min_width: 300,
            rows: [
              { view: 'grid', scrollX: 'gridScroll', scrollable: true, scrollY: 'scrollVer' },
              { view: 'scrollbar', id: 'gridScroll', group: 'horizontal' }
            ]
          },
          { resizer: true, width: 1 },
          {
            rows: [
              { view: 'timeline', scrollX: 'scrollHor', scrollY: 'scrollVer' },
              { view: 'scrollbar', id: 'scrollHor', group: 'horizontal' }
            ]
          },
          { view: 'scrollbar', id: 'scrollVer' }
        ]
      }
      console.log('toGantt')
      // 重新初始化 Gantt
      // gantt.init(this.$refs.gantt);
      gantt.init(ganttContainer.value)
    }
    const toLeft = () => {
      showTodayButton.value = true // 显示“今天”按钮
      // 修改布局配置，仅显示图表视图
      gantt.config.layout = {
        css: 'gantt_container',
        rows: [
          {
            cols: [
              { view: 'timeline', scrollX: 'scrollHor', scrollY: 'scrollVer' },
              { view: 'scrollbar', id: 'scrollVer' }
            ]
          },
          { view: 'scrollbar', id: 'scrollHor' },
          { view: 'scrollbar', id: 'scrollVer' },
          { view: 'scrollbar', id: 'scrollHor' } // 添加横向滚动条视图
        ]
      }
      console.log('toLeft')
      // 重新初始化 Gantt
      // gantt.init(this.$refs.gantt);
      gantt.init(ganttContainer.value)
    }
    const toRight = () => {
      showTodayButton.value = false // 隐藏“今天”按钮
      // 修改布局配置，仅显示列表视图
      gantt.config.layout = {
        css: 'gantt_container',
        cols: [
          {
            rows: [
              {
                cols: [
                  {
                    view: 'grid',
                    scrollX: 'gridScroll',
                    scrollable: true,
                    scrollY: 'scrollVer',
                    width: '100%'
                  }
                ]
              },
              { view: 'scrollbar', id: 'gridScroll', group: 'horizontal' },
              { view: 'scrollbar', id: 'scrollHor' }
            ]
          },
          {
            view: 'scrollbar',
            id: 'scrollVer',
            group: 'vertical'
          }
        ]
      }

      // 重新初始化 Gantt
      // gantt.init(this.$refs.gantt);
      console.log('toRight')
      gantt.init(ganttContainer.value)
    }
    // 打开产品列表操作
    const citingOtherProjects = () => {
      // const gantt = window.gantt
      const taskId = gantt.getLastSelectedTask()
      if (!taskId) {
        return message.error('请选择一个任务')
      }
      linkTaskId.value = taskId
      projectVisible.value = true
      timer.value = new Date().getTime()
      sessionStorage.setItem('searchStatus', '0')
      // let search = '?ppvVersionStatus=1'
      // window.history.replaceState('', '',  window.location.origin + window.location.pathname  + search)
    }
    // 打开模板列表操作
    const citingTemplateProjects = () => {
      actionLoading.value = true
      isCheck.value = 1
      // const gantt = window.gantt
      const taskId = gantt.getLastSelectedTask()
      if (!taskId) {
        return message.error('请选择一个任务')
      }
      linkTaskId.value = taskId
      templateVisible.value = true
      timer.value = new Date().getTime()
      sessionStorage.setItem('searchStatus', '0')
      // let search = '?ppvVersionStatus=1'
      // window.history.replaceState('', '',  window.location.origin + window.location.pathname  + search)
    }
    //初始化甘特图数据
    // const $_initDataProcessor= ()=> {
    //      gantt.createDataProcessor((entity, action, data, id) => {
    //        emit(`${entity}-updated`, id, action, data);
    //      });
    //      gantt.$_dataProcessorInitialized = true;
    //  }
    //控制搜索框展开收起
    const changeSearchVisible = () => {
      searchVisible.value = !searchVisible.value
    }
    const getClose = async (value) => {
      if (value || value.status) {
        if (value.status == true) {
          await getListData(-1)
          gantt.parse(tasks.value)
        }
        personFormVisible.value = false
      }
    }
    //子组件选中产品订单后传值
    const getSonValue = async (value) => {
      isCheck.value = 1
      if (value.id == route.query.id) {
        message.error('请勿引用自身项目')
      } else {
        let data = {}
        data.sourceId = value.id
        data.type = 1
        data.objectId = parseInt(route.query.id)
        if (route.query.id) {
          data.objectId = route.query.id
        }
        data.targetId = parseInt(linkTaskId.value)
        templateStatus.value = true
        await RoleApi.citingOtherProjectsApi(data)
          .then((res) => {
            gantt.clearAll()
            getListData(-1)
            gantt.init(ganttContainer.value)
            gantt.parse(tasks.value)
            templateStatus.value = false
            templateVisible.value = false
          })
          .catch((err) => {
            templateStatus.value = false
          })
        projectVisible.value = false
        // templateVisible.value = false

        await getListData(-1)
        await getProjectTaskLinkPageApi(-1)
        await saveTaskNo()
        gantt.init(ganttContainer.value)
        gantt.parse(tasks.value)
      }
    }

    const handleDeleteTask = async (taskId) => {
      try {
        // 添加标志变量，防止重复显示确认框
        if (window.isConfirmDialogOpen) {
          console.log('已有确认框打开，忽略此次请求')
          return
        }

        // 设置标志，表示确认框已打开
        window.isConfirmDialogOpen = true

        await SaleApi.deleteSaleApi(taskId, 2, false, false)
          .then(async (res) => {
            ElMessage.success('删除成功')
            // 删除成功后刷新数据并保存序号
            gantt.clearAll()
            await getListData(-1)
            await saveTaskNo()
            gantt.parse(tasks.value)
          })
          .catch((err) => {
            // console.log(err,'err')
            // 弹框提示异常信息
            let tips = ''
            if (err.code === 21310001) {
              return message.error('只有未开始的任务才能进行删除操作')
            } else if (err.code == 21310002) {
              return message.error('"最顶级父任务不允许删除"')
            } else if (err.code == 21310003) {
              tips =
                '该任务中存在进行中或已完成的子任务，如果继续删除的话，该任务下的所有任务将一并被删除，请谨慎操作。确认要删除吗?'
            } else if (err.code == 21310004) {
              tips = '确定要删除该任务(以及该任务下的所有子任务)吗?'
            } else if (err.code == 21410003) {
              message.error(err.msg)
            } else {
              tips = '确定要删除该任务吗'
            }
            ElMessageBox.confirm(tips, '提示', {
              confirmButtonText: '确认删除',
              cancelButtonText: '不删除',
              type: 'warning',
              // 无论确认还是取消，都需要重置标志
              beforeClose: (action, instance, done) => {
                // 重置标志
                window.isConfirmDialogOpen = false
                done() // 继续关闭对话框
              }
            })
              .then(async () => {
                // 用户点击确认后，重新发送删除请求并带上confirm:true参数
                await SaleApi.deleteSaleApi(taskId, 2, true, false)
                ElMessage.success('删除成功')
                gantt.clearAll()
                await getListData(-1)
                gantt.parse(tasks.value)
                await saveTaskNo() // 调用修改序号的方法
              })
              .catch(() => {
                // 用户点击取消，不做任何操作
                window.isConfirmDialogOpen = false
              })
          })
      } finally {
      }
    }

    // 使用防抖包装删除方法
    const debouncedDeleteTask = debounce(async (taskId) => {
      try {
        await SaleApi.deleteSaleApi(taskId, 2, false, false)
        message.success('删除成功')
        emit('getValue', { status: 'delete', id: taskId })
      } catch (err) {
        console.log(err, 'error.msg ')
        let tips = ''
        if (err === 21310001) {
          tips = '确定要删除该任务(以及该任务下的所有子任务)吗?'
        } else if (err == 21310002) {
          return message.error('"最顶级父任务不允许删除"')
        } else if (err == 21310003) {
          tips =
            '该任务中存在进行中或已完成的子任务，如果继续删除的话，该任务下的所有任务将一并被删除，请谨慎操作。确认要删除吗?'
        } else if (err == 21310004) {
          return message.error('只有未开始的任务才能进行删除操作')
        } else {
          tips = '确定要删除该任务吗'
        }

        ElMessageBox.confirm(tips, '提示', {
          confirmButtonText: '确认删除',
          cancelButtonText: '不删除',
          type: 'warning'
        })
          .then(async () => {
            await SaleApi.deleteSaleApi(taskId, 2, true, false)
            message.success('删除成功')
            emit('getValue', { status: 'delete', id: taskId })
          })
          .catch(() => {
            // 用户取消删除，不做任何操作
          })
      }
    }, 300) // 300ms 的防抖时间

    // 在组件卸载时取消未执行的防抖函数
    onUnmounted(() => {
      debouncedDeleteTask.cancel()
    })

    // 修改甘特图配置中的删除事件处理
    const ganttConfig = {
      // ... 其他配置 ...
      taskDelete: (taskId) => {
        debouncedDeleteTask(taskId)
      }
    }
    // 更新滚动条位置
    const updateScrollbarPosition = () => {
      const scrollbar = document.querySelector('.gantt_hor_scroll')
      if (scrollbar) {
        scrollbar.style.width = window.innerWidth + 'px'
      }
    }

    return {
      ganttContainer,
      message,
      defaultProps,
      props,
      datas,
      isActive,
      isOff,
      isCheck,
      templateStatus,
      projectId,
      personFormVisible,
      projectVisible,
      attachmentVisible,
      searchVisible,
      templateVisible,
      formInline,
      form,
      loadingbut,
      taskStatusCode,
      loadingbuttext,
      endTimeVisible,
      deptList,
      isMilestoneList,
      taskStatusOption,
      attachments,
      task,
      startTime,
      endTime,
      userOption,
      task_category,
      projectCode,
      projectPhase,
      projectName,
      orgName,
      taskID,
      deptOption,
      taskStatus,
      attachment,
      frontTaskOption,
      taskId,
      tasks,
      linkTaskId,
      detailTaskId,
      detailDeptId,
      parentTaskId,
      typeAdd,
      addAttachment,
      getProjectDate,
      saveTaskNo,
      ganttChangeDateView,
      getTree,
      formatDate,
      getTaskStatus,
      timestampToTime,
      getProjectStatus,
      getProjectTaskLinkPageApi,
      getListData,
      submitForm,
      configData,
      resetForm,
      getOrgName,
      export_data,
      import_data,
      to_quotation,
      getDetailSonValue,
      openAttachment,
      toGantt,
      toLeft,
      toRight,
      citingTemplateProjects,
      citingOtherProjects,
      getSonValue,
      getClose,
      changeSearchVisible,
      $_initDataProcessor,
      $_initGanttEvents,
      saveDragTaskNo,
      handleDeleteTask,
      // scrollToToday,
      // positionTodayInView,
      updateScrollbarPosition
      // showTodayButton
    }
  }
}
</script>

<style scoped>
:deep(.gantt_grid_head_add) {
  /*//display: none; */
  /*opacity: 0;*/
  visibility: hidden;
}

:deep(.overdue-indicator) {
  width: 17px;
  margin-top: 5px;
  height: 17px;
  box-sizing: border-box;
  border-radius: 15px;
  color: white;
  background: rgb(255, 60, 60);
  line-height: 18px;
  text-align: center;
  font-size: 16px;
}

:deep(.dhx_calendar_cont input) {
  width: 96px;
  padding: 0;
  margin: 3px 10px 10px 10px;
  font-size: 11px;
  height: 17px;
  text-align: center;
  border: 1px solid #ccc;
  color: #646464;
}

:deep(.dhtmlxcalendar_dhx_skyblue),
:deep(.dhtmlxcalendar_dhx_web),
:deep(.dhtmlxcalendar_dhx_terrace) {
  z-index: 999999 !important;
}

:deep(.gantt_cal_light .gantt_btn_set.gantt_delete_btn_set) {
  font-size: 20px !important;
}

:deep(.deadline) {
  position: absolute;
  border-radius: 12px;
  border: 2px solid #585858;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  width: 22px;
  height: 22px;
  margin-left: -11px;
  margin-top: 6px;
  z-index: 1;
  background: url('@/assets/imgs/yuqi.png') center no-repeat;
}

:deep(.gantt_cal_light) {
  display: none !important;
}

:deep(.child_preview) {
  box-sizing: border-box;
  margin-top: 2px;
  position: absolute;
  z-index: 1;
  color: white;
  text-align: center;
  font-size: 12px;
}

:deep(.gantt_task_line.task-collapsed) {
  height: 4px;
  opacity: 0.25;
}

:deep(.weekend) {
  background: #f4f7f4;
}

:deep(.gantt_selected .weekend) {
  background: #f7eb91;
}

:deep(.gantt_task_line.gantt_project.task-collapsed .gantt_task_content) {
  background-color: #65c16f !important;
  border: 1px solid #3c9445 !important;
  display: none;
}

:deep(.gantt_row.task-parent) {
  font-weight: bold;
}

:deep(.no_progress .gantt_task_progress_wrapper),
:deep(.no_progress .gantt_task_progress_drag) {
  display: none !important;
}

:deep(.gantt_task_progress) {
  display: none !important;
}

:deep(.gantt_task_line.gantt_milestone .gantt_side_content.gantt_right) {
  font-size: 12px;
}

/* 固定表头的样式 */
:deep(.gantt_grid_head) {
  position: fixed;
  top: 0;
  z-index: 2; /* 保证表头在最上层 */
  background-color: #fff; /* 可根据需要设置背景颜色 */
}

:deep(.project-task-readonly) {
  pointer-events: none !important;
}

:deep(.gantt_task_line.project-task-readonly) {
  cursor: default !important;
}

:deep(.gantt_grid_data .project-task-readonly) {
  pointer-events: none;
}

/* 添加回按钮相关样式 */
.gantt-button {
  padding: 8px;
  background-color: #007bff;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  margin-right: 10px;
  margin-bottom: 1px;
}

.gantt-button:hover {
  background-color: #0056b3;
}

.gantt-button:focus {
  outline: none;
}

.gantt-button[disabled] {
  background-color: #cccccc;
  cursor: not-allowed;
}

/* 其他必要的样式 */
div.gantt_cal_ltext {
  padding: 0 !important;
}

.gantt_task_line.gantt_dependent_task {
  background-color: #65c16f;
  border: 1px solid #3c9445;
}

.gantt_task_line.gantt_dependent_task .gantt_task_progress {
  background-color: #46ad51;
}

.gantt_cal_light .gantt-lb-datepicker input {
  border: 1px solid #ccc !important;
  margin: 10px 0 !important;
}

:deep(.el-button--danger) {
  cursor: pointer;
}

:deep(.el-button--danger:hover) {
  cursor: pointer;
}

/* 今天标记样式 */
.today {
  position: absolute !important;
  background-color: #ff0000 !important;
  width: 2px !important;
  height: 100% !important;
  z-index: 1000 !important; /* 提高z-index确保在最顶层 */
  opacity: 1 !important;
  border-left: 2px solid #f00 !important;
  pointer-events: none !important; /* 防止干扰鼠标事件 */
}

/* 确保甘特图内容区域正确显示 */
:deep(.gantt_bars_area) {
  overflow: auto !important;
}

:deep(.gantt_grid_data) {
  overflow-y: auto !important;
}

/* 设置甘特图头部文字颜色为黑色 */
:deep(.gantt_grid_head_cell) {
  color: #000000 !important;
  font-weight: bold !important;
}

:deep(.gantt_scale_cell:not(.weekend)) {
  color: #000000 !important;
  font-weight: bold !important;
}

:deep(.gantt_scale_cell:not(.weekend) .gantt_scale_cell_text) {
  color: #000000 !important;
}
</style>
