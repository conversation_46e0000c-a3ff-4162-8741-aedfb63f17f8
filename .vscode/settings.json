{"typescript.tsdk": "node_modules/typescript/lib", "prettier.enable": true, "editor.formatOnType": true, "editor.formatOnSave": true, "editor.formatOnPaste": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[css]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[scss]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "i18n-ally.localesPaths": ["src/locales"], "i18n-ally.keystyle": "nested", "i18n-ally.sortKeys": true, "i18n-ally.namespace": true, "i18n-ally.enabledParsers": ["ts"], "i18n-ally.sourceLanguage": "en", "i18n-ally.displayLanguage": "zh-CN", "i18n-ally.enabledFrameworks": ["vue", "react"], "god.tsconfig": "./tsconfig.json", "vue-i18n.i18nPaths": "src/locales"}