/*non-production styles*/
.webixdoc_error{
	color:red !important; font-weight:bold !important;
}

/*layout*/
body{
	color:#222;
	font-size: 16px;
	font-family: "Roboto", Arial, sans-serif;

	background-color: #f1f5f6;

	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
	-webkit-tap-highlight-color: transparent;
	-webkit-text-size-adjust: none;
	text-rendering: optimizeLegibility;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.main_header{
	background-color:#f0f0f0; color:#444;
}

.content_area{
	width:100%;
	margin: 0 auto;
}

/*Main page area*/
.webixdoc_page{
	background:#ffffff;
	width:1000px;
	display:table;
	margin-left:auto;
	margin-right:auto;
	clear:both;
}

/*text part*/
.webixdoc_content{
	vertical-align:top;
	overflow:hidden;
	padding: 0px 15px 5px 15px;
	border-left:1px solid #dfdfdf;
	border-right:1px solid #dfdfdf;
}
.webixdoc_content_inner{
	width:686px;
	min-height:600px;
	overflow:hidden;
}


.webixdoc_content a{
	color:#0F8192;
	text-decoration:none;
}
.webixdoc_content_alt{
	background-color: #f2efea;
	border-top:1px solid #ece8e2;
	border-bottom:1px solid #ece8e2;
	padding-top:10px;
	padding-bottom:10px;
}
.webixdoc_backtop{
	margin:40px 40px 0px 0px;
	display:block;
	text-align:right;
}
/*search box*/
.webixdoc_search{
	margin:15px;

	width:248px;
	height:29px;

	border:1px solid #cccccc;
	border-radius:2px;
}

.webixdoc_search input {
	border:none;
	outline:none;

	height:23px;
	font-size:14px;

	margin:0px;
	padding-left: 8px;
	padding-top:2px;
	width:200px;
}
.webixdoc_search button {
	border:none;
	float:right;
	cursor:pointer;

	background:#ffffff;
	padding:0;
	margin: 7px 8px 0px 0px;
}

.webixdoc_search .button {
	background-image: url(data:image/png;base64,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);
	width: 15px;
	height: 15px;
}

/*Separate styles for front page*/
.webixdoc_start  .webixdoc_search{
	width:930px;
}
.webixdoc_start .webixdoc_search input {
	width:867px;
}
.webixdoc_start > .webixdoc_content{
	display: block;
}
.webixdoc_start .webixdoc_content_inner{
	min-height:0px;
}
.webixdoc_content .webixdoc_content{
	border-width:0px;
}
.bottom_block li{
	display: block;
	width:215px; float:left;
	padding:10px 23px 0px 0px;
}
.bottom_block li a{
	color:#444;
}
.bottom_block h4{
	text-align: center;
	font-size:20px;
	font-weight: normal;
}
.bottom_block p{
	text-align: center;
}
.bottom_icon{
	background:url("index/front2.png") no-repeat;
	width:150px;
	height:100px;
	margin: 15px 25px;
}
#hints .bottom_icon{
	background-position: 10px 0px;
}
#guides .bottom_icon{
	background-position: -190px 0px;
}
#samples .bottom_icon{
	background-position: -380px 0px;
}
#api .bottom_icon{
	background-position: -575px 0px;
}


/*breadcrumb*/
.webixdoc_breadcrumb {
	overflow: hidden;
	margin-top:15px;
}

.webixdoc_content .webixdoc_breadcrumb a {
	text-decoration:none;

	color: #fff;
	text-decoration: none;
	padding: 7px 0 7px 45px;
	background: #3ea9d9;

	position: relative;
	display: block;
	float: left;
}

.webixdoc_breadcrumb a:after {
	content: " ";
	display: block;
	width: 0; height: 0;
	border-top: 35px solid transparent;
	border-bottom: 35px solid transparent;
	border-left: 30px solid #3ea9d9;
	position: absolute;

	top: 50%; left: 100%;
	z-index: 2;
	margin-top: -35px;
}


.webixdoc_breadcrumb a:first-child{
	padding-left: 25px;
}

.webixdoc_breadcrumb a:nth-child(2)       { background:#6ebfe3; }
.webixdoc_breadcrumb a:nth-child(2):after { border-left-color:#6ebfe3; }
.webixdoc_breadcrumb a:nth-child(3)       { background:#93cfea; }
.webixdoc_breadcrumb a:nth-child(3):after { border-left-color:#93cfea; }
.webixdoc_breadcrumb a:nth-child(4)       { background:#B4DBED;}
.webixdoc_breadcrumb a:nth-child(4):after { border-left-color:#B4DBED;}
.webixdoc_breadcrumb a:nth-child(5)       { background:#E1D4E6; }
.webixdoc_breadcrumb a:nth-child(5):after { border-left-color:#E1D4E6;}

.webixdoc_breadcrumb a:hover { background:#4D7DBF; }
.webixdoc_breadcrumb a:hover:after { border-left-color:#4D7DBF !important; }



/*navigation*/
.webixdoc_navarea{
	display: table-cell;
	vertical-align:top;
	width: 280px;

	border-left:1px solid #B6D0DE;
	background:#ffffff;
}
.webixdoc_navarea_inner{
	width: 280px;
}
.webixdoc_nav{
	padding-left:6px;
	padding-right:8px;
}
.webixdoc_nav li a{
	padding:5px 10px 5px 0px;
	color:#666;
	text-decoration:none;
}

.webixdoc_nav li {
	line-height:16px;
	padding:4px 0px;
	margin:0px;
}
.webixdoc_nav ul li{
	margin-left:-38px;
	padding-right:5px;

	list-style:none;
	background:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAsAAAALCAIAAAAmzuBxAAAABnRSTlMA/wD/AP83WBt9AAAACXBIWXMAAAsTAAALEwEAmpwYAAAApElEQVR4nK2QMQqEMBBFx20EC9NZiJAjeBMLu+RqQpoEbyGBJCBpQs4RsIkgWGWLDbuyhWyxv5z3YPi/SCnBbR73+Gdjmiat9RdQSjHGslHXtdbaWvvG67oaYxBC2RjHseu6ZVm89wDgnJNSYoyHYQCA4tXlPE8hRAih73vvfdu2hJCyLD8GABzHwTnftq1pGkppVVX5ZbokxjjP877v12Pxh8We28BeQ2OWgBsAAAAASUVORK5CYII=) no-repeat 3px center;
	padding-left: 20px;
}

.webixdoc_nav ul ul li{
	list-style: circle;
	background:none;
	padding-left: 0px;
}
.webixdoc_nav ul ul li.sub{
	list-style:disc;
	background:none;
}


.webixdoc_nav ul li.webix_current{
	background: url('common/list2.png') no-repeat 3px center;
	background-color:#3ea9d9;
	border-radius:2px;
}

.webixdoc_nav ul li.webix_current a{
	color:#fff;
}

/*headers*/
h1{
	font-weight:normal;
	text-align:center;
	font-size:35px;
	margin-top:30px;
	margin-bottom: 20px;
}
h2{
	color:#222222;
	line-height: 2em;
	margin-top:25px;
	font-size:1.5em;
	font-weight:normal;
}

h4{
	font-size:14px;
	margin-bottom: 0;
}

p{
	line-height: 1.8em;
	margin: 0.5em 0;
}

pre {
	padding:2px 0;
	font-size:15px;
	font-family:Consolas, monospace;

	background:#F2F4F5;
	border:1px solid #D8E4E8;
	margin-top:0px;
	overflow-x: hidden;
	line-height: 1.5em;
}
pre pre {
	border:none;
	margin-left:10px;
	margin-bottom:0px;
}

li{
	line-height:20px;
	margin-bottom: 6px;
	margin-top: 6px;
}

dt{
	padding-left:10px;
}

/**
 * GeSHi (C) 2004 - 2007 Nigel McNie, 2007 - 2008 Benny Baumann
 * (http://qbnz.com/highlighter/ and http://geshi.org/)
 */

.css .de1, .css .de2 {margin:0; padding:0; background:none; vertical-align:top;}
.css .imp {font-weight: bold; color: red;}
.css li, .css .li1 {font-weight: normal; vertical-align:top;}
.css .ln {width:1px;text-align:right;margin:0;padding:0 2px;vertical-align:top;}
.css .li2 {font-weight: bold; vertical-align:top;}
.css .kw1 {color: #000000; font-weight: bold;}
.css .kw2 {color: #993333;}
.css .co1 {color: #a1a100;}
.css .co2 {color: #ff0000; font-style: italic;}
.css .coMULTI {color: #808080; font-style: italic;}
.css .es0 {color: #000099; font-weight: bold;}
.css .es2 {color: #000099; font-weight: bold;}
.css .br0 {color: #00AA00;}
.css .sy0 {color: #00AA00;}
.css .st0 {color: #ff0000;}
.css .nu0 {color: #cc66cc;}
.css .re0 {color: #cc00cc;}
.css .re1 {color: #6666ff;}
.css .re2 {color: #3333ff;}
.css .re3 {color: #933;}
.css .ln-xtra, .css li.ln-xtra, .css div.ln-xtra {background-color: #ffc;}
.css span.xtra { display:block; }

.html .de1, .html .de2 { margin:0; padding:0; background:none; vertical-align:top;}
.html .imp {font-weight: bold; color: red;}
.html li, .html .li1 {font-weight: normal; vertical-align:top;}
.html .ln {width:1px;text-align:right;margin:0;padding:0 2px;vertical-align:top;}
.html .li2 {font-weight: bold; vertical-align:top;}
.html .kw2 {color: #000000; font-weight: bold;}
.html .kw3 {color: #000066;}
.html .es0 {color: #000099; font-weight: bold;}
.html .br0 {color: #66cc66;}
.html .sy0 {color: #66cc66;}
.html .st0 {color: #ff0000;}
.html .nu0 {color: #cc66cc;}
.html .sc-2 {color: #404040;}
.html .sc-1 {color: #808080; font-style: italic;}
.html .sc0 {color: #00bbdd;}
.html .sc1 {color: #ddbb00;}
.html .sc2 {color: #009900;}
.html .ln-xtra, .html li.ln-xtra, .html div.ln-xtra {background-color: #ffc;}
.html span.xtra { display:block; }

.js .de1, .js .de2 {margin:0; padding:0; background:none; vertical-align:top;}
.js .imp {font-weight: bold; color: red;}
.js li, .js .li1 {font-weight: normal; vertical-align:top;}
.js .ln {width:1px;text-align:right;margin:0;padding:0 2px;vertical-align:top;}
.js .li2 {font-weight: bold; vertical-align:top;}
.js .kw1 {color: #000066; font-weight: bold;}
.js .kw2 {color: #003366; font-weight: bold;}
.js .kw3 {color: #000066;}
.js .co1 {color: #006600; font-style: italic;}
.js .co2 {color: #009966; font-style: italic;}
.js .coMULTI {color: #006600; font-style: italic;}
.js .es0 {color: #000099; font-weight: bold;}
.js .br0 {color: #009900;}
.js .sy0 {color: #339933;}
.js .st0 {color: #3366CC;}
.js .nu0 {color: #CC0000;}
.js .me1 {color: #660066;}
.js .ln-xtra, .js li.ln-xtra, .js div.ln-xtra {background-color: #ffc;}
.js span.xtra { display:block; }

.php .de1, .php .de2 {margin:0; padding:0; background:none; vertical-align:top;}
.php .imp {font-weight: bold; color: red;}
.php li, .php .li1 {font-weight: normal; vertical-align:top;}
.php .ln {width:1px;text-align:right;margin:0;padding:0 2px;vertical-align:top;}
.php .li2 {font-weight: bold; vertical-align:top;}
.php .kw1 {color: #b1b100;}
.php .kw2 {color: #000000; font-weight: bold;}
.php .kw3 {color: #990000;}
.php .kw4 {color: #009900; font-weight: bold;}
.php .co1 {color: #666666; font-style: italic;}
.php .co2 {color: #666666; font-style: italic;}
.php .co3 {color: #0000cc; font-style: italic;}
.php .co4 {color: #009933; font-style: italic;}
.php .coMULTI {color: #666666; font-style: italic;}
.php .es0 {color: #000099; font-weight: bold;}
.php .es1 {color: #000099; font-weight: bold;}
.php .es2 {color: #660099; font-weight: bold;}
.php .es3 {color: #660099; font-weight: bold;}
.php .es4 {color: #006699; font-weight: bold;}
.php .es5 {color: #006699; font-weight: bold; font-style: italic;}
.php .es6 {color: #009933; font-weight: bold;}
.php .es_h {color: #000099; font-weight: bold;}
.php .br0 {color: #009900;}
.php .sy0 {color: #339933;}
.php .sy1 {color: #000000; font-weight: bold;}
.php .st0 {color: #0000ff;}
.php .st_h {color: #0000ff;}
.php .nu0 {color: #cc66cc;}
.php .nu8 {color: #208080;}
.php .nu12 {color: #208080;}
.php .nu19 {color:#800080;}
.php .me1 {color: #004000;}
.php .me2 {color: #004000;}
.php .re0 {color: #000088;}
.php .ln-xtra, .php li.ln-xtra, .php div.ln-xtra {background-color: #ffc;}
.php span.xtra { display:block; }

.sql .de1, .sql .de2 {margin:0; padding:0; background:none; vertical-align:top;}
.sql .imp {font-weight: bold; color: red;}
.sql li, .sql .li1 {font-weight: normal; vertical-align:top;}
.sql .ln {width:1px;text-align:right;margin:0;padding:0 2px;vertical-align:top;}
.sql .li2 {font-weight: bold; vertical-align:top;}
.sql .kw1 {color: #993333; font-weight: bold;}
.sql .co1 {color: #808080; font-style: italic;}
.sql .coMULTI {color: #808080; font-style: italic;}
.sql .es0 {color: #000099; font-weight: bold;}
.sql .br0 {color: #66cc66;}
.sql .sy0 {color: #66cc66;}
.sql .st0 {color: #ff0000;}
.sql .nu0 {color: #cc66cc;}
.sql .ln-xtra, .sql li.ln-xtra, .sql div.ln-xtra {background-color: #ffc;}
.sql span.xtra { display:block; }

.xml .de1, .xml .de2 {margin:0; padding:0; background:none; vertical-align:top;}
.xml .imp {font-weight: bold; color: red;}
.xml li, .xml .li1 {font-weight: normal; vertical-align:top;}
.xml .ln {width:1px;text-align:right;margin:0;padding:0 2px;vertical-align:top;}
.xml .li2 {font-weight: bold; vertical-align:top;}
.xml .es0 {color: #000099; font-weight: bold;}
.xml .br0 {color: #66cc66;}
.xml .sy0 {color: #66cc66;}
.xml .st0 {color: #ff0000;}
.xml .nu0 {color: #cc66cc;}
.xml .sc-1 {color: #808080; font-style: italic;}
.xml .sc0 {color: #00bbdd;}
.xml .sc1 {color: #ddbb00;}
.xml .sc2 {color: #339933;}
.xml .sc3 {color: #009900;}
.xml .re0 {color: #000066;}
.xml .re1 {color: #000000; font-weight: bold;}
.xml .re2 {color: #000000; font-weight: bold;}
.xml .ln-xtra, .xml li.ln-xtra, .xml div.ln-xtra {background-color: #ffc;}
.xml span.xtra { display:block; }

/*todo blocks*/
.webixdoc_todo{
	border:4px solid #FAA;
	margin:10px;
}
.webixdoc_todo h3{
	padding:5px; margin:0px;
	background-color:#FAA;
	width:60px;
	border-bottom:none;
	text-align:center;
}
.webixdoc_todo p{
	margin: -26px 0px 3px 100px;
}


.webixdoc_note {
	background-color: #F7F4E8;

	background-image: url("common/note.gif");
	background-repeat: no-repeat;
	background-position: 7px center;

	border: 1px solid #DFCA89;
	padding-left: 60px;
	margin-bottom:10px;
}
.webixdoc_note p {
	margin:15px 0;
}

.webixdoc_snippet {
	margin-top:0px;
	font-family:Consolas, monospace;
	margin-bottom:0px;
	padding:0px 10px;
	color:#222;

	white-space:nowrap; display:inline-block;
	background:#F2F4F5;
	border:1px solid #D8E4E8;
	border-bottom:none;
	border-top-left-radius:4px;
	border-top-right-radius:4px;
}
.webixdoc_snippet span{
	display: none;

}

.webixdoc_sign{
	padding:10px 30px;
	background-color:#f8e8b8;
	font-size:15px;
	font-family:Consolas, monospace;
}

.webixdoc_params{
	margin-left:20px;
	font-size:13px;
}

.webixdoc_params th, .webixdoc_params td{
	font-weight:normal;
	text-align:left;
	padding:4px;
	margin:0px;
	line-height:25px;
}
.webixdoc_params th{
	border-bottom:1px solid orange;
}
.webixdoc_params tr:nth-child(even){
	background-color:#f8f0c8;
}
.webixdoc_method_col{
	width:100px;
}
.webixdoc_type_col{
	width:200px;
}
.hidden_row th{
	height:1px; line-height:1px; padding:0px;
}


.webixdoc_spacer{
	width:1000px;
	clear:both;
	height:15px;
}
.webixdoc_footer{
	width:960px;
	margin:auto;
	color:#ddd;
	clear:both;
}

.webixdoc_title{
	color:#E8E8E8;
	padding:20px 0px 0px 185px;
	font-size:24px;
}
.webixdoc_subtitle{
	font-size:12px;
	padding-top:8px;
}

.webixdoc_related a{
	padding-left:20px;
}


/*custom layouts*/
.webixdoc_chm .webixdoc_navarea, .webixdoc_start .webixdoc_navarea{
	display:none;
}
.webixdoc_chm .webixdoc_backtop, .webixdoc_start .webixdoc_backtop{
	display:none;
}
.webixdoc_chm .webixdoc_navarea, .webixdoc_chm .webixdoc_search, .webixdoc_start .webixdoc_navarea{
	display:none;
}
.webixdoc_chm .webixdoc_content, .webixdoc_chm .webixdoc_content_inner, .webixdoc_start .webixdoc_content, .webixdoc_start .webixdoc_content_inner{
	width:968px;
	float:none;
}
.webixdoc_chm, .webixdoc_chm .webixdoc_content{
	overflow:hidden;
}
.webixdoc_chm.webixdoc_page{
	margin-left: 0px;
}


/*api table*/

.webixdoc_content .webixdoc_links, .webixdoc_content .webixdoc_apitable{
	width:680px;
	border-left:none;
	border-top:none;
}
.webixdoc_content .webixdoc_apitable tr:nth-child(even), .webixdoc_content .webixdoc_links tr:nth-child(even){
	background-color: #F5FAF5;
}

.webixdoc_content  .webixdoc_apitable{
	width:740px;
}
.webixdoc_apitable .col1{
	width:140px;
	padding-left:10px;
}
.webixdoc_apitable .col4{
	width:80px;
	padding-right:10px;
	text-align:right;
}
.webixdoc_apitable .col2{
	width:100px;
}
.webixdoc_content .webixdoc_apitable td, .webixdoc_content .webixdoc_links td{
	border-right:none;
	border-bottom:none;
	font-size:13px;
	line-height: 20px;
}
.webixdoc_links0{
	width:140px;
	padding-left:10px;
}

.webixdoc_lesser{
	color:#444;
	padding-left:20px;
}

.webixdoc_parents{
	font-size:12px;
	padding-left:70px;
	position:relative;
}
.webixdoc_parents a{
	margin-left:10px;
	text-decoration: none;
}
.webixdoc_parents span{
	position:absolute;
	left:10px;
}

/*tables*/
.webixdoc_content table{
	border-left: 1px solid #dfdfdf;
	border-top: 1px solid #dfdfdf;
	margin-bottom: 1em;
	border-collapse: collapse;
	width:680px;
}
.webixdoc_content caption{
	text-align:left;
	color:#616060;
}
.webixdoc_content th {
	background:#F2F4F5;
	font-size:11px;
	padding:5px;
	text-transform:uppercase;
	border-bottom: 1px solid #dfdfdf;
	border-right: 1px solid #dfdfdf;
}
.webixdoc_content td {
	padding:5px;
	border-bottom: 1px solid #dfdfdf;
	border-right: 1px solid #dfdfdf;
}




/*search results*/
.webixdoc_search_results h3 {
	margin-top: 0px;
	margin-bottom: 0px;
	font-size: 1.1em;
}

.webixdoc_search_results h2 {
	line-height: 1em;
	padding-top: 1em;
	padding-bottom: 0;
}

.webixdoc_search_results h2 span,
.webixdoc_search_results span.rendertime {
	font-size: 11px;
	font-weight: normal;
	color: #999;
}

.webixdoc_search_results p {
	margin-top: 0px;
	margin-bottom: 30px;
}

.webixdoc_paging {
	margin-bottom: 30px;
}

.webixdoc_paging .webixdoc_item{
	float: left;
	height: 24px;
	width: 20px;
	border: 1px solid #cccccc;
	text-align: center;
	margin: 1px;

	line-height: 24px;
	color: #222;
	border-radius:3px;
}

.webixdoc_paging .webixdoc_item.active {
	background-color: #9c74ad;
	color:#fff;
}

.webixdoc_paging .webixdoc_item{
	width:40px;
	margin:2px 4px;
}


.webixdoc_paging .webixdoc_item:hover{
	background-color: #F2F4F5;
}

.webixdoc_paging .webixdoc_paging_clear {
	clear: both;
}

.webixdoc_search_results .viewall {
	padding-top: 14px;
}

.webixdoc_search_results .viewall .viewall_text {
	display: block;
	float: left;
}

.webixdoc_search_results .viewall .viewall_arrow {
	float: left;
	width: 36px;
	height: 20px;
}

.webixdoc_search_results .api_icon {
	display: inline-block;
	width: 20px;
	height: 20px;
	background-image: url(data:image/png;base64,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);
	background-repeat: no-repeat;
	background-position: 0px 0px;
	margin-right: 6px;
	position: relative;
	top: 5px;
}

.webixdoc_search_results .api_icon.method {
	background-position: 0px -20px;
}

.webixdoc_search_results .api_icon.property {
	background-position: 0px -40px;
}

.webixdoc_search_results .api_icon.event {
	background-position: 0px -60px;
}


/*autocomplete*/
.autocomplete {
	position: absolute;
	background-color: #FFFFFF;
	border: 1px solid #B6D0DE;
	border-bottom-right-radius:5px;
	border-bottom-left-radius:5px;
	box-shadow:5px 5px 10px #888;
	padding: 0px;
	margin: 0px;
	list-style-type: none;
}

.autocomplete li {
	padding: 16px 10px;
	margin: 0px;
	height: 32px;
	border-bottom: 1px dotted #B6D0DE;
	cursor: pointer;
	font-size: 15px;
	font-family: Consolas, monospace;
	overflow: hidden;
}
.autocomplete li:last-child{
	border-bottom:none;
}
.autocomplete li.hover {
	background-color: #F0F3F5;
}

.autocomplete li div.icon {
	width: 20px;
	height: 20px;
	background-image: url(data:image/png;base64,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);
	background-repeat: no-repeat;
	background-position: 0px 0px;
	display: block;
	float: left;
	margin-right: 4px;
}

.autocomplete li div.label {
	padding: 0px 4px;
	width: 600px;
	float: left;
	height: 20px;
}

.autocomplete li div.short {
	clear: both;
	font-size: 12px;
	font-weight: normal;
	color: #444;
	padding-left: 30px;
	line-height: 11px;
}

.autocomplete li .webixdoc_sign {
	padding: 0px;
	background-color: transparent;
	width: 590px;
	height: 20px;
	line-height: 20px;
	overflow: hidden;
}

.autocomplete li span {
	display: inline-block;
	position: relative;
	top: -5px;
}

.autocomplete li span.search_select {
	display: inline;
	top: 0px;
	padding: 0px;
}

.autocomplete li div.method {
	background-position: 0px -20px;
}

.autocomplete li div.prop {
	background-position: 0px -40px;
}

.autocomplete li div.event {
	background-position: 0px -60px;
}

.autocomplete li div.article {
	background-position: 0px -80px;
}


.webixdoc_sample{
	border: 1px solid #D8E4E8;
	padding: 2px 10px;
	margin-top:-2px;
	margin-right:5px;

	background: #F2F4F5;
	color: #222;
	text-transform: uppercase;

	padding-left: 10px;
	font-size: 10px;

	border-radius: 4px;
}

p + ul{
	margin-top:20px;
}

table caption{
	text-align:left;
	margin-bottom:3px;
}
table{
	border-spacing:0px;
	width:100%;
}
table td{
	padding: 5px;
}
td pre{
	margin-bottom:0px;
}

table th{
	padding:2px 5px;
	text-align:left;
	font-weight:normal;
}
table.list td:first-child, table.list th:first-child{
	text-align:center;
}

img{
	border:0px;
}


@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : landscape) {
	.webixdoc_page{
		width:800px;
	}
	.webixdoc_start .webixdoc_content{
		width: 938px;
	}
	.webixdoc_start .webixdoc_search {
		width: 909px;
	}
	.webixdoc_start .webixdoc_search input{
		width: 859px;
		height:23px;
	}
	.webixdoc_start .webixdoc_content_inner{
		width:950px;
	}
	.bottom_block li {
		padding-right:10px;
	}
	.webixdoc_navarea{
		width:170px;
	}
	.side_links{
		margin-left:485px;
	}
}

@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
	.webixdoc_page{
		width:720px;
	}
	.webixdoc_content{
		width:718px;
	}

	.webixdoc_start .webixdoc_content{
		width: 718px; overflow:hidden;
	}
	.webixdoc_start .webixdoc_search {
		width: 700px;
	}
	.webixdoc_start .webixdoc_search input{
		width: 650px;
	}
	.front_page{
		padding:0px 0px 0px 0px !important;
		width:710px !important;
	}
	.webixdoc_start .webixdoc_content_inner{
		width:720px;
	}
	.webixdoc_navarea{
		display:none;
	}
}

@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
	.webixdoc_content{
		width:450px;
	}

	.webixdoc_start .webixdoc_content{
		width:450px; overflow:hidden;
	}
	.webixdoc_start .webixdoc_search {
		width:450px;
	}
	.front_page{
		padding:0px 0px 0px 0px !important;
		width:450px !important;
	}

	.webixdoc_navarea{
		display:none;
	}
}


.webixdoc_wrong{
	font-weight:bold;
	color:red;
	background:#ddd;
	padding:5px 5px 5px 25px;
	border:1px dashed red;
	border-radius:5px;
}

.dhx_doc_complex{
	float:right;
	font-size:18px;
	padding-top:10px;
}


/*expandable topics*/
.webixdoc_content .topic_description{
	vertical-align: top;
	padding-top:40px;
	border-right:none;
}
.webixdoc_content td.topics{
	width:300px;
	border-right:none;
}
/*scheduler specific*/
.api_subsection_box{
	clear:both;
	margin:3px 0px;
}
.api_subsection{
	font-weight: bold; font-family: Arial;
	display:block;
	width:150px;
	float:left;
}

.webixdoc_deprecated{
	border: 4px solid #F66;
	padding:10px;
	margin:5px;
}

.webixdoc_deprecated h3 {
	margin:-10px -10px 10px -10px;
	padding:3px 10px;
	background-color:#F66;
	color:white;
}


/*navigation*/
.webixdoc_content .nav_table{
	padding:30px;
	width: 95%;
	margin-left: auto;
	margin-right: auto;
	border-width: 0;
}
.webixdoc_content .nav_table td{
	border-width: 0;
	padding:11px;
}
.webixdoc_content .nav_table tr{
	background: #f3f3f3;
}
.webixdoc_content .nav_table tr:nth-child(2n){
	background: white;
}
.webixdoc_content .nav_table a{
	font-size:16px;
}
.nav_breadcrumb{
	margin:18px 25px;
}


#webixContent{
	min-height: 750px;
}

/*navigation*/
.nav_folder_img{
	background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAdCAYAAADLnm6HAAADwUlEQVR4Xr2V3U+bVRzHCd5wIzEx8dIbL73Q/8Jro5o4jS564dhWYCrThQnOBBilzIGUV0sZb+3o1rLN0VJaKC0LpayDAgymRp2JeqP+C1/PJ3FP0pfnSS/smnyS33PO75zPl3OelrrSz1unPm9o7pm4fM49+fu5Pr9KYZx5+upq8Kk/0zXa7w0ltZz7QeuFx4ZfLXhmnHn66P+/AzS0uCf/imw81PXUQ80nD8pgPJI50kAgrtY+f3WYU3OZU3uzilNrZEFg9UAzK3u2zCb2FNk4VuLBz3LmFyUNS9ljfXs9YZ2a7b239vr+5J5rCKfxR+k7VH/WJBsOJRXP/ah04TfD45rA3jhw4cRt3fvte0e6kTZ3v3ZYU3DgwonbuvfQ+pGCq4dPBVw4cRPgOR4WUoe8fE8FXDhxWwE4Hr5m1eANpfTR+W5qoGas2vW4ygMETbK5xL4jo+ENavnubKrp0pA800tAzVhRjwO4ygKYZPuO3/3hmxm93XRBF65MqX8myldK77V+BdS6Mhtjjh4NhzNOe+EqD0Cy6fieLdzdWHhNJ8/36ERL55PvNFDrRHMHc/TQ67QXrvIAJJtaLlTkaiCpwYWURkKryEDtQwF9t5jWeGTd1EHGgB56WWO3H67iAC3mYTpekD+2W5FTFz0yv1zWX37RG1R06xE/x6Bo9hFjzNFDL2ts98PVUhqAZL7oTkViuZ8UTOT1af81JBoOJeRf3rXmqb0LK8zRQy9r7PbDVR5gMlbQxNKOLd/Mr+gTzxQSdfsWy+Z7fLeYo4dep71wFQdoNg++6K7G7z6oyMnPuvSO60vrhWvqHJA3sskcmDrLGHP00Msa2/1wNZcGINno9/mKzMXzimT2NRvbQsI/EnPXHWrzTBn81IwxRw+9rLHbD1dxAJd5INnInfsVmYxx3zvq+O9FA1f3mD5o6wZq5EAPvayx3Q+XqzQAyby3t21xzyf14Re98t5Y1bWlrPXCAfV0dIs5etRneh32wlUc4Kzbb5LlNXRr2xZSh9MHCqwdyBvOINVMLGfYptaQGWOOnjHT67QXLpxWgDO9fpPsvgYXc46QnNo9t6JL4zfli+0ANWNFPU7gwmkFOH2ZANsaiGxVxdjdvCL3jqmBmrGq1+PCaQX4uMv3z2B4E4GuhrM1BQcunE8CNL7b5p5oH4nIPZ+SJ5hWX2C9JrA3DlzGOYmbAA2Nz7/w0mvvt86+frrz7zdcX6uW4MCFEzcB6g3PGl40vGx4xfBqjWBvHLhw4q6DZ0jDkXAvNQYHLpx1/wJsd8eSoFZ04QAAAABJRU5ErkJggg==);
	width:32px;
	height:29px;
}
.nav_page_img{
	background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAdCAYAAADLnm6HAAADzklEQVR4Xu2WX0xTVxzHN3kyfeCN+IBPe8EXx8vCK7yYDX1pHKW4zYTaaCiUNg3t1BRaqLQlkZZe244NsIuosvkfkaQKVGhBqBuAVDeiGBEBUmgLiFJbuN/dc9PwtyKTG548ySf59STnfj/nnnNPzxefW4xd5oY+fp1zdPL8vTFsh7MNvuXaUN+3XJfW9rA1ySBZJHO1AM90bdhfdXME26XI4lmulVT8uszRHySZqwWSyi4PgwtUv/ZDaupcwdwJmeXhmt+n63wgmasFkpW1Q9hJSOZqgb0S21OMhcApnU9DcftJFslcI5Br8u2YAJO1UeBIxWNivCMwWXEFiN1OsKnAsqXHF0Bjixe/1zfAarPDZK6CtboOf9xph9s3vekMY8+Jx9YEXL2vUV3jgMFggKnqHOqvXIeDEdGdMUClUkFfUYmWR6+4F3gdpNExOAXrLzUoLSuDy+NFOEKDpkFg63ttnShSKqHVGeAenOZQwDiAkako7rd7odfr0dXtRayBFcBK6/B0QSaT4VazK94af5pAjrEfz8ffgrL9hrOVlQjOhRGYByZmVgJITfrehGlotFqcKtZxtwRE4J+XIZTr9ewSPB+bYwJpTM4iLrbq8xAdE3MoYOiHb3ga6uISmCk7XozPwj9HY+oN4mKxVuPHn45yL1CsLYfq55N4NRHEzNslzC1gA7PvaEilMhTIVdwvgcl+AXl5eWhsuov5d+/xPkojsog1tLS6kJOTg3O1V7kVeDY2jz+dvVAoTyE/vwBOpxMLC2FEo4tYXFxCJBJFa2sbcnNFkEgV6Hjs5/gz9EfQ3DUMx1UXCuVFEIvF7DdvtVphsVAoKJBCKBQysz8Cyl6DB72j3B5Eo4EluPomccc9hIuND1FaYcNxSSEE2UIc/j4LR3OPo0RP4bTGAIVCAZvNDqd7gNuj2O0LEAn2TVy7P4DLTV4iw3KpqQdX7jK/b3fhRL4ccrkcFEXhdnMb3IP+bQkkZ595FCQCBHIkk+Uge4JszCcvAgS2/ndkBn1DE3A++Bsi8QlIJBKYzWZcv3ELz0ZntvRvyGSF1t+Ikr7hq0sEup5ZYsdiHGBhNucGhOV/4XBJOw7KG3DgUBZEIhGMRiPqHBdQaOkm4z+IQNcdIlnr74Q8hn0M3zFkMQg+QnaMH3g8niwtLW1cIBBArVaTDRtOT09XxR/HPvtbkrX+Vrwr1pEUezV7/w+JiYn7U1NTPZmZmewZotFonnxgTHIsg8dmctS+ZNidkJCwJyUlRZuRkTHB5/N9TN8ehoTNAjgViM3sK4avGfZvVeA/tqV+XfMiVxcAAAAASUVORK5CYII=);
	width:32px;
	height:29px;
}
.nav_back_img{
	background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA75JREFUeNrsV21Ik1EUvq/bdMPhzG9Npvg1Zugfo18FkRT1swyC+jUSIiSJwB+CPyxFEyVRNDCEiWCIRZAQiVCaBYK/FPxI/JhLm+lkpptu2j56ztv72vIDt2lK0IWHc9/dc+597rnnnnPHeTwedpwtiB1z+3cJNDY2Vh4GAam/Bg0NDRKIJuA2UHykHqirqwt2uVwvYmNjb0Me7RHU1tYq3W53d1xc3NXo6GiGPjuyI6ipqYmGeJuYmJgTERHB1tfX2WF5YF8CVVVVaojupKQkzYnwcOaw21lQUNDREKioqMikxVNSUk6qwsKYw+HYGvvrBMrKys6Q29PT0yOUSiVzbGz8HkT2PCwC3G6puLS09CLEa61Wq1DI5czpdP5pxHHsq8nElpaW7PgcBcaBN0AXbC0HIlBSUnIDojU7OztYJpPtuVMiERkZyQfk2toaM5vNzGQy2TDUAjwpLy83+E2guLj4bkJCwtP4+Hj+e7+rRiSkUimPkJAQXs7NzbGxsTHyzKPKysrHPhMoKio6GxUV9VGTkcG7PJAzptuhUCh4OWM0MoPB8BI/36qurt7cNxFB6dPCwsK9z+PjbNVqZUTLDXL+wAnSVpuNB64tS05Ovo6N6P2KgcLCwpsQ+kytNlilUrG93gvkfoJ3E3VJ0hh5Y2Zmhk0bDHfq6+uf+XwLCgoKLkO80mg0ChzLjligyeFeNj8/z0JDQ3mXSyQSvh+OZEXERXJk29/fT8GZggpq9qkWQLELcZA7MjJioUXEiUQQ6dnZWdLjVlZWTi0vL5/GlTxvNBrvDw0Ndfb19dmGh4eZxWLhyaWlpSkx3wOfPSC2/Pz8LMqEqampcagDW56g3fb29rLm5mZuDzsq2eTFhyheOWq1mg0MDFB+iIGNy2cC1HQ6HdWCd0jHaTQR6dOuenp6mF6v5/a7GbCnmGqKiYlRLi4u5sLmvV/lGAZfEMnnJiYmBqenp7fO1tdrCvvn0L2Eo7RAXgjoPdDa2vqNSExOTn4AEZ6EP3kC9v3Q1wFZAT9I2trabJjgCkh0jo6O+p2oYN8Jm8kDPcna29vtCMRrU1NTLYG8iGDTeeBHaUdHB21dl5eXZ/HXFh4Y3HENvSR5QwYEC5LISby8JOqINp5d5uEzuNcY9V0CqB78EKT7V8L08Itw2wgoARUQDoQBoYACkAvjEkFfJOf2gktYhF4v6wJWgO/AKmAVdESCHm6XXUiFhUKEReVCX+ZFQLotfmhxpzC5U9glkXAI2BTg9PYeXzP+/zk9bgI/BRgApBT2zDk4SMkAAAAASUVORK5CYII=);
	width:32px;
	height:32px;
}


#webixContent{
	display: flex;
}

.webixdoc_content_inner{
	float: left !important;
	width: 30% !important;
}

.webixdoc_content_demo{
	float: right;
	width: 70%;
}


.webixdoc_params th, .webixdoc_params td{
	font-weight:normal;
	text-align:left;
	padding:4px;
	margin:0px;
	line-height:25px;
}

.toggler {
	display: none;
}

.toggler + .toggler-content {
	max-height: 0;
	opacity: 0;
	overflow: hidden;
	transition: all .1s ease-in-out;
}

.toggler:checked + .toggler-content {
	max-height: 100%;
	opacity: 1;
}

.content{
	display: flex;
	flex-direction: row;
}

.loading_icon{
	display: block;
	-webkit-animation: spin 1s linear 0s infinite normal;
	-moz-animation: spin 1s linear 0s infinite normal;
	-ms-animation: spin 1s linear 0s infinite normal;
	-o-animation: spin 1s linear 0s infinite normal;
	animation: spin 1s linear 0s infinite normal;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}
@-webkit-keyframes spin {
	0% {-webkit-transform: rotate(0deg); }
	100% { -webkit-transform: rotate(360deg); }
}
@-ms-keyframes spin {
	0% {-ms-transform: rotate(0deg); }
	100% { -ms-transform: rotate(360deg); }
}
@-moz-keyframes spin {
	0% { -moz-transform: rotate(0deg); }
	100% { -moz-transform: rotate(360deg); }
}
@-o-keyframes spin {
	0% { -o-transform: rotate(0deg); }
	100% { -o-transform: rotate(360deg); }
}

#x6{
	position: relative;
	top: -1px;
	left: -1px;

	height: calc(100% + 2px);
	width: calc(100% + 2px);
}

.demo.loading .preview_frame{
	background: url(data:image/gif;base64,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) center center / 40px no-repeat;
}

.toggle_list_placeholder{
	top: 0;
	width: 1vh;
	height: 5vh;
	line-height: 5vh;
	visibility: hidden;
	background: #f3f3f3;
}

#source_code{
	height: 100%;
	display: none;
}

#source_code_text,
.CodeMirror{
	width: 100%;
	height: 100%;
	resize: none;
}
.CodeMirror{
	height: 100% !important;
}

@media screen and (max-width: 800px) {
	.page_space{
		height: 800px;
		width: 800px !important;
		box-sizing: border-box;
	}
	.page_space{
		height: calc(800px + 5vh);
	}

	#source_code_text, .CodeMirror{
		width: 800px;
		height: 800px;
	}

	.CodeMirror{
		height: 800px !important;
	}

	.demo{
		margin-left: 0 !important;
	}

	.side_links{
		margin-left: auto;
		position:relative;
	}
}


@media screen and (max-width: 400px) {
	.toggler-content div{
		line-height: 40px;
		padding: 6px;
		padding-left: 40px;
	}
}

/* product samples */
/* == reset == */
a, article, body, div, footer, form, fieldset, h1, h2, h3, h4, h5, h6, header, html, i, img, li, nav, ol, p, section, span, u, ul, input, textarea, dl, dt, dd {
	margin: 0;
	padding: 0;
}

svg,use{
	display: block;
}

a{
	outline: none;
	cursor: pointer;
	text-decoration: none;
}

img, fieldset{
	border: none
}

li{
	list-style: none;
}

iframe{
	display: block;
	border: none;
}

input[type='text'],
input[type='text']:focus,
input[type='text']:active,
input[type='email'],
input[type='email']:focus,
input[type='email']:active,
input[type='tel'],
input[type='tel']:focus,
input[type='tel'],
input[type='submit'],
input[type='submit']:focus,
input[type='submit']:active,
input[type='password'],
input[type='password']:focus,
input[type='password']:active,
select,
textarea{
	-webkit-appearance: none;
}

.svg{
	display: none;
}

.dhx-container{
	position: relative;

	max-width: 1260px;
	padding-left: 20px;
	padding-right: 20px;
	margin-left: auto;
	margin-right: auto;

	box-sizing: border-box;
}

@media (max-width: 480px){
	.dhx-container{
		padding-left: 15px;
		padding-right: 15px;
	}
}

/* == header == */
@media (min-width: 1025px){
	.page-header{
		position: relative;

		padding: 0 35px 0 20px;

		box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.1), 0 1px 4px 0 rgba(0, 0, 0, 0.1);
		background-color: #fff;

		z-index: 10;
	}

	.page-header__inside{
		position: relative;
		text-align: right;
	}

	.dhx-logo{
		position: absolute;
		left: 0;
		top: 50%;

		margin-top: -25px;

		line-height: 0;
	}

	.dhx-logo-icon{
		width: 82px;
		height: 50px;
	}

	.dhx-logo-icon.line{
		fill: #2095f3;
	}

	.menu-link-block{
		display: inline-block;
		vertical-align: middle;

		line-height: 0;

		padding: 25px 0 25px 100px;
	}

	.menu-link-block:first-child{
		padding-left: 0;
	}

	.menu-link{
		position: relative;

		display: inline-block;

		color: #202020;
		font: 500 18px/24px "Roboto", Arial, sans-serif;

		z-index: 5;
	}

	.menu-link:hover,
	.menu-link.active{
		color: #03a9f4;
	}
}

.share_dialog{
	position: fixed;
	top: 80px;
	left: 340px;

	padding: 15px 24px;

	box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.1), 0 1px 4px 0 rgba(0, 0, 0, 0.1);
	background-color: #fff;

	z-index: 10;
}

.share_dialog-field{
	position: relative;

	margin-top: 10px;
	padding-right: 70px;
}

.share_text{
	font-weight: 500;
}

.share_link{
	display: block;

	font: 400 16px/1.625 "Roboto", Arial, sans-serif;

	min-width: 300px;
	padding: 5px 15px;

	border-radius: 30px;
	border: none;
	background-color: #ebedf0;

	box-sizing: border-box;

	transition: box-shadow 0.25s ease-in-out;
}

.share_link:focus{
	box-shadow: inset 0 0 0 1px #03a9f4;
	outline: none;
}

.share_button{
	position: absolute;
	right: 0;
	top: 0;

	color: #fff;
	font: 500 14px/2.571428571428571 "Roboto", Arial, sans-serif;

	padding: 0 14px;

	border-radius: 30px;
	border: none;
	background-color: #2095f3;

	cursor: pointer;

	transition: color 0.25s ease-in-out;
}

.share_button:focus{
	outline: none;
}

/* == page body == */
.page-body{
	display: -ms-flexbox;
	display: -webkit-flex;
	display: flex;

	height: calc(100vh - 74px);
}

.page-aside,
.page-aside__container,
.page-aside__inside,
.page-container{
	height: 100%;

	box-sizing: border-box;
}

.page-aside{
	min-width: 320px;
	max-width: 320px;

	background-color: #fff;

	overflow: hidden;
}

.page-aside__inside{
	display: -ms-flexbox;
	display: -webkit-flex;
	display: flex;
	-webkit-flex-direction: column;
	-ms-flex-direction: column;
	flex-direction: column;
}

@media screen and (min-width: 1025px){
	.page-aside.aside-state{
		min-width: 80px;
		max-width: 80px;
	}

	.page-aside.aside-state .search_wrapper,
	.page-aside.aside-state .aside-nav{
		display: none;
	}
}

.page-container{
	padding: 15px;
	width: 100%;
}

.demo-container{
	display: -ms-flexbox;
	display: -webkit-flex;
	display: flex;
	-webkit-flex-direction: column;
	-ms-flex-direction: column;
	flex-direction: column;

	height: 100%;

	border: 1px solid #cfcfcf;
	background-color: #fff;

	box-sizing: border-box;
}

.demo-container__nav{
	font-size: 0;
	line-height: 0;

	padding: 7px 0 6px 6px;

	border-bottom: 1px solid #cfcfcf;
}

.demo-container__nav-btn{
	display: inline-block;
	vertical-align: top;

	color: #1c1e21;
	font: 500 16px/1.875 "Roboto", Arial, sans-serif;

	margin-left: 11px;
	padding: 0 14px;

	cursor: pointer;

	transition: color 0.25s ease-in-out;
}

@media screen and (max-width: 1024px){
	.demo-container__nav-btn.desktop{
		display: none;
	}
}

.demo-container__nav-btn:first-child{
	margin: 0;
}

@media screen and (min-width: 1025px){
	.demo-container__nav-btn:hover,
	.demo-container__nav-btn.active{
		color: #03a9f4;
	}
}

.demo-container__nav-btn.show_sample{
	color: #fff;

	margin-left: 25px;

	border-radius: 30px;
	background-color: #2095f3;
}

.demo{
	width: 100%;
	height: 100%;

	transition: all 0.1s ease-in-out;

	overflow: hidden;
}

/* aside */
/* == aside head == */
.page-aside__head{
	font-size: 0;
	line-height: 0;
	white-space: nowrap;

	width: 320px;
	padding: 20px;

	box-sizing: border-box;

	-webkit-transform-origin: left top;
	transform-origin: left top;
}

.page-aside.aside-state .page-aside__head{
	-webkit-transform: rotate(-90deg) translateX(-272px);
	transform: rotate(-90deg) translateX(-272px);
}

.toggle-list-btn,
.page-aside__head-title{
	display: inline-block;
	vertical-align: middle;

	user-select: none;
}

.toggle-list-btn{
	width: 28px;
	height: 28px;
	padding: 8px 5px;

	border-radius: 50%;

	box-sizing: border-box;

	cursor: pointer;

	transition: background-color 0.25s ease-in-out;
}

@media screen and (min-width: 1025px){
	.toggle-list-btn:hover,
	.toggle-list-btn:active{
		background-color: #ededed;
	}
}


.toggle-list-btn__row{
	height: 2px;
	width: 18px;

	margin-top: 3px;

	background-color: #03a9f4;
}

.toggle-list-btn__row:first-child{
	margin-top: 0;
}

.page-aside__head-title{
	font: 500 18px/2 "Roboto", Arial, sans-serif;

	margin-left: 8px;

	cursor: pointer;
}

/* == search == */
.search_wrapper{
	position: relative;
	padding: 0 20px;
	margin-bottom: 15px;
}

.search-elem{
	position: relative;
}

.search-elem__icon{
	position: absolute;
	top: 10px;
	left: 11px;

	pointer-events: none;
}

.search-field{
	display: block;

	font: 400 16px/1.625 "Roboto", Arial, sans-serif;

	width: 100%;
	padding: 5px 15px 5px 40px;

	border-radius: 30px;
	border: none;
	background-color: #ebedf0;

	box-sizing: border-box;

	transition: box-shadow 0.25s ease-in-out;
}

.search-field::-webkit-search-cancel-button{
	display: none;
}

.search-field:focus{
	box-shadow: inset 0 0 0 1px #03a9f4;

	outline: none;
}

.no_results{
	display: none;

	margin-top: 10px;
}

.no_results.visible{
	display: block;
}

.aside-nav{
	position: relative;

	height: 100%;

	overflow: auto;

	z-index: 1;
}

@media screen and (min-width: 1025px){
	.aside-nav::-webkit-scrollbar{
		width: 4px;

		border-radius: 20px;
		background: transparent;

		cursor: pointer;
	}

	.aside-nav::-webkit-scrollbar-thumb{
		border-radius: 20px;
		box-shadow: inset 0 0 0 7px #b3b3b3;
		background: transparent;

		cursor: pointer;
	}
}

.aside-nav > div{
	position: relative;
}

.aside-nav > div:first-child label{
	margin: 0;
}

.aside-nav label{
	position: relative;

	display: block;

	color: #606770;
	font: 500 16px/2.25 "Roboto", Arial, sans-serif;

	padding: 0 20px;
	margin-top: 10px;

	transition: color 0.25s ease-in-out;

	z-index: 1;
	cursor: pointer;
}

.aside-nav label.hidden,
.aside-nav label.hidden + .toggler + div + div{
	display: none;
}

.aside-nav label:hover,
.aside-nav label.active{
	color: #03a9f4;
}

.aside-nav .link{
	color: #606770;
	font: 500 16px/1.25 "Roboto", Arial, sans-serif;

	padding: 5px 20px 4px 64px;
	margin-top: 6px;

	transition: color 0.25s ease-in-out, background-color 0.25s ease-in-out;

	cursor: pointer;
}

.aside-nav .link:first-child{
	margin: 0;
}

.aside-nav .link:hover,
.more-link:hover{
	color: #03a9f4;
}

.aside-nav .link.active{
	color: #03a9f4;

	background-color: #f2f2f2;
}

.toggler-icon{
	position: absolute;
	right: 28px;
	top: 12px;

	transition: transform 0.25s ease-in-out;

	z-index: 0;
}

.arrow-icon{
	width: 8px;
	height: 12px;
}

.arrow-icon.line{
	fill: #606770;
}

.toggler:checked + .toggler-content + .toggler-icon{
	-webkit-transform: rotate(90deg);
	transform: rotate(90deg);
}

@media screen and (min-width: 1025px){
	.page-aside.aside-state .more-link-container{
		display: none;
	}
}

.more-link{
	position: relative;

	display: block;

	color: rgba(96, 103, 112, 0.7);
	font: 500 16px/2.25 "Roboto", Arial, sans-serif;

	padding: 5px 0 5px 40px;
	margin-top: 3px;

	transition: color 0.25s ease-in-out;
}

.more-link__icon{
	position: absolute;
	left: 20px;
	top: 50%;
	margin-top: -7px;
}

.more-link__v{
	width: 12px;
	height: 13px;
}

.more-link__v.line{
	fill: rgba(96, 103, 112, 0.7);

	transition: all 0.25s ease-in-out;
}

.more-link:hover .more-link__v.line{
	fill: #03a9f4;
}

/* api section */
.api-container{
	font: 500 18px/1 "Roboto", Arial, sans-serif;

	height: 100%;

	overflow: auto;
}

.api-container h3{
	font-weight: 500;
	font-size: 18px;

	margin-top: 40px;
}

.api-container h3:first-child{
	margin: 0;
}

.api-container h3 > div > div{
	margin-top: 20px;
}

.api-container h3 > div > div:first-child{
	margin-top: 15px;
}

.api-container__inside{


	padding: 40px 24px;
}

.api-container a{
	position: relative;

	display: inline-block;

	color: #0288d1;
	font: 400 16px/1.5 "Roboto", Arial, sans-serif;
}

.api-container h3 > div > div a{
	padding-left: 18px;
}

.api-container h3 > div > div a:before{
	content: "";

	position: absolute;
	left: 0;
	top: 50%;
	margin-top: -3px;

	width: 6px;
	height: 6px;

	border-radius: 50%;
	background-color: #9c9c9c;
}

/* footer */
.footer{
	color: #fff;

	padding: 45px 0 40px;

	background-color: #303846;
}

.footer a{
	position: relative;

	color: #fff;
	text-decoration: none;
}

.footer a:before{
	content: "";

	position: absolute;
	left: 0;
	bottom: 0;

	width: 0;
	border-top: 1px solid #fff;

	transition: width 0.15s ease-in-out;
}

.footer a:hover:before{
	width: 100%;
}

.footer-nav{
	display: -ms-flexbox;
	display: -webkit-flex;
	display: flex;
	-webkit-flex-wrap: wrap;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	-webkit-justify-content: space-between;
	-ms-flex-pack: justify;
	justify-content: space-between;

	font-size: 0;
	line-height: 0;
}

.footer-nav__col{
	display: inline-block;
	vertical-align: top;

	padding-left: 80px;
	box-sizing: border-box;
}

.footer-nav__col:first-child{
	padding-left: 0;
}

.footer-nav__title{
	padding-bottom: 9px;
}

.footer-title-link{
	font: 600 16px/1.5 "Roboto", Arial, sans-serif;
}

.footer-nav__row{
	padding-top: 8px;
}

.footer-link{
	font-size: 16px;
	line-height: 1.5;
}

.footer-nav__sub-cols{

}

.footer-nav__sub-col{
	display: inline-block;
	vertical-align: top;

	padding-left: 40px;
	box-sizing: border-box;
}

.footer-nav__sub-col:first-child{
	padding: 0;
}

.copyrights{
	text-align: center;

	padding-top: 40px;
}

@media screen and (max-width: 800px){
	.footer-nav{
		margin-top: -40px;
	}

	.footer-nav__col{
		width: 50%;
		padding: 40px 0 0;
	}
}

@media screen and (max-width: 480px){
	.footer-nav__col{
		width: 100%;
	}
}

/* media */
@media screen and (min-width: 1025px){
	.dhx-logo-icon.mobile,
	.toggle-list-btn.mobile,
	.toggle-list-title.mobile,
	.mobile-site-menu,
	.demo-container__nav-dropdown.mobile{
		display: none;
	}
}

@media screen and (max-width: 1024px){
	.site-menu.desktop,
	.page-aside__head.desktop,
	.dhx-logo-icon.desktop{
		display: none;
	}

	.page-header{
		font-size: 0;
		line-height: 0;

		box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.1), 0 1px 4px 0 rgba(0, 0, 0, 0.1);
		background-color: #fff;
	}

	.page-header__inside{
		padding: 6px 14px;
	}

	.dhx-logo,
	.toggle-list-title{
		display: inline-block;
		vertical-align: middle;
	}

	.dhx-logo{
		margin-left: 5px;
	}

	.dhx-logo-icon{
		width: 40px;
		height: 23px;
	}

	.dhx-logo-icon.line{
		fill: #2095f3;
	}

	.toggle-list-title{
		font: 500 18px/2 "Roboto", Arial, sans-serif;

		margin-left: 7px;
	}

	.toggle-list-btn{
		position: absolute;
		top: 0;
		right: 0;

		width: 56px;
		height: 48px;

		padding: 18px 0;
	}

	.toggle-list-btn__row{
		margin-left: auto;
		margin-right: auto;
	}

	.toggle-list-btn:active .toggle-list-btn__row:first-child{
		transform: translateY(1px);
	}

	.toggle-list-btn:active .toggle-list-btn__row:last-child{
		transform: translateY(-1px);
	}

	.page-aside{
		position: fixed;
		left: 0;
		top: 0;

		width: 100%;
		min-width: 0;
		max-width: none;

		background-color: rgba(0, 0, 0, 0.4);

		overflow: visible;

		z-index: 11;

		opacity: 0;
		visibility: hidden;

		transition: all 0.25s ease-in-out;
	}

	.page-aside.aside-state{
		opacity: 1;

		visibility: visible;
	}

	.page-aside__container{
		position: relative;

		padding-top: 54px;
		max-width: 264px;

		box-shadow: 0 4px 15px 0 rgba(0, 0, 0, 0.25);
		background-color: #fff;

		-webkit-transform: translate3d(-100%, 0, 0);
		transform: translate3d(-100%, 0, 0);
		opacity: 0;

		transition: all 0.25s ease-in-out;
	}

	.page-aside.aside-state .page-aside__container{
		opacity: 1;

		-webkit-transform: translate3d(0, 0, 0);
		transform: translate3d(0, 0, 0);
	}

	.page-aside__inside{
		overflow: auto;
	}

	.search_wrapper-container{
		position: relative;

		z-index: 1;
	}

	.search_wrapper{
		padding: 0 16px;
	}

	.page-body{
		display: block;

		height: calc(100vh - 48px);
	}

	.page-container{
		padding: 8px;
	}

	.aside-nav{
		height: auto;
		overflow: visible;
	}

	.aside-nav label{
		margin-top: 8px;
		padding: 0 16px;
	}

	.aside-nav .link{
		padding: 2px 20px 2px 16px;
		margin-top: 4px;
	}

	.close-page-aside{
		position: absolute;
		right: 0;
		top: 0;

		width: 54px;
		height: 54px;

		z-index: 1;
	}

	.close-page-aside:before,
	.close-page-aside:after{
		content: "";

		position: absolute;
		left: 50%;
		top: 50%;

		margin: -1px 0 0 -9px;

		width: 18px;
		border-top: 2px solid #606770;
	}

	.close-page-aside:before{
		-webkit-transform: rotate(45deg);
		transform: rotate(45deg);
	}

	.close-page-aside:after{
		-webkit-transform: rotate(-45deg);
		transform: rotate(-45deg);
	}

	.close-page-aside:active{
		-webkit-transform: scale(0.96);
		transform: scale(0.96);
	}

	.mobile-site-menu{
		position: relative;

		padding-bottom: 8px;
		margin-bottom: 20px;

		z-index: 1;
	}

	.mobile-site-menu:before{
		content: "";

		position: absolute;
		left: 16px;
		right: 16px;
		bottom: 0;

		border-top: 1px solid #dde0e5;
	}

	.mobile-site-menu__row{
		margin-top: 8px;
	}

	.mobile-site-menu__row:first-child{
		margin: 0;
	}

	.mobile-menu-link{
		display: block;

		color: #1c1e21;
		font: 500 16px/2.25 "Roboto", Arial, sans-serif;

		padding: 0 16px;
	}

	.mobile-menu-link:active{
		color: #2095f3;

		background-color: #fbfbfb;
	}

	.demo-container__nav{
		position: relative;

		padding: 3px 0 4px 10px;

		z-index: 10;
	}

	.demo-container__nav-btn.show_sample{
		position: absolute;
		right: 16px;
		top: 50%;
		margin-top: -12px;

		font-size: 16px;
		line-height: 1.5;
	}

	.demo-container__nav-dropdown{
		position: relative;

		line-height: 0;
	}

	.demo-container__nav-dropdown-chosen{
		position: relative;
		display: inline-block;

		color: #03a9f4;
		font-weight: 500;
		font-size: 16px;
		line-height: 2.25;

		padding-right: 18px;
	}

	.demo-container__nav-dropdown-arrow{
		position: absolute;
		right: 0;
		top: 50%;
		margin-top: -6px;

		-webkit-transform: rotate(90deg);
		transform: rotate(90deg);
	}

	.demo-container__nav-dropdown-list{
		position: absolute;
		left: 0;
		top: 100%;
		margin-top: -6px;

		padding: 8px 0;

		box-shadow: 0 1px 10px 0 rgba(0, 0, 0, 0.2);
		background-color: #fff;

		opacity: 0;
		visibility: hidden;
		-webkit-transform: translate3d(0, 10px, 0);
		transform: translate3d(0, 10px, 0);

		transition: all 0.25s ease-in-out;
	}

	.demo-container__nav-dropdown-list.opened{
		opacity: 1;
		visibility: visible;
		-webkit-transform: translate3d(0, 0, 0);
		transform: translate3d(0, 0, 0);
	}

	.demo-container__nav-dropdown-row{
		margin-top: 8px;
	}

	.demo-container__nav-dropdown-row:first-child{
		margin-top: 0;
	}

	.demo-container__nav-btn{
		display: block;

		font-size: 16px;
		line-height: 1.5;

		padding: 0 16px;
	}

	.demo-container__nav-btn:active{
		background-color: #f4f5f9;
	}

	.share_dialog{
		left: auto;
		top: 45%;

		max-width: 100%;

		box-sizing: border-box;
	}

	#source_code{
		overflow: auto;
	}

	.CodeMirror{
		min-width: 1100px;
	}
}

@media screen and (max-width: 480px){
	.share_dialog,
	.share_link{
		width: 100%;
	}

	.share_link{
		min-width: 0;
	}
}
