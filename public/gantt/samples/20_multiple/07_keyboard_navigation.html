<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Keyboard navigation, multiple gantts</title>
	<script src="https://cdn.dhtmlx.com/edge/dhtmlx.js?v=8.0.9"></script>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.9"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.9">


	<link rel="stylesheet" href="https://cdn.dhtmlx.com/edge/skins/skyblue/dhtmlx.css?v=8.0.9">

	<style>
		html, body {
			margin: 0px;
			padding: 0px;
			height: 100%;
		}
	</style>

	<script>

		window.addEventListener("DOMContentLoaded", function(){
			gantt.plugins({
				keyboard_navigation: true
			});
			var dhxLayout = new dhtmlXLayoutObject(document.body, "2E");
			dhxLayout.cells("a").attachGantt(null, null, gantt);
			gantt.parse({
				data: [
					{ id: 1, text: "Project #1", start_date: "01-04-2023", duration: 18,
						progress: 0.4, open: true
					},
					{ id: 2, text: "Task #1", start_date: "02-04-2023", duration: 8,
						progress: 0.6, parent: 1
					},
					{ id: 3, text: "Task #2", start_date: "11-04-2023", duration: 8,
						progress: 0.6, parent: 1
					}
				],
				links: [
					{id: 1, source: 1, target: 2, type: "1"}
				]
			});

			gantt2 = Gantt.getGanttInstance();
			gantt2.plugins({
				keyboard_navigation: true
			});
			dhxLayout.cells("b").attachGantt(null, null, gantt2);
			gantt2.parse({
				data: [
					{ id: 1, text: "Project #2", start_date: "01-04-2023", duration: 18,
						progress: 0.4, open: true
					},
					{ id: 3, text: "Task #3", start_date: "02-04-2023", duration: 1,
						progress: 0.6, parent: 1
					},
					{ id: 4, text: "Task #4", start_date: "03-04-2023", duration: 1,
						progress: 0.6, parent: 1
					}
				],
				links: [
					{id: 3, source: 3, target: 4, type: "0"}
				]
			});
		})
	</script>
</head>


<body>

</body>