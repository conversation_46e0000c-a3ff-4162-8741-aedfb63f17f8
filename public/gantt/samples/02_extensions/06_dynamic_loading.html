<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Loading subtasks on demand</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.9"></script>
	<link rel="stylesheet" href="../../codebase/skins/dhtmlxgantt_meadow.css?v=8.0.9">
	<style>
		html, body {
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}
	</style>
</head>

<body>
<div id="gantt_here" style='width:100%; height:100%;'></div>
<script>
	gantt.message({
		text: "This example requires a RESTful API on a backend. <br>Check our <a target='_blank' href='https://docs.dhtmlx.com/gantt/desktop__server_side.html'>guides on backend integration here</a> ",
		expire: -1
	});
	gantt.message({
		text: "Also, you can find our step-by-step tutorials for various platforms <a target='_blank' href='https://docs.dhtmlx.com/gantt/desktop__howtostart_guides.html'>here </a> ",
		expire: -1
	});

	gantt.config.date_format = "%Y-%m-%d %H:%i:%s";
	gantt.config.branch_loading = true;

	gantt.init("gantt_here");

	gantt.load("/gantt/backend/data-dynamic");

	var dp = gantt.createDataProcessor({
		url: "/gantt/backend/data-dynamic",
		mode: "REST"
	});
</script>
</body>