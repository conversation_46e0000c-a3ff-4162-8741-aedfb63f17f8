<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Auto Scheduling - Groups of connected tasks</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.9"></script>

	<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Open+Sans|Roboto:regular,medium,thin,bold">
	<link rel="stylesheet" href="../../codebase/skins/dhtmlxgantt_material.css?v=8.0.9">
	<style>
		html, body {
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}

		.weekend {
			background: #f4f7f4 !important;
		}

		.task_groups {
			background-color: orangered !important;
		}

		.task_groups .gantt_task_progress {
			background-color: red;
			opacity: 0.6;
		}
		.gantt_task_row.gantt_selected .weekend {
			background-color: #C0E8FF !important;
		}
	</style>
</head>

<body>
<div id="gantt_here" style='width:100%; height:100%;'></div>
<script>

	gantt.plugins({
		auto_scheduling: true
	});

	gantt.templates.scale_cell_class = function (date) {
		if (!gantt.isWorkTime(date)) {
			return "weekend";
		}
	};
	gantt.templates.timeline_cell_class = function (item, date) {
		if (!gantt.isWorkTime({date: date, task: item})) {
			return "weekend";
		}
	};
	gantt.templates.rightside_text = function(start, end, task) {
		if (task.type === gantt.config.types.milestone)
			return task.text;
		return ""
	};

	gantt.config.work_time = true;
	gantt.config.min_column_width = 60;

	gantt.config.auto_scheduling = true;
	gantt.config.auto_scheduling_strict = true;

	gantt.config.date_format = "%d-%m-%Y";

	(function() {
		var highlightTasks = [],
			highlightSearch = {};

		function reset(value) {
			if (value) {
				if (value.join() === highlightTasks.join()) {
				    return;
				}
				highlightTasks = value;
				highlightSearch = {};
				highlightTasks.forEach(function(id){
					highlightSearch[id] = true;
				});
				gantt.render();
			}
			else if (highlightTasks.length) {
				highlightTasks = [];
				highlightSearch = {};
				gantt.render();
			}
		}

		gantt.templates.task_class = function(start, end, task) {
			if (highlightSearch[task.id])
				return "task_groups";
			return "";
		};

		gantt.attachEvent("onTaskClick", function(id) {
			var task = gantt.getTask(id);
			var group = gantt.getConnectedGroup(id);
			if(!group.tasks.length){
				reset();
			}else{
				reset(group.tasks);
				gantt.message({
					text: "<strong>Selected task:</strong> " + task.text +
						"<br><strong>Connected Group:</strong><br> " +
						group.tasks.map(function(t) { return gantt.getTask(t).text}).join("<br>"),
					expire: 5000
					}
				);
			}

			return true;
		});

		gantt.attachEvent("onEmptyClick", function() {
			reset();
			return true;
		});

	})();

	gantt.message({text:"Click any task to highlight the connected group", expire:-1});

	gantt.init("gantt_here");
	gantt.parse({
		data:[
			{id:11,text:"Project #1",type:"project",progress:0.6,open:true,start_date:"02-04-2023",duration:14,parent:0},
			{id:12,text:"Task #1",start_date:"02-04-2023",duration:5,parent:"11",progress:1,open:true},
			{id:13,text:"Task #2",start_date:"03-04-2023",type:"project",parent:"11",progress:0.5,open:true,duration:13},
			{id:17,text:"Task #2.1",start_date:"03-04-2023",duration:2,parent:"13",progress:1,open:true},
			{id:18,text:"Task #2.2",start_date:"05-04-2023",duration:3,parent:"13",progress:0.8,open:true},
			{id:19,text:"Task #2.3",start_date:"10-04-2023",duration:4,parent:"13",progress:0.2,open:true},
			{id:20,text:"Task #2.4",start_date:"16-04-2023",duration:4,parent:"13",progress:0,open:true},
			{id:14,text:"Task #3",start_date:"02-04-2023",duration:6,parent:"11",progress:0.8,open:true},
			{id:15,text:"Task #4",type:"project",parent:"11",progress:0.2,open:true,start_date:"02-04-2023",duration:8},
			{id:21,text:"Task #4.1",start_date:"02-04-2023",duration:4,parent:"15",progress:0.5,open:true},
			{id:22,text:"Task #4.2",start_date:"06-04-2023",duration:4,parent:"15",progress:0.1,open:true},
			{id:23,text:"Mediate milestone",start_date:"12-04-2023",type:"milestone",parent:"15",progress:0,open:true,duration:0}
			],
		links:[
			{id:"15",source:"13",target:"17",type:"1"},
			{id:"16",source:"17",target:"18",type:"0"},
			{id:"17",source:"18",target:"19",type:"0"},
			{id:"18",source:"19",target:"20",type:"0"},
			{id:"21",source:"15",target:"23",type:"0"},
			{id:"22",source:"21",target:"22",type:"0"},
			{id:"23",source:"14",target:"15",type:"0"},
			{id:"24",source:"22",target:"23",type:"0"},
			{id:"25",source:"12",target:"19",type:"0"}
		]
	});

</script>
</body>