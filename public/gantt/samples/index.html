<!doctype html>
<html>
	<head>
		<link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;600&display=swap" rel="stylesheet" />

		<link rel="stylesheet" type="text/css" href="common/docs.css?v=8.0.9">
		<link rel="stylesheet" type="text/css" href="common/codehighlight/codemirror.css">
		<script src="common/codehighlight/codemirror.js"></script>
		<script src="common/codehighlight/xml.js"></script>
		<script src="common/codehighlight/htmlmixed.js"></script>
		<script src="common/codehighlight/javascript.js"></script>
		<script src="common/sample_sources.js"></script>
		<script src="common/bodyScrollLock.min.js"></script>
		<script src="common/sample_navigation.js"></script>
		<style class="custom_styles"></style>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta name="viewport" content="initial-scale = 1.0" />
		<title>Gantt : Samples</title>
		
	</head>
<body>
	
	<div class="page-header">
		<div class='page-header__inside'>
			<a href='//dhtmlx.com' title="DHTMLX - JavaScript Web App Framework & UI Widgets" class="dhx-logo" target="_blank">
				<svg viewBox="0 0 82 50" class="dhx-logo-icon desktop">
					<path fill-rule="evenodd" class="dhx-logo-icon line" d="M71.1 36L64.5 23.6L57.9 36H48L59 17.9L48 1H57.9L64.5 11.2L71.1 1H81L68.9 17.9L81 36H71.1ZM1 41V39H81V41H1ZM47 36L55 23.6V13L47 1V13.4H36V1H27V4.8C28.6 6.1 30 7.7 31 9.7C32.3 12.2 33 15.1 33 18.5C33 21.8 32.3 24.7 30.9 27.3C29.8 29.2 28.5 30.8 27 32.1V36H36V21.3H47V36ZM32 18.5C32 21.8 31.3 24.7 29.9 27.3C28.5 29.9 26.7 31.9 24.5 33.2C22.7 34.4 20.7 35.1 18.6 35.5C16.5 35.8 14.2 36 11.8 36H1V1H11.8C14.2 1 16.5 1.2 18.7 1.5C20.8 1.8 22.8 2.6 24.7 3.8C27 5.3 28.8 7.3 30.1 9.8C31.3 12.2 32 15.2 32 18.5ZM18.6 8.8C20.2 9.7 21.4 11 22.2 12.6C23 14.2 23.4 16.2 23.5 18.4C23.5 20.8 23.1 22.7 22.4 24.3C21.6 25.9 20.5 27.2 19.1 28.1C17.9 28.9 16.6 29.3 15.3 29.4C13.9 29.5 12.3 29.5 10.3 29.5H10.1V7.6H10.3C12.1 7.6 13.6 7.6 14.9 7.7C16.2 7.8 17.4 8.1 18.6 8.8ZM5 44V45H3V48H5V49H2V44H5ZM5 48V45H6V48H5ZM20 46H18V44H17V49H18V47H20V49H21V44H20V46ZM36 44V45H34V49H33V45H31V44H36ZM48 45H47V44H46V49H47V46H48V47H49V46H50V49H51V44H50V45H49V46H48V45ZM61 44H62V48H65V49H61V44ZM77 45H76V44H75V45H76V46H77V47H76V48H75V49H76V48H77V47H78V48H79V49H80V48H79V47H78V46H79V45H80V44H79V45H78V46H77V45Z"></path>
				</svg>

				<svg viewBox="0 0 40 23" class="dhx-logo-icon mobile">
					<path fill-rule="evenodd" class="dhx-logo-icon line" d="m34.612 16.655-3.265-5.869-3.265 5.87h-4.817l5.388-8.646L23.265 0h4.817l3.265 4.838L34.612 0h4.816L33.47 8.01l5.96 8.645h-4.817ZM.245 19.035v-.952h39.184v.952H.245Zm22.53-2.38 3.919-5.869V5.71L22.776 0v5.869h-5.388V0H12.98v1.824c.816.635 1.469 1.348 1.959 2.3.653 1.19.98 2.538.98 4.204 0 1.586-.327 2.934-1.062 4.203-.571.872-1.143 1.666-1.877 2.3v1.824h4.408v-6.98h5.387v6.98ZM15.43 8.328c0 1.586-.327 2.934-1.062 4.203-.734 1.269-1.55 2.22-2.612 2.776-.898.555-1.877.872-2.857 1.11a22.896 22.896 0 0 1-3.347.238H.245V0H5.55c1.143 0 2.286.08 3.347.238 1.061.159 2.04.555 2.939 1.11 1.143.714 2.04 1.666 2.612 2.855.653 1.11.98 2.538.98 4.125Zm-6.531-4.6c.735.396 1.306 1.03 1.714 1.824.408.793.572 1.745.653 2.776 0 1.11-.163 2.062-.571 2.775-.408.794-.898 1.349-1.633 1.825-.571.317-1.143.555-1.796.555-.653.08-1.47.08-2.449.08h-.081V3.172H7.02a5.64 5.64 0 0 1 1.878.555ZM2.204 20.462v.476h-.98v1.428h.98v.475H.734v-2.379h1.47Zm0 1.904v-1.428h.49v1.428h-.49Zm7.347-.952h-.98v-.952h-.49v2.38h.49v-.952h.98v.951h.49v-2.379h-.49v.952Zm7.837-.952v.476h-.98v1.903h-.49v-1.903h-.98v-.476h2.45Zm5.877.476h-.49v-.476h-.49v2.38h.49v-1.428h.49v.476h.49v-.476h.49v1.427h.49v-2.379h-.49v.476h-.49v.476h-.49v-.476Zm6.368-.476h.49v1.904h1.469v.475h-1.96v-2.379Zm7.836.476h-.49v-.476h-.49v.476h.49v.476h.49v-.476Zm-.49 1.428v-.476h.49v-.476h.49v.476h-.49v.476h-.49Zm0 0v.475h-.49v-.476h.49Zm1.47 0v-.476h-.49v.476h.49Zm0 0v.475h.49v-.476h-.49Zm-.49-.952h.49v-.476h-.49v.476Zm.98-.476v-.476h-.49v.476h.49Z"></path>
				</svg>
			</a>

			<div class="toggle-list-title mobile" onclick="toggle_list()">Gantt Samples</div>

			<div class="toggle-list-btn mobile" onclick="toggle_list()">
				<div class="toggle-list-btn__row"></div>
				<div class="toggle-list-btn__row"></div>
				<div class="toggle-list-btn__row"></div>
			</div>

			<ul class="site-menu m-dropdown desktop">
				<li class="menu-link-block">
					<a href="//docs.dhtmlx.com/gantt/" title="" class="menu-link" target="_blank">Documentation</a>
				</li>

				<li class="menu-link-block">
					<a href="//docs.dhtmlx.com/gantt/api__refs__gantt.html" title="" class="menu-link" target="_blank">Gantt API</a>
				</li>

				<li class="menu-link-block">
					<a href="//forum.dhtmlx.com/c/gantt" title="" class="menu-link" target="_blank">Developer forum</a>
				</li>
			</ul>
		</div>
	</div>

	<div class="page-body">
		<div class="page-aside" id="page-aside" onclick="toggle_mobile_menu(event);">
			<div class="page-aside__container">
				<div class="close-page-aside" onclick="toggle_list()"></div>

				<div class="page-aside__inside">
					<!-- main burger -->
					<div class="page-aside__head desktop">
						<div class="toggle-list-btn" onclick="toggle_list()">
							<div class="toggle-list-btn__row"></div>
							<div class="toggle-list-btn__row"></div>
							<div class="toggle-list-btn__row"></div>
						</div>

						<div class="page-aside__head-title" onclick="toggle_list()">DHTMLX Gantt Samples</div>
					</div>

					<ul class="mobile-site-menu">
						<li class="mobile-site-menu__row">
							<a href="//docs.dhtmlx.com/gantt/" title="" class="mobile-menu-link" target="_blank">Documentation</a>
						</li>

						<li class="mobile-site-menu__row">
							<a href="//docs.dhtmlx.com/gantt/api__refs__gantt.html" title="" class="mobile-menu-link" target="_blank">Gantt API</a>
						</li>

						<li class="mobile-site-menu__row">
							<a href="//forum.dhtmlx.com/c/gantt" title="" class="mobile-menu-link" target="_blank">Developer forum</a>
						</li>
					</ul>

					<!-- search -->
					<div class="search_wrapper-container">
						<div class="search_wrapper">
							<div class="search-elem">
								<span class="search-elem__icon">
									<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
										<path fill-rule="evenodd" clip-rule="evenodd" d="M11.5088 11.509L14.7789 14.7791L11.5088 11.509C9.15535 13.8625 5.33959 13.8625 2.98615 11.509C0.632628 9.15553 0.632628 5.33977 2.98615 2.98633C5.33959 0.632811 9.15535 0.632811 11.5088 2.98633C13.8623 5.33977 13.8623 9.15553 11.5088 11.509V11.509Z" stroke="black" stroke-linecap="round" stroke-linejoin="round"></path>
									</svg>
								</span>

								<input type="search" class="search-field" placeholder="Find samples" oninput="filterSamples(this.value)" />
							</div>

							<span class="no_results">No results</span>
						</div>
					</div>

					<!-- demos navigation -->
					<svg class="svg" xmlns="http://www.w3.org/2000/svg">
						<symbol viewBox="0 0 8 12" id="arrow">
							<path d="M0.589966 10.58L5.16997 6L0.589966 1.41L1.99997 0L7.99997 6L1.99997 12L0.589966 10.58Z"></path>
						</symbol>
					</svg>

					<div class="aside-nav links">
									<div>
				<label for="01_initialization">Initialization</label>
				<input type="checkbox" id="01_initialization" class="toggler" />
				<div class="toggler-content">
					<div data-folder=01_initialization id=01_basic_init.html class="link">Basic initialization</div><div data-folder=01_initialization id=02_load_json.html class="link">Load data from JSON file</div><div data-folder=01_initialization id=03_load_xml.html class="link">Load data from XML file</div><div data-folder=01_initialization id=04_save_rest.html class="link">Backend storage using REST API</div><div data-folder=01_initialization id=06_touch_forced.html class="link">Forced touch mode</div><div data-folder=01_initialization id=07_jquery.html class="link">jQuery integration</div><div data-folder=01_initialization id=08_explicit_time_range.html class="link">Define date range</div><div data-folder=01_initialization id=09_backward_compatibility.html class="link">Loading data in Gantt 1.6 format</div><div data-folder=01_initialization id=10_fixed_size.html class="link">Fixed size gantt</div><div data-folder=01_initialization id=11_clickable_links.html class="link">Clickable links</div><div data-folder=01_initialization id=12_localization.html class="link">Localization</div><div data-folder=01_initialization id=13_project_duration.html class="link">Project duration</div><div data-folder=01_initialization id=14_reinitializtion.html class="link">ReInit in different container</div><div data-folder=01_initialization id=15_connector_json_enddate.html class="link">Loading tasks start/end dates</div><div data-folder=01_initialization id=16_projects_and_milestones.html class="link">Projects and milestones</div><div data-folder=01_initialization id=17_bootstrap.html class="link">Bootstrap layout</div><div data-folder=01_initialization id=18_backward_planning.html class="link">Backward planning</div><div data-folder=01_initialization id=19_tasks_without_dates.html class="link">Show Unscheduled Tasks</div><div data-folder=01_initialization id=20_tasks_outside_timescale.html class="link">Tasks outside timescale</div><div data-folder=01_initialization id=21_rollup_tasks.html class="link">Rollup tasks and milestones</div>
				</div>

				<div class="toggler-icon">
					<svg class="arrow-icon" viewBox="0 0 8 12">
						<use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#arrow" class="arrow-icon line"></use>
					</svg>
				</div>
			</div>
			<div>
				<label for="02_extensions">Extensions</label>
				<input type="checkbox" id="02_extensions" class="toggler" />
				<div class="toggler-content">
					<div data-folder=02_extensions id=01_quickinfo.html class="link">QuickInfo extension</div><div data-folder=02_extensions id=02_tooltip.html class="link">Tooltip</div><div data-folder=02_extensions id=03_critical_path.html class="link">Critical path</div><div data-folder=02_extensions id=04_grid_resize.html class="link">Resizable columns in grid</div><div data-folder=02_extensions id=05_today_line.html class="link">Today and Status lines in Gantt</div><div data-folder=02_extensions id=06_dynamic_loading.html class="link">Loading subtasks on demand</div><div data-folder=02_extensions id=07_managing_grid_columns.html class="link">Hiding grid columns</div><div data-folder=02_extensions id=08_tasks_grouping.html class="link">Tasks grouping</div><div data-folder=02_extensions id=09_multiselection.html class="link">Multiselection and Indent/Outdent tasks</div><div data-folder=02_extensions id=10_tasks_grouping_multilevel.html class="link">Multi level task grouping</div><div data-folder=02_extensions id=11_full_screen.html class="link">Full Screen</div><div data-folder=02_extensions id=12_auto_scheduling.html class="link">Auto Scheduling extension</div><div data-folder=02_extensions id=13_smart_rendering.html class="link">Working with 30000 tasks</div><div data-folder=02_extensions id=14_undo.html class="link">Undo/Redo changes in Gantt</div><div data-folder=02_extensions id=16_keyboard_navigation.html class="link">Keyboard Navigation</div><div data-folder=02_extensions id=17_keyboard_navigation_cell.html class="link">Keyboard Navigation - navigate cells</div><div data-folder=02_extensions id=18_linked_tasks.html class="link">Auto Scheduling - Groups of connected tasks</div><div data-folder=02_extensions id=19_constraints_scheduling.html class="link">Schedule From Project Start & Constraints</div><div data-folder=02_extensions id=20_backwards_scheduling.html class="link">Schedule From Project End</div><div data-folder=02_extensions id=21_overlay.html class="link">Gantt chart with overlay and zoom</div><div data-folder=02_extensions id=22_tooltip_api.html class="link">Custom Tooltips</div><div data-folder=02_extensions id=23_click_drag_splittask.html class="link">Create split tasks by Drag and Drop</div><div data-folder=02_extensions id=24_click_drag.html class="link">Create new tasks by Drag and Drop</div><div data-folder=02_extensions id=25_click_drag_select_by_drag.html class="link">Select multiple tasks by Drag and Drop</div><div data-folder=02_extensions id=26_full_screen_with_additional_elements.html class="link">Full Screen with additional elements</div><div data-folder=02_extensions id=27_drag_timeline.html class="link">Drag timeline</div><div data-folder=02_extensions id=28_row_resize.html class="link">Resizable rows in grid</div><div data-folder=02_extensions id=28_tasks_grouping_save_tree_structure.html class="link">Save tree structure when grouping tasks</div>
				</div>

				<div class="toggler-icon">
					<svg class="arrow-icon" viewBox="0 0 8 12">
						<use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#arrow" class="arrow-icon line"></use>
					</svg>
				</div>
			</div>
			<div>
				<label for="03_scales">Scales</label>
				<input type="checkbox" id="03_scales" class="toggler" />
				<div class="toggler-content">
					<div data-folder=03_scales id=01_multiple_scales.html class="link">Multiple scales</div><div data-folder=03_scales id=02_month_days.html class="link">Month view</div><div data-folder=03_scales id=03_full_year.html class="link">Year scale</div><div data-folder=03_scales id=04_days.html class="link">Day hours</div><div data-folder=03_scales id=05_dynamic_scales.html class="link">Dynamic scales</div><div data-folder=03_scales id=06_custom_scales.html class="link">Custom scales</div><div data-folder=03_scales id=07_minutes_scale.html class="link">Minutes timeline</div><div data-folder=03_scales id=08_scale_autoconfig.html class="link">Auto resize scale</div><div data-folder=03_scales id=09_skip_weekends.html class="link">Not render weekends on the scale</div><div data-folder=03_scales id=10_working_hours.html class="link">Show working hours</div><div data-folder=03_scales id=11_select_column.html class="link">Selecting columns</div><div data-folder=03_scales id=12_year_quarters.html class="link">Year quarters scale</div><div data-folder=03_scales id=13_zoom_to_fit.html class="link">Zoom To Fit</div><div data-folder=03_scales id=14_scale_zoom_by_wheelmouse.html class="link">Mouse wheel zoom</div>
				</div>

				<div class="toggler-icon">
					<svg class="arrow-icon" viewBox="0 0 8 12">
						<use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#arrow" class="arrow-icon line"></use>
					</svg>
				</div>
			</div>
			<div>
				<label for="04_customization">Customization</label>
				<input type="checkbox" id="04_customization" class="toggler" />
				<div class="toggler-content">
					<div data-folder=04_customization id=01_outer_content.html class="link">Define side content</div><div data-folder=04_customization id=02_custom_tree.html class="link">Custom tree formatting</div><div data-folder=04_customization id=03_link_styles.html class="link">Link styles</div><div data-folder=04_customization id=04_task_styles.html class="link">Task styles</div><div data-folder=04_customization id=05_tree_template.html class="link">Template for tree nodes</div><div data-folder=04_customization id=06_highlight_weekend.html class="link">Highlighting weekends</div><div data-folder=04_customization id=07_progress_text.html class="link">Text in the Progress bar</div><div data-folder=04_customization id=08_templates.html class="link">Styling task bars with events</div><div data-folder=04_customization id=09_html_content.html class="link">Custom html content</div><div data-folder=04_customization id=10_context_menu.html class="link">Simple context menu</div><div data-folder=04_customization id=11_split_task.html class="link">Split task</div><div data-folder=04_customization id=12_custom_task_type.html class="link">Custom task type</div><div data-folder=04_customization id=13_autosize_container.html class="link">Expand container</div><div data-folder=04_customization id=14_deadline.html class="link">Displaying deadlines</div><div data-folder=04_customization id=15_baselines.html class="link">Display project baseline</div><div data-folder=04_customization id=16_inline_task_colors.html class="link">Specify inline colors for Tasks and Links</div><div data-folder=04_customization id=17_classic_gantt_look.html class="link">Classic Look</div><div data-folder=04_customization id=18_subtasks_displaying.html class="link">Display subtasks when the Project is closed</div><div data-folder=04_customization id=19_task_type.html class="link">Create summary tasks dynamically</div><div data-folder=04_customization id=20_message_types.html class="link">Gantt message types</div><div data-folder=04_customization id=21_open_split_task.html class="link">Expand and collapse split tasks</div><div data-folder=04_customization id=22_draw_links_as_svg_images.html class="link">Draw links as SVG images</div><div data-folder=04_customization id=23_integration_with_suite.html class="link">Integration with DHTMLX Suite</div><div data-folder=04_customization id=24_timeline_cells_custom_content.html class="link">Custom content inside the timeline cells</div>
				</div>

				<div class="toggler-icon">
					<svg class="arrow-icon" viewBox="0 0 8 12">
						<use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#arrow" class="arrow-icon line"></use>
					</svg>
				</div>
			</div>
			<div>
				<label for="05_lightbox">Lightbox</label>
				<input type="checkbox" id="05_lightbox" class="toggler" />
				<div class="toggler-content">
					<div data-folder=05_lightbox id=01_lightbox_customization.html class="link">Lightbox customization</div><div data-folder=05_lightbox id=02_checkbox.html class="link">Checkbox control</div><div data-folder=05_lightbox id=02_progress_lightbox.html class="link">Progress lightbox</div><div data-folder=05_lightbox id=02_radio.html class="link">Radio control</div><div data-folder=05_lightbox id=03_validation.html class="link">Validate lightbox values</div><div data-folder=05_lightbox id=04_custom_editor.html class="link">Custom control in the lightbox</div><div data-folder=05_lightbox id=05_template.html class="link">Template control</div><div data-folder=05_lightbox id=06_custom_button.html class="link">Custom button in the lightbox</div><div data-folder=05_lightbox id=07_time.html class="link">Time control</div><div data-folder=05_lightbox id=08_parent_selector.html class="link">Parent selector</div><div data-folder=05_lightbox id=09_years_selector_range.html class="link">Specify year selector range</div><div data-folder=05_lightbox id=10_progress_slider.html class="link">Slider control in lightbox</div><div data-folder=05_lightbox id=11_datepicker_for_lightbox.html class="link">Datepicker in lightbox</div><div data-folder=05_lightbox id=12_select.html class="link">Select control</div><div data-folder=05_lightbox id=13_resources.html class="link">Resources control</div><div data-folder=05_lightbox id=14_jquery_multiselect.html class="link">3rd party multiselect control</div><div data-folder=05_lightbox id=15_readonly_lightbox.html class="link">Readonly lightbox</div>
				</div>

				<div class="toggler-icon">
					<svg class="arrow-icon" viewBox="0 0 8 12">
						<use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#arrow" class="arrow-icon line"></use>
					</svg>
				</div>
			</div>
			<div>
				<label for="06_skins">Skins</label>
				<input type="checkbox" id="06_skins" class="toggler" />
				<div class="toggler-content">
					<div data-folder=06_skins id=01_default.html class="link">Default skin</div><div data-folder=06_skins id=02_default_lightbox.html class="link">Task edit form</div><div data-folder=06_skins id=03_skyblue.html class="link">'Skyblue' skin</div><div data-folder=06_skins id=04_meadow.html class="link">'Meadow' skin</div><div data-folder=06_skins id=05_broadway.html class="link">'Broadway' skin</div><div data-folder=06_skins id=06_dynamic_skin.html class="link">Change skin dynamically</div><div data-folder=06_skins id=07_high_contrast_black.html class="link">High contrast theme - Black</div><div data-folder=06_skins id=08_high_contrast_white.html class="link">High contrast theme - White</div><div data-folder=06_skins id=09_material.html class="link">Material theme</div>
				</div>

				<div class="toggler-icon">
					<svg class="arrow-icon" viewBox="0 0 8 12">
						<use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#arrow" class="arrow-icon line"></use>
					</svg>
				</div>
			</div>
			<div>
				<label for="07_grid">Grid</label>
				<input type="checkbox" id="07_grid" class="toggler" />
				<div class="toggler-content">
					<div data-folder=07_grid id=01_builtin_sorting.html class="link">Built-in sorting</div><div data-folder=07_grid id=02_branch_ordering.html class="link">Branch ordering</div><div data-folder=07_grid id=03_filtering.html class="link">Filtering</div><div data-folder=07_grid id=04_custom_sorting.html class="link">Custom sorting function</div><div data-folder=07_grid id=05_sort_api.html class="link">Using sorting methods</div><div data-folder=07_grid id=06_without_grid.html class="link">Render Gantt chart without grid</div><div data-folder=07_grid id=07_custom_buttons.html class="link">Custom Buttons in a Grid</div><div data-folder=07_grid id=08_drag_between_levels.html class="link">Drag and drop rows in Grid</div><div data-folder=07_grid id=09_wbs_column.html class="link">Show Task WBS Codes (Outline Numbers)</div><div data-folder=07_grid id=10_scrollable_grid.html class="link">Horizontal scroll inside Grid</div><div data-folder=07_grid id=11_inline_edit_basic.html class="link">Inline editing</div><div data-folder=07_grid id=12_inline_edit_key_nav.html class="link">Inline editing - keyboard navigation mode</div><div data-folder=07_grid id=13_custom_mapping.html class="link">Inline editing - Custom keyboard mapping</div><div data-folder=07_grid id=14_branch_ordering_highlight.html class="link">Branch ordering - highlighting mode</div>
				</div>

				<div class="toggler-icon">
					<svg class="arrow-icon" viewBox="0 0 8 12">
						<use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#arrow" class="arrow-icon line"></use>
					</svg>
				</div>
			</div>
			<div>
				<label for="08_api">Api</label>
				<input type="checkbox" id="08_api" class="toggler" />
				<div class="toggler-content">
					<div data-folder=08_api id=01_dnd_events.html class="link">D'n'D Events</div><div data-folder=08_api id=02_constraints.html class="link">Limit drag and drop dates</div><div data-folder=08_api id=03_validation.html class="link">Validation</div><div data-folder=08_api id=04_limit_project.html class="link">Fixed project dates</div><div data-folder=08_api id=05_limit_drag_dates.html class="link">Denying dragging tasks out of specific dates</div><div data-folder=08_api id=06_export.html class="link">Export data from Gantt</div><div data-folder=08_api id=07_export_styles.html class="link">Task styles</div><div data-folder=08_api id=08_export_other.html class="link">Export data : MS Project, PrimaveraP6, Excel &amp; iCal</div><div data-folder=08_api id=09_export_store.html class="link">Export data: store online</div><div data-folder=08_api id=10_performance_tweaks.html class="link">Performance tweaks</div><div data-folder=08_api id=11_project_structure.html class="link">Predefined Project Structure</div><div data-folder=08_api id=12_fit_task_text.html class="link">Dynamically move text content of a task</div><div data-folder=08_api id=13_highlight_drag_position.html class="link">Highlight Task Position on Drag</div><div data-folder=08_api id=14_critical_tasks_expanding.html class="link">Critical tasks expanding</div><div data-folder=08_api id=15_show_dates_on_drag.html class="link">Show task dates on Drag and Drop</div><div data-folder=08_api id=16_dynamic_progress.html class="link">Calculate Progress of Summary Tasks</div><div data-folder=08_api id=17_show_task_slack.html class="link">Show Slack time</div><div data-folder=08_api id=18_load_from_mpp.html class="link">Import MS Project file</div><div data-folder=08_api id=18_load_from_primaverap6.html class="link">Import Primavera P6 file</div><div data-folder=08_api id=19_draggable_projects.html class="link">Draggable projects</div><div data-folder=08_api id=21_load_from_excel.html class="link">Import Excel file</div><div data-folder=08_api id=22_data_processor.html class="link">Custom data api - using local storage</div><div data-folder=08_api id=23_empty_gantt_with_placeholder_views.html class="link">Empty Gantt with placeholder grid and timeline</div><div data-folder=08_api id=24_empty_state_screen.html class="link">Show empty state screen</div>
				</div>

				<div class="toggler-icon">
					<svg class="arrow-icon" viewBox="0 0 8 12">
						<use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#arrow" class="arrow-icon line"></use>
					</svg>
				</div>
			</div>
			<div>
				<label for="09_worktime">Worktime</label>
				<input type="checkbox" id="09_worktime" class="toggler" />
				<div class="toggler-content">
					<div data-folder=09_worktime id=01_working_hours_per_day.html class="link">Calculate working hours</div><div data-folder=09_worktime id=02_working_days.html class="link">Working days as duration</div><div data-folder=09_worktime id=03_exclude_holidays.html class="link">Exclude holidays</div><div data-folder=09_worktime id=04_custom_workday_duration.html class="link">Custom working days and time</div><div data-folder=09_worktime id=05_adjust_to_worktime.html class="link">Correct task position on drag</div><div data-folder=09_worktime id=06_task_calendars.html class="link">Task level calendars</div><div data-folder=09_worktime id=07_resource_calendars.html class="link">Resource level calendars</div><div data-folder=09_worktime id=08_project_calendars.html class="link">Project level calendars</div><div data-folder=09_worktime id=09_decimal_durations.html class="link">Decimal durations for tasks</div><div data-folder=09_worktime id=10_merge_calendars.html class="link">Merge work Calendars of different resources</div><div data-folder=09_worktime id=11_working_hours_with_minutes.html class="link">Specify work time with minute precision (8:30-17:30)</div><div data-folder=09_worktime id=12_calendar_ranges.html class="link">Different worktimes for different time periods</div>
				</div>

				<div class="toggler-icon">
					<svg class="arrow-icon" viewBox="0 0 8 12">
						<use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#arrow" class="arrow-icon line"></use>
					</svg>
				</div>
			</div>
			<div>
				<label for="10_layout">Layout</label>
				<input type="checkbox" id="10_layout" class="toggler" />
				<div class="toggler-content">
					<div data-folder=10_layout id=01_rightside_columns.html class="link">Grid columns rightside of gantt</div><div data-folder=10_layout id=02_resource_panel.html class="link">Gantt chart with resource panel</div><div data-folder=10_layout id=03_scale_at_bottom.html class="link">Time scale at the bottom of gantt</div><div data-folder=10_layout id=04_rtl.html class="link">Right to left gantt</div>
				</div>

				<div class="toggler-icon">
					<svg class="arrow-icon" viewBox="0 0 8 12">
						<use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#arrow" class="arrow-icon line"></use>
					</svg>
				</div>
			</div>
			<div>
				<label for="11_resources">Resources</label>
				<input type="checkbox" id="11_resources" class="toggler" />
				<div class="toggler-content">
					<div data-folder=11_resources id=01_assigning_resources.html class="link">Assigning owners to tasks</div><div data-folder=11_resources id=02_resource_calendars.html class="link">Resource calendars</div><div data-folder=11_resources id=03_break_down_by_resource.html class="link">Break down by resources</div><div data-folder=11_resources id=04_resource_usage_diagram.html class="link">Resource load diagram</div><div data-folder=11_resources id=05_resource_usage_templates.html class="link">Templates of the Resource diagram</div><div data-folder=11_resources id=06_assign_multiple_owners.html class="link">Assign multiple owners to a task</div><div data-folder=11_resources id=07_assign_multiple_resources.html class="link">Assign multiple resources</div><div data-folder=11_resources id=08_resource_usage_groups.html class="link">Group by multiple resources</div><div data-folder=11_resources id=09_resource_histogram.html class="link">Resource histogram</div><div data-folder=11_resources id=10_resource_histogram_workload_percents.html class="link">Assign workload in percents</div><div data-folder=11_resources id=11_resource_histogram_display_tasks.html class="link">Show all assigned tasks in the resource panel</div><div data-folder=11_resources id=12_work_and_material_resources.html class="link">Work and material resources</div><div data-folder=11_resources id=13_resource_assignments_for_days.html class="link">Assign resource values to specific days</div><div data-folder=11_resources id=14_resource_load_with_zoom.html class="link">Resource load diagram with different zoom levels</div>
				</div>

				<div class="toggler-icon">
					<svg class="arrow-icon" viewBox="0 0 8 12">
						<use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#arrow" class="arrow-icon line"></use>
					</svg>
				</div>
			</div>
			<div>
				<label for="20_multiple">Multiple</label>
				<input type="checkbox" id="20_multiple" class="toggler" />
				<div class="toggler-content">
					<div data-folder=20_multiple id=01_basic.html class="link">Multiple Gantts on the page</div><div data-folder=20_multiple id=03_layout.html class="link">Gantts in dhtmlxLayout cells (dhtmlxSuite v5.x)</div><div data-folder=20_multiple id=05_jquery.html class="link">jQuery initialization</div><div data-folder=20_multiple id=06_export.html class="link">Export data from Gantt</div><div data-folder=20_multiple id=07_keyboard_navigation.html class="link">Keyboard navigation, multiple gantts</div><div data-folder=20_multiple id=08_constructor_destructor.html class="link">Using gantt constructor and destructor</div>
				</div>

				<div class="toggler-icon">
					<svg class="arrow-icon" viewBox="0 0 8 12">
						<use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#arrow" class="arrow-icon line"></use>
					</svg>
				</div>
			</div>
					</div>
					<div class="more-link-container">
						<a href="https://snippet.dhtmlx.com/40tsh9uz?text=gantt." title="" class="more-link" target="_blank">
							<span class="more-link__icon">
								<svg viewBox="0 0 12 13" xmlns="http://www.w3.org/2000/svg" class="more-link__v">
									<path d="M0 11.1583L8.825 2.33329H3.33333V0.666626H11.6667V8.99996H10V3.50829L1.175 12.3333L0 11.1583Z" class="more-link__v line"></path>
								</svg>
							</span>
		
							More in Snippet Tool
						</a>
					</div>
				</div>
			</div>
		</div>

		<div class="page-container">
			<div class="demo-container">
				<div class="demo-container__nav">
					<span class="demo-container__nav-btn desktop show_demo" onclick="toggle_demo('demo')">Demo</span>

					<span class="demo-container__nav-btn desktop show_code" onclick="toggle_demo('code')">Source code</span>

					<span class="demo-container__nav-btn desktop show_api" onclick="toggle_demo('api')">API reference</span>

					<span class="demo-container__nav-btn desktop share" onclick="shareSample()">Share</span>

					<div class="demo-container__nav-dropdown mobile">
						<div class="demo-container__nav-dropdown-chosen" onclick="navDropdown();">
							<div class="demo-container__nav-dropdown-chosen-label" id="nav-dropdown-chosen">Demo</div>

							<div class="demo-container__nav-dropdown-arrow">
								<svg class="arrow-icon" viewBox="0 0 8 12">
									<use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#arrow" class="arrow-icon line"></use>
								</svg>
							</div>
						</div>

						<div class="demo-container__nav-dropdown-list" id="nav-dropdown-list">
							<div class="demo-container__nav-dropdown-row">
								<span class="demo-container__nav-btn show_demo" onclick="toggle_demo('demo'); toggle_dropdown(event);">Demo</span>
							</div>

							<div class="demo-container__nav-dropdown-row">
								<span class="demo-container__nav-btn show_code" onclick="toggle_demo('code'); toggle_dropdown(event);">Source code</span>
							</div>

							<div class="demo-container__nav-dropdown-row">
								<span class="demo-container__nav-btn show_api" onclick="toggle_demo('api'); toggle_dropdown(event);">API reference</span>
							</div>

							<div class="demo-container__nav-dropdown-row">
								<span class="demo-container__nav-btn share" onclick="shareSample(); navDropdown();">Share</span>
							</div>
						</div>
					</div>

					<a href="01_initialization/01_basic_init.html" title="Open the sample in a separate tab" id="current_sample" class="demo-container__nav-btn show_sample" target="_blank">Open sample</a>
				</div>

				<div class="demo-body demo">
					<iframe id="x6" class="preview_frame" frameborder="0"
							src="">
					</iframe>
					<div id="source_code">
					</div>
					<div class="api-container" id=api_reference>
						<div class="api-container__inside">
							<h3>Google suggestions
								<div class=suggestions>
								</div>
							</h3>
							<h3>Files
								<div class=files>
								</div>
							</h3>
							<h3>Methods
								<div class=methods>
								</div>
							</h3>
							<h3>Properties
								<div class=properties>
								</div>
							</h3>
							<h3>Events
								<div class=events>
								</div>
							</h3>
							<h3>Templates
								<div class=templates>
								</div>
							</h3>
							<h3>Other
								<div class=other>
								</div>
							</h3>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<footer class="footer">
		<div class="dhx-container">
			<div class="footer-nav">
				<div class="footer-nav__col">
					<div class="footer-nav__title">
						<span class="footer-title-link">Development center</span>
					</div>

					<div class="footer-nav__row">
						<a href="//dhtmlx.com/docs/download.shtml" title="" class="footer-link" target="_blank">Download</a>
					</div>

					<div class="footer-nav__row">
						<a href="//dhtmlx.com/docs/products/docsExplorer/samples.shtml" title="" class="footer-link" target="_blank">Code Examples</a>
					</div>

					<div class="footer-nav__row">
						<a href="//dhtmlx.com/docs/products/demoApps/" title="" class="footer-link" target="_blank">Demos</a>
					</div>

					<div class="footer-nav__row">
						<a href="//forum.dhtmlx.com/" title="" class="footer-link" target="_blank">Forum</a>
					</div>

					<div class="footer-nav__row">
						<a href="//dhtmlx.com/docs/technical-support.shtml" title="" class="footer-link" target="_blank">Technical Support</a>
					</div>
				</div>

				<div class="footer-nav__col">
					<div class="footer-nav__title">
						<span class="footer-title-link">Community</span>
					</div>

					<div class="footer-nav__row">
						<a href="//github.com/DHTMLX" title="" class="footer-link" target="_blank">GitHub</a>
					</div>

					<div class="footer-nav__row">
						<a href="//www.youtube.com/user/dhtmlx" title="" class="footer-link" target="_blank">YouTube</a>
					</div>

					<div class="footer-nav__row">
						<a href="//www.facebook.com/dhtmlx" title="" class="footer-link" target="_blank">Facebook</a>
					</div>

					<div class="footer-nav__row">
						<a href="//twitter.com/dhtmlx" title="" class="footer-link" target="_blank">Twitter</a>
					</div>

					<div class="footer-nav__row">
						<a href="//www.linkedin.com/groups/3345009/" title="" class="footer-link" target="_blank">LinkedIn</a>
					</div>
				</div>

				<div class="footer-nav__col">
					<div class="footer-nav__title">
						<span class="footer-title-link">Company</span>
					</div>

					<div class="footer-nav__row">
						<a href="//dhtmlx.com/docs/company.shtml" title="" class="footer-link" target="_blank">About us</a>
					</div>

					<div class="footer-nav__row">
						<a href="//dhtmlx.com/docs/contact.shtml" title="" class="footer-link" target="_blank">Contact us</a>
					</div>

					<div class="footer-nav__row">
						<a href="//dhtmlx.com/docs/products/licenses.shtml" title="" class="footer-link" target="_blank">Licensing</a>
					</div>

					<div class="footer-nav__row">
						<a href="//dhtmlx.com/blog/" title="" class="footer-link" target="_blank">Blog</a>
					</div>

					<div class="footer-nav__row">
						<a href="//dhtmlx.com/clients/" title="" class="footer-link" target="_blank">Client's Area</a>
					</div>
				</div>

				<div class="footer-nav__col">
					<div class="footer-nav__title">
						<span class="footer-title-link">DHTMLX Components</span>
					</div>

					<div class="footer-nav__sub-cols">
						<div class="footer-nav__sub-col">
							<div class="footer-nav__row">
								<a href="//docs.dhtmlx.com/gantt/index.html" title="" class="footer-link">Gantt</a>
							</div>

							<div class="footer-nav__row">
								<a href="//docs.dhtmlx.com/scheduler/index.html" title="" class="footer-link">Scheduler</a>
							</div>

							<div class="footer-nav__row">
								<a href="//docs.dhtmlx.com/diagram/index.html" title="" class="footer-link">Diagram</a>
							</div>

							<div class="footer-nav__row">
								<a href="//docs.dhtmlx.com/suite/" title="" class="footer-link">Suite 7</a>
							</div>

							<div class="footer-nav__row">
								<a href="//docs.dhtmlx.com/spreadsheet/index.html" title="" class="footer-link">Spreadsheet</a>
							</div>
						</div>

						<div class="footer-nav__sub-col">
							<div class="footer-nav__row">
								<a href="//docs.dhtmlx.com/pivot/index.html" title="" class="footer-link">Pivot</a>
							</div>

							<div class="footer-nav__row">
								<a href="//docs.dhtmlx.com/richtext/index.html" title="" class="footer-link">RichText</a>
							</div>

							<div class="footer-nav__row">
								<a href="//docs.dhtmlx.com/vault/index.html" title="" class="footer-link">Vault</a>
							</div>

							<div class="footer-nav__row">
								<a href="//docs.dhtmlx.com/suite5.html" title="" class="footer-link">Suite 5</a>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="copyrights">Copyright © 2023 XB Software Ltd.</div>
		</div>
	</footer>
</body>
<script>
	document.addEventListener("DOMContentLoaded", function(event) {
		loadSampleFromParams()
  });


</script>
</html>
