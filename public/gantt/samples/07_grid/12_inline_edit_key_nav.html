<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Inline editing - keyboard navigation mode</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.9"></script>
	<link rel="stylesheet" href="../../codebase/skins/dhtmlxgantt_terrace.css?v=8.0.9">
	<script src="../common/testdata.js?v=8.0.9"></script>
	<style>
		html, body {
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}

		.gantt_row_project{
			font-weight: bold;
		}

		.gantt-info ul{
			line-height: 150%;
		}

	</style>
</head>

<body>
<div id="gantt_here" style='width:100%; height:100%;'></div>
<script>
	gantt.plugins({
		multiselect: true,
		auto_scheduling: true,
		keyboard_navigation: true
	});
	gantt.message({
		text: "<ul>"+
		"<li>Click <b>Create a new task</b> and start adding a project</li>"+
		"<li>Use <b>arrows</b> to move focus between cells</li>"+
		"<li>Press <b>Enter</b> or <b>Space</b> to open an editor or just start typing</li>"+
		"<li><b>shift+right/shift+left</b> to indent/outdent tasks</li>"+
		"<li>Create relations by entering task codes into <b>Predecessors</b> column</li>"+
		"<li>Check out <a target='_blank' href='http://recordit.co/84N9h6GE2i'>a video demo</a></li>"+
	"</ul>",
		expire: 30*1000
	});

	gantt.config.keyboard_navigation_cells = true;
	gantt.config.auto_scheduling = true;
	gantt.config.auto_scheduling_strict = true;
	gantt.config.row_height = 23;
	gantt.config.fit_tasks = true;
	gantt.config.show_unscheduled = true;
	gantt.config.placeholder_task = true;
	gantt.config.auto_types = true;

	gantt.serverList("priority", [
		{key: "1", label: "Low"},
		{key: "2", label: "Normal"},
		{key: "3", label: "High"}
	]);

	var formatter = gantt.ext.formatters.durationFormatter({
		enter: "day", 
		store: "day", 
		format: "auto"
	});
	var linksFormatter = gantt.ext.formatters.linkFormatter({durationFormatter: formatter});


	var editors = {
		text: {type: "text", map_to: "text"},
		start_date: {type: "date", map_to: "start_date", min: new Date(2023, 0, 1), max: new Date(2024, 0, 1)},
		end_date: {type: "date", map_to: "end_date", min: new Date(2023, 0, 1), max: new Date(2024, 0, 1)},
		duration: {type: "duration", map_to: "duration", min:0, max: 100, formatter: formatter},
		priority: {type: "select", map_to: "priority", options:gantt.serverList("priority")},
		predecessors: {type: "predecessor", map_to: "auto", formatter: linksFormatter}
	};

	function priorityLabel(task){
		var value = task.priority;
		var list = gantt.serverList("priority");
		for(var i = 0; i < list.length; i++){
			if(list[i].key == value){
				return list[i].label;
			}
		}
		return "";
	}

	gantt.config.columns = [
		{name: "wbs", label: "#", width: 60, align: "center", template: gantt.getWBSCode},
		{name: "text", label: "Name", tree: true, width: 200, editor: editors.text, resize: true},
		{name: "duration", label: "Duration",width:60, align: "center", editor: editors.duration, resize: true, template: function(task){
			return formatter.format(task.duration);
		}},
		{name: "start_date", label: "Start", width:80, align: "center", editor: editors.start_date, resize: true},
		{name: "end_date", label: "Finish", width:80, align: "center", editor: editors.end_date, resize: true},
		{name: "priority", label: "Priority", width:80, align: "center", editor: editors.priority, template: priorityLabel, resize: true},
		{name: "predecessors", label: "Predecessors",width:80, align: "left", editor: editors.predecessors, resize: true, template: function(task){
				var links = task.$target;
				var labels = [];
				for(var i = 0; i < links.length; i++){
					var link = gantt.getLink(links[i]);
					labels.push(linksFormatter.format(link));
				}
				return labels.join(", ")
			}},
		{name:"add"}
	];

	gantt.attachEvent("onTaskCreated", function(task){
		task.priority = "1";
		if(task.type == gantt.config.types.placeholder){
			task.text = "Create a new task";
		}
		return true;
	});

	gantt.ext.inlineEditors.attachEvent("onSave", function(state){
		var col = state.columnName;
		if(gantt.autoSchedule && (col == "start_date" || col == "end_date" || col == "duration")){
			gantt.autoSchedule();
		}
	});

	gantt.init("gantt_here");
	gantt.parse({data:[], links:[]});
</script>
</body>