<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Built-in sorting</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.9"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.9">
	<style>
		html, body{
			padding:0;
			margin: 0;
		}
	</style>
</head>

<body>
<div id="gantt_here" style='height:100vh;'></div>

<script>
	gantt.config.sort = true;

	gantt.init("gantt_here");
	gantt.parse({
		data: [
			{id: 1, text: "Project #1", start_date: "01-04-2023", duration: 8, progress: 0.6, open: true},
			{id: 2, text: "Task #1", start_date: "03-04-2023", duration: 5, parent: 1, progress: 1, open: true},
			{id: 3, text: "Task #2", start_date: "02-04-2023", duration: 7, parent: 1, progress: 0.5, open: true},
			{id: 4, text: "Task #2.1", start_date: "03-04-2023", duration: 2, parent: 3, progress: 1, open: true},
			{id: 5, text: "Task #2.2", start_date: "04-04-2023", duration: 3, parent: 3, progress: 0.8, open: true},
			{id: 6, text: "Task #2.3", start_date: "05-04-2023", duration: 4, parent: 3, progress: 0.2, open: true}
		],
		links: [
			{id: 1, source: 1, target: 2, type: "1"},
			{id: 2, source: 1, target: 3, type: "1"},
			{id: 3, source: 3, target: 4, type: "1"},
			{id: 4, source: 4, target: 5, type: "0"},
			{id: 5, source: 5, target: 6, type: "0"}
		]
	});
</script>
</body>