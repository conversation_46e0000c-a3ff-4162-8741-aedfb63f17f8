<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Render Gantt chart without grid</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.9"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.9">
	<script src="../common/data.js?v=8.0.9"></script>
</head>

<body>
<style>
	html, body {
		padding: 0px;
		margin: 0px;
		height: 100%;
	}
</style>
<div id="gantt_here" style='width:100%; height:100%;'></div>
<script>
	gantt.config.date_format = "%Y-%m-%d %H:%i:%s";
	gantt.config.grid_width = 0;
	gantt.init("gantt_here");
	gantt.parse(taskData);
	
</script>
</body>