<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Template for tree nodes</title>

	<script src="../../codebase/dhtmlxgantt.js?v=8.0.9"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.9">

	<script src="../common/testdata.js?v=8.0.9"></script>
	<style>
		.important {
			color: red;
		}
		html, body{
			padding:0;
			margin: 0;
		}
	</style>
</head>

<body>
<div id="gantt_here" style='height:100vh;'></div>


<script>
	gantt.config.columns = [
		{name: "text", label: "Task name", tree: true, width: 230, template: function (task) {
			if (task.priority == 1)
				return "<div class='important'>" + task.text + " (" + task.users + ") </div>";
			return task.text + " (" + task.users + ")";
		}},
		{name: "start_date", label: "Start time", align: "center"},
		{name: "duration", label: "Duration", align: "center"}
	];
	gantt.init("gantt_here");
	gantt.parse(users_data);

</script>
</body>