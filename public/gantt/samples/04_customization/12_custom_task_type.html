<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Custom task type</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.9"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.9">

	<script src="../common/testdata.js?v=8.0.9"></script>
	<style>
		html, body {
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}

		.meeting_task {
			border: 2px solid #BFC518;
			color: #6ba8e3;
			background: #F2F67E;
		}

		.meeting_task .gantt_task_progress {
			background: #D9DF29;
		}

	</style>
</head>

<body>
<div id="gantt_here" style='width:100%; height:100%;'></div>


<script>
	gantt.config.types["meeting"] = "type_id";
	gantt.locale.labels["type_meeting"] = "Meeting";

	//sections for tasks with 'meeting' type

	gantt.locale.labels.section_title = "Subject";
	gantt.locale.labels.section_details = "Details";
	gantt.config.lightbox["meeting_sections"] = [
		{name: "title", height: 20, map_to: "text", type: "textarea", focus: true},
		{name: "details", height: 70, map_to: "details", type: "textarea", focus: true},
		{name: "type", type: "typeselect", map_to: "type"},
		{name: "time", type: "time", map_to: "auto"}
	];

	//sections for regular lightbox
	gantt.config.lightbox.sections = [
		{name: "description", height: 70, map_to: "text", type: "textarea", focus: true},
		{name: "type", type: "typeselect", map_to: "type"},
		{name: "time", type: "duration", map_to: "auto"}
	];
	gantt.templates.task_class = function (start, end, task) {
		if (task.type == gantt.config.types.meeting) {
			return "meeting_task";
		}
		return "";
	};

	gantt.templates.task_text = function (start, end, task) {
		if (task.type == gantt.config.types.meeting) {
			return "Meeting: <b>" + task.text + "</b>";
		}
		return task.text;
	};


	gantt.templates.rightside_text = function (start, end, task) {
		if (task.type == gantt.config.types.milestone) {
			return task.text;
		}
		return "";
	};

	gantt.init("gantt_here");

	var demo_tasks = {
		data: [
			{id: 11, text: "Project #1", type: gantt.config.types.project, progress: 0.6, open: true},
			{id: 12, text: "Task #1", start_date: "03-04-2023", duration: "5", parent: "11", progress: 1, open: true},
			{id: 13, text: "Task #2", start_date: "03-04-2023", type: gantt.config.types.project, parent: "11", progress: 0.5, open: true},
			{id: 14, text: "Task #3", start_date: "02-04-2023", type: gantt.config.types.meeting, duration: "6", parent: "11", progress: 0.8, open: true},
			{id: 15, text: "Task #4", type: gantt.config.types.project, parent: "11", progress: 0.2, open: true},
			{id: 16, text: "Final milestone", start_date: "15-04-2023", type: gantt.config.types.milestone, parent: "11", progress: 0, open: true},
			{id: 17, text: "Task #2.1", start_date: "03-04-2023", duration: "2", parent: "13", progress: 1, open: true},
			{id: 18, text: "Task #2.2", start_date: "06-04-2023", duration: "3", parent: "13", progress: 0.8, open: true},
			{id: 19, text: "Task #2.3", start_date: "10-04-2023", duration: "4", parent: "13", progress: 0.2, open: true},
			{id: 20, text: "Task #2.4", start_date: "10-04-2023", duration: "4", parent: "13", progress: 0, open: true},
			{id: 21, text: "Task #4.1", start_date: "03-04-2023", duration: "4", parent: "15", progress: 0.5, open: true},
			{id: 22, text: "Task #4.2", start_date: "03-04-2023", duration: "4", parent: "15", progress: 0.1, open: true},
			{id: 23, text: "Mediate milestone", start_date: "14-04-2023", type: gantt.config.types.milestone, parent: "15", progress: 0, open: true}
		],
		links: [
			{id: "10", source: "11", target: "12", type: "1"},
			{id: "11", source: "11", target: "13", type: "1"},
			{id: "12", source: "11", target: "14", type: "1"},
			{id: "13", source: "11", target: "15", type: "1"},
			{id: "14", source: "23", target: "16", type: "0"},
			{id: "15", source: "13", target: "17", type: "1"},
			{id: "16", source: "17", target: "18", type: "0"},
			{id: "17", source: "18", target: "19", type: "0"},
			{id: "18", source: "19", target: "20", type: "0"},
			{id: "19", source: "15", target: "21", type: "2"},
			{id: "20", source: "15", target: "22", type: "2"},
			{id: "21", source: "15", target: "23", type: "0"}
		]
	};
	gantt.parse(demo_tasks);

</script>
</body>