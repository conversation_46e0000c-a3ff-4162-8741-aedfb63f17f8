<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Define side content</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.9"></script>
	<link rel="stylesheet" href="../../codebase/skins/dhtmlxgantt_meadow.css?v=8.0.9">

	<script src="../common/testdata.js?v=8.0.9"></script>
	<style>
		html, body {
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}
	</style>
</head>

<body>

<div id="gantt_here" style='width:100%; height:100%'></div>
<script>
	gantt.templates.rightside_text = function (start, end, task) {
		return "ID: #" + task.id;
	};

	gantt.templates.leftside_text = function (start, end, task) {
		return task.duration + " days";
	};

	gantt.init("gantt_here");
	gantt.parse(demo_tasks);
</script>
</body>