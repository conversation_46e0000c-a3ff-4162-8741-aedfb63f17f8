<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Assign multiple resources</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.9"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.9">

	<script src="../common/resource_project_assignments.js?v=8.0.9"></script>

	<style>
		html, body {
			padding: 0px;
			margin: 0px;
			height: 100%;
		}

		#gantt_here {
			width:100%;
			height:100%;
		}

		.gantt_grid_scale .gantt_grid_head_cell,
		.gantt_task .gantt_task_scale .gantt_scale_cell {
			font-weight: bold;
			font-size: 14px;
			color: rgba(0, 0, 0, 0.7);
		}

		.resource_marker{
			text-align: center;
		}
		.resource_marker div{
			width: 28px;
			height: 28px;
			line-height: 29px;
			display: inline-block;
			border-radius: 15px;
			color: #FFF;
			margin: 3px;
		}
		.resource_marker.workday_ok div {
			background: #51c185;
		}

		.resource_marker.workday_over div{
			background: #ff8686;
		}



		.owner-label{
			width: 20px;
			height: 20px;
			line-height: 20px;
			font-size: 12px;
			display: inline-block;
			border: 1px solid #cccccc;
			border-radius: 25px;
			background: #e6e6e6;
			color: #6f6f6f;
			margin: 0 3px;
			font-weight: bold;
		}

	</style>
</head>
<body>

<div id="gantt_here"></div>
<script>

	gantt.config.columns = [
		{name: "text", tree: true, width: 200, resize: true},
		{name: "start_date", align: "center", width: 80, resize: true},
		{name: "owner", align: "center", width: 75, label: "Owner", template: function (task) {
			if (task.type == gantt.config.types.project) {
				return "";
			}

			var store = gantt.getDatastore("resource");
			var assignments = task[gantt.config.resource_property];

			if (!assignments || !assignments.length) {
				return "Unassigned";
			}

			if(assignments.length == 1){
				return store.getItem(assignments[0].resource_id).text;
			}

			var result = "";
			assignments.forEach(function(assignment) {
				var owner = store.getItem(assignment.resource_id);
				if (!owner)
					return;
				result += "<div class='owner-label' title='" + owner.text + "'>" + owner.text.substr(0, 1) + "</div>";

			});

			return result;
			}, resize: true
		},
		{name: "duration", width: 60, align: "center"},
		{name: "add", width: 44}
	];

	function getResourceAssignments(resourceId) {
		var assignments;
		var store = gantt.getDatastore(gantt.config.resource_store);
		var resource = store.getItem(resourceId);

		if (resource.$level === 0) {
			assignments = [];
			store.getChildren(resourceId).forEach(function(childId){
				assignments = assignments.concat(gantt.getResourceAssignments(childId));
			});
		} else if (resource.$level === 1) {
			assignments = gantt.getResourceAssignments(resourceId);
		}else{
			assignments = gantt.getResourceAssignments(resource.$resource_id, resource.$task_id);
		}
		return assignments;
	}

	var resourceConfig = {
		columns: [
			{
				name: "name", label: "Name", tree:true, template: function (resource) {
					return resource.text;
				}
			},
			{
				name: "workload", label: "Workload", template: function (resource) {
					var totalDuration = 0;
					if (resource.$level == 2) {
						var assignment = gantt.getResourceAssignments(resource.$resource_id, resource.$task_id)[0];
						totalDuration = resource.duration * assignment.value;
					} else {
						var assignments = getResourceAssignments(resource.id);
						assignments.forEach(function (assignment) {
							var task = gantt.getTask(assignment.task_id);
							totalDuration += Number(assignment.value) * task.duration;
						});
					}

					return (totalDuration || 0) + "h";
				}
			}
		]
	};

	gantt.templates.resource_cell_class = function(start_date, end_date, resource, tasks){
		var css = [];
		css.push("resource_marker");
		if (tasks.length <= 1) {
			css.push("workday_ok");
		} else {
			css.push("workday_over");
		}
		return css.join(" ");
	};

	gantt.templates.resource_cell_value = function(start_date, end_date, resource, tasks){
		var result = 0;
		tasks.forEach(function(item) {
			var assignments = gantt.getResourceAssignments(resource.id, item.id);
			assignments.forEach(function(assignment){
				var task = gantt.getTask(assignment.task_id);
				result += assignment.value * 1;
			});
		});

		if(result % 1){
			result = Math.round(result * 10)/10;
		}
		return "<div>" + result + "</div>";
	};

	gantt.locale.labels.section_resources = "Owners";
	gantt.config.lightbox.sections = [
		{name: "description", height: 38, map_to: "text", type: "textarea", focus: true},
		{
			name: "resources", type: "resources", map_to: "owner", options: gantt.serverList("people"), default_value:8
		},

		{name: "time", type: "duration", map_to: "auto"}
	];

	gantt.config.resource_store = "resource";
	gantt.config.resource_property = "owner";
	gantt.config.order_branch = true;
	gantt.config.open_tree_initially = true;
	gantt.config.layout = {
		css: "gantt_container",
		rows: [
			{
				cols: [
					{view: "grid", group:"grids", scrollY: "scrollVer"},
					{resizer: true, width: 1},
					{view: "timeline", scrollX: "scrollHor", scrollY: "scrollVer"},
					{view: "scrollbar", id: "scrollVer", group:"vertical"}
				],
				gravity:2
			},
			{resizer: true, width: 1},
			{
				config: resourceConfig,
				cols: [
					{view: "resourceGrid", group:"grids", width: 435, scrollY: "resourceVScroll" },
					{resizer: true, width: 1},
					{view: "resourceTimeline", scrollX: "scrollHor", scrollY: "resourceVScroll"},
					{view: "scrollbar", id: "resourceVScroll", group:"vertical"}
				],
				gravity:1
			},
			{view: "scrollbar", id: "scrollHor"}
		]
	};

	var resourcesStore = gantt.createDatastore({
		name: gantt.config.resource_store,
		type: "treeDatastore",
		initItem: function (item) {
			item.parent = item.parent || gantt.config.root_id;
			item[gantt.config.resource_property] = item.parent;
			item.open = true;
			return item;
		}
	});

	gantt.init("gantt_here");

	resourcesStore.attachEvent("onParse", function(){
		var people = [];
		resourcesStore.eachItem(function(res){
			if(!resourcesStore.hasChild(res.id)){
				var copy = gantt.copy(res);
				copy.key = res.id;
				copy.label = res.text;
				people.push(copy);
			}
		});
		gantt.updateCollection("people", people);
	});

	resourcesStore.parse([
		{id: 1, text: "QA", parent:null},
		{id: 2, text: "Development", parent:null},
		{id: 3, text: "Sales", parent:null},
		{id: 4, text: "Other", parent:null},
		{id: 5, text: "Unassigned", parent:4},
		{id: 6, text: "John", parent:1, unit: "hours/day" },
		{id: 7, text: "Mike", parent:2, unit: "hours/day" },
		{id: 8, text: "Anna", parent:2, unit: "hours/day" },
		{id: 9, text: "Bill", parent:3, unit: "hours/day" },
		{id: 10, text: "Floe", parent:3, unit: "hours/day" }
	]);

	gantt.parse(taskData);
</script>
</body>