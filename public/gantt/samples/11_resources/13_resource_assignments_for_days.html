<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Assign resource values to specific days</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.9"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.9">

	<script src="../common/resource_project_assignments.js?v=8.0.9"></script>

	<style>
		html, body {
			padding: 0px;
			margin: 0px;
			height: 100%;
		}

		#gantt_here {
			width:100%;
			height:100%;
		}

		.gantt_grid_scale .gantt_grid_head_cell,
		.gantt_task .gantt_task_scale .gantt_scale_cell {
			font-weight: bold;
			font-size: 14px;
			color: rgba(0, 0, 0, 0.7);
		}

		.resource_marker{
			text-align: center;
		}
		.resource_marker div{
			color: black;
			background: none;
		}

		.resource_marker.resource_cell div{
			font-weight: bold;
			background-color: #d6d6d6;
		}
		.resource_marker.task_cell div {
			cursor: pointer;
		}

		.resource_marker.workday_ok div {
		}


		.resource_marker.workday_over div{
			color: red;
		}


		.owner-label{
			width: 20px;
			height: 20px;
			line-height: 20px;
			font-size: 12px;
			display: inline-block;
			border: 1px solid #cccccc;
			border-radius: 25px;
			background: #e6e6e6;
			color: #6f6f6f;
			margin: 0 3px;
			font-weight: bold;
		}

	</style>
</head>
<body>
<div id="gantt_here"></div>
<script>

	gantt.config.resource_property = "owner";
	gantt.config.order_branch = true;
	gantt.config.open_tree_initially = true;
	gantt.config.lightbox = {
		sections: [
			{ name: "description", height: 38, map_to: "text", type: "textarea", focus: true },
			{ name: "resources", type: "resources", map_to: "auto", default_value: 8},
			{ name: "time", type: "duration", map_to: "auto" }
		]
	};

	gantt.config.columns = [
		{name: "text", tree: true, width: 200, resize: true},
		{name: "start_date", align: "center", width: 80, resize: true},
		{name: "owner", align: "center", width: 75, label: "Owner", template: function (task) {
			if (task.type == gantt.config.types.project) {
				return "";
			}
			const resources = gantt.getTaskResources(task.id);
			if(!resources.length){
				return "Unassigned";
			}else if(resources.length === 1){
				return resources[0].text;
			}else{
				return resources.map(function(resource){
					return "<div class='owner-label' title='" + resource.text + "'>" + resource.text.substr(0, 1) + "</div>";
				}).join("");
			}
			}, resize: true
		},
		{name: "duration", width: 60, align: "center"},
		{name: "add", width: 44}
	];

	gantt.config.layout = {
		css: "gantt_container",
		rows: [
			{
				cols: [
					{view: "grid", group:"grids", scrollY: "scrollVer"},
					{resizer: true, width: 1},
					{view: "timeline", scrollX: "scrollHor", scrollY: "scrollVer"},
					{view: "scrollbar", id: "scrollVer", group:"vertical"}
				],
				gravity:2
			},
			{resizer: true, width: 1},
			{
				config: {
					columns: [
						{
							name: "name", label: "Name", tree:true, template: function (resource) {
								return resource.text;
							}
						},
						{
							name: "workload", label: "Workload", template: function (resource) {
								var totalDuration = 0;
								if (resource.$role === "task"){
									gantt.getResourceAssignments(resource.$resource_id, resource.$task_id).forEach(function(a){
										totalDuration += a.value * a.duration;
									});
								}else{
									gantt.ext.resources.getSummaryResourceAssignments(resource.id).forEach(function (assignment) {
										totalDuration += Number(assignment.value) * assignment.duration;
									});
								}
								return (totalDuration || 0) + "h";
							}
						}
					]
				},
				cols: [
					{view: "resourceGrid", group:"grids", width: 435, scrollY: "resourceVScroll" },
					{resizer: true, width: 1},
					{view: "resourceTimeline", scrollX: "scrollHor", scrollY: "resourceVScroll"},
					{view: "scrollbar", id: "resourceVScroll", group:"vertical"}
				],
				gravity:1
			},
			{view: "scrollbar", id: "scrollHor"}
		]
	};

	gantt.config.resources = {
		editable_resource_diagram: true,
		dataprocessor_assignments: true
	};

	gantt.locale.labels.section_resources = "Owners";

	gantt.init("gantt_here");

	gantt.createDataProcessor(function(entity, action, data, id){
		gantt.config.readonly = true;
		return new Promise(function(resolve, reject){
			console.log(JSON.stringify({
				entity,
				action,
				data,
				id
			}, null, 2));
			
			gantt.config.readonly = false;
			if(action === "create"){
				// when new item is inserted, we need to either return database id from Promise or to reload gantt data
				const permanentId = `${entity}_${Math.round(Math.random() * 1000)}`;
				resolve({id: permanentId});	
			}else{
				resolve();
			}
		});
	});

	gantt.parse({
		tasks: [
			{
				id: 1,
				text: "Office itinerancy",
				type: "project",
				start_date: "02-04-2024 00:00",
				duration: 17,
				progress: 0.4,
				parent: 0
			},
			{
				id: 2,
				text: "Office facing",
				type: "project",
				start_date: "02-04-2024 00:00",
				duration: 8,
				progress: 0.6,
				parent: "1"
			},
			{
				id: 3,
				text: "Furniture installation",
				type: "project",
				start_date: "11-04-2024 00:00",
				duration: 8,
				parent: "1",
				progress: 0.6,
				owner: []
			},
			{
				id: 4,
				text: "The employee relocation",
				type: "project",
				start_date: "13-04-2024 00:00",
				duration: 5,
				parent: "1",
				progress: 0.5,
				owner: [],
				priority: 3
			},
			{
				id: 5,
				text: "Interior office",
				type: "task",
				start_date: "03-04-2024 00:00",
				duration: 7,
				parent: "2",
				progress: 0.6,
				owner: [
				{
						resource_id: "6",
						value: 3,
						start_date: "03-04-2024 00:00",
						end_date: "05-04-2024 00:00",
					}],
				priority: 1
			},
			{
				id: 6,
				text: "Air conditioners check",
				type: "task",
				start_date: "03-04-2024 00:00",
				duration: 7,
				parent: "2",
				progress: 0.6,
				owner: [
				{
						resource_id: "6",
						value: 4,
						delay: 2
					}],
				priority: 2
			},
			{
				id: 7,
				text: "Workplaces preparation",
				type: "task",
				start_date: "12-04-2024 00:00",
				duration: 8,
				parent: "3",
				progress: 0.6,
				owner: [
					{
						resource_id: "10",
						value: 2
					}]
			},
			{
				id: 8,
				text: "Preparing workplaces",
				type: "task",
				start_date: "14-04-2024 00:00",
				duration: 5,
				parent: "4",
				progress: 0.5,
				owner: [
					{
						resource_id: "10",
						value: 4
					},
					{
						resource_id: "9",
						value: 5
					}],
				priority: 1
			},
			{
				id: 9,
				text: "Workplaces importation",
				type: "task",
				start_date: "21-04-2024 00:00",
				duration: 4,
				parent: "4",
				progress: 0.5,
				owner: [
					{
						resource_id: "7",
						value: 3
					}]
			},
			{
				id: 10,
				text: "Workplaces exportation",
				type: "task",
				start_date: "27-04-2024 00:00",
				duration: 3,
				parent: "4",
				progress: 0.5,
				owner: [
					{
						resource_id: "8",
						value: 5
					}],
				priority: 2
			},
			{
				id: 11,
				text: "Product launch",
				type: "project",
				progress: 0.6,
				start_date: "02-04-2024 00:00",
				duration: 13,
				owner: [
					{
						resource_id: "5",
						value: 4
					}],
				parent: 0
			},
			{
				id: 12,
				text: "Perform Initial testing",
				type: "task",
				start_date: "03-04-2024 00:00",
				duration: 5,
				parent: "11",
				progress: 1,
				owner: [
					{
						resource_id: "7",
						value: 6
					}]
			},
			{
				id: 13,
				text: "Development",
				type: "project",
				start_date: "03-04-2024 00:00",
				duration: 11,
				parent: "11",
				progress: 0.5,
				owner: [
					{
						resource_id: "5",
						value: 2
					}]
			},
			{
				id: 14,
				text: "Analysis",
				type: "task",
				start_date: "03-04-2024 00:00",
				duration: 6,
				parent: "11",
				owner: [
				],
				progress: 0.8
			},
			{
				id: 15,
				text: "Design",
				type: "project",
				start_date: "03-04-2024 00:00",
				duration: 5,
				parent: "11",
				progress: 0.2,
				owner: [
					{
						resource_id: "5",
						value: 5
					}]
			},
			{
				id: 16,
				text: "Documentation creation",
				type: "task",
				start_date: "03-04-2024 00:00",
				duration: 7,
				parent: "11",
				progress: 0,
				owner: [
					{
						resource_id: "7",
						value: 2
					}],
				priority: 1
			},
			{
				id: 17,
				text: "Develop System",
				type: "task",
				start_date: "03-04-2024 00:00",
				duration: 2,
				parent: "13",
				progress: 1,
				owner: [
					{
						resource_id: "8",
						value: 1
					}],
				priority: 2
			},
			{
				id: 25,
				text: "Beta Release",
				type: "milestone",
				start_date: "06-04-2024 00:00",
				parent: "13",
				progress: 0,
				owner: [
					{
						resource_id: "5",
						value: 1
					}],
				duration: 0
			},
			{
				id: 18,
				text: "Integrate System",
				type: "task",
				start_date: "10-04-2024 00:00",
				duration: 2,
				parent: "13",
				progress: 0.8,
				owner: [
					{
						resource_id: "6",
						value: 2
					}],
				priority: 3
			},
			{
				id: 19,
				text: "Test",
				type: "task",
				start_date: "13-04-2024 00:00",
				duration: 4,
				parent: "13",
				progress: 0.2,
				owner: [
					{
						resource_id: "6",
						value: 3
					}]
			},
			{
				id: 20,
				text: "Marketing",
				type: "task",
				start_date: "13-04-2024 00:00",
				duration: 4,
				parent: "13",
				progress: 0,
				owner: [
					{
						resource_id: "8",
						value: 4
					}],
				priority: 1
			},
			{
				id: 21,
				text: "Design database",
				type: "task",
				start_date: "03-04-2024 00:00",
				duration: 4,
				parent: "15",
				progress: 0.5,
				owner: [
					{
						resource_id: "6",
						value: 5
					}]
			},
			{
				id: 22,
				text: "Software design",
				type: "task",
				start_date: "03-04-2024 00:00",
				duration: 4,
				parent: "15",
				progress: 0.1,
				owner: [
					{
						resource_id: "8",
						value: 3
					}],
				priority: 1
			},
			{
				id: 23,
				text: "Interface setup",
				type: "task",
				start_date: "03-04-2024 00:00",
				duration: 5,
				parent: "15",
				progress: 0,
				owner: [
					{
						resource_id: "8",
						value: 5
					}],
				priority: 1
			},
			{
				id: 24,
				text: "Release v1.0",
				type: "milestone",
				start_date: "20-04-2024 00:00",
				parent: "11",
				progress: 0,
				owner: [
					{
						resource_id: "5",
						value: 3
					}],
				duration: 0
			}
		],
		links: [],
		resources: [
			{id: 1, text: "QA", parent:null},
			{id: 2, text: "Development", parent:null},
			{id: 3, text: "Sales", parent:null},
			{id: 4, text: "Other", parent:null},
			{id: 5, text: "Unassigned", parent:4},
			{id: 6, text: "John", parent:1, unit: "hours/day" },
			{id: 7, text: "Mike", parent:2, unit: "hours/day" },
			{id: 8, text: "Anna", parent:2, unit: "hours/day" },
			{id: 9, text: "Bill", parent:3, unit: "hours/day" },
			{id: 10, text: "Floe", parent:3, unit: "hours/day" }
		]
	});

</script>
</body>