<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Resources control</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.9"></script>
	<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Open+Sans|Roboto:regular,medium,thin,bold">
	<link rel="stylesheet" href="../../codebase/skins/dhtmlxgantt_material.css?v=8.0.9">
	<script src="../common/testdata.js?v=8.0.9"></script>
</head>

<body>
<div id="gantt_here" style='width:100%; height:100vh'></div>

<script>
	gantt.plugins({
		tooltip: true
	});

	var usageMap =  [
		{ key: 1, label: "wood", unit: "box" },
		{ key: 2, label: "water", unit: "liter" },
		{ key: 3, label: "grain", unit: "lbs",
			options: [
				{ key: 1, label: "10" },
				{ key: 2, label: "20" },
				{ key: 3, label: "30" }
			]
		}
	];

	var roomsMap =  [
		{ key: 1, label: "room 1", unit: "hours" },
		{ key: 2, label: "room 2", unit: "hours" },
		{ key: 3, label: "room 3", unit: "hours" }
	];

	var helper = {
		getArrayForTemplate: function(resourcesUsed, resourcesDefinition) {
			return resourcesUsed.map(function(entry) {
				var value = entry.value;
				var currentResource = helper.getItemById(resourcesDefinition, entry.resource_id);

				if (currentResource.options) {
					value = helper.getItemById(currentResource.options, entry.value).label;
				}
				return currentResource.label + ": " + value + " " + currentResource.unit;
			});
		},
		getItemById: function(resources, id) {
			var result = resources.filter(function(option) {
				return option.key == id;
			});

			return result[0];
		}
	}

	gantt.config.grid_width = 580;
	gantt.config.add_column = false;
	gantt.config.columns = [
		{ name: "text", label: "Task name", tree: true, width: 200 },
		{ name: "start_date", label: "Start time", align: "center" },
		{ name: "duration", label: "Duration", align: "center" },
		{
			name: "resources", label: "Resources",
			template: function (item) {
				if (!item.usage) return "";
				return helper.getArrayForTemplate(item.usage, usageMap).join("; ");
			}
		},
		{
			name: "rooms", label: "Rooms",
			template: function (item) {
				if (!item.rooms) return "";
				return helper.getArrayForTemplate(item.rooms, roomsMap).join("; ");
			}
		}
	];
	gantt.locale.labels.section_priority = "Priority";
	gantt.locale.labels.section_resources = "Resources";
	gantt.locale.labels.section_rooms = "Rooms";

	gantt.config.lightbox.sections = [
		{ name: "description", height: 38, map_to: "text", type: "textarea", focus: true },
		{
			name: "priority", height: 22, map_to: "priority", type: "select", options: [
				{key: 1, label: "High"},
				{key: 2, label: "Normal"},
				{key: 3, label: "Low"}
			]
		},
		{ name: "time", type: "duration", map_to: "auto" },
		{
			name: "resources", type: "resources", map_to: "usage", options: usageMap
		},
		{
			name: "rooms", type: "resources", map_to: "rooms", options: roomsMap
		}
	];

	gantt.templates.tooltip_text = function(start, end, task) {
		var tooltip = "";
		tooltip += "<b>Task:</b> "+task.text+"<br/>";
		tooltip += "<b>Duration:</b> " + task.duration + "<br/>";
		if (task.usage && task.usage.length > 0) {
			tooltip += "<b>Resources:</b><br/>" + helper.getArrayForTemplate(task.usage, usageMap).join("<br/>");
		}
		if (task.rooms && task.rooms.length > 0) {
			tooltip += "<b>Rooms:</b><br/>" + helper.getArrayForTemplate(task.rooms, roomsMap).join("<br/>");
		}
		return tooltip;
	};

	gantt.init("gantt_here");
	gantt.parse(users_data);
	gantt.showLightbox(1);
</script>
</body>