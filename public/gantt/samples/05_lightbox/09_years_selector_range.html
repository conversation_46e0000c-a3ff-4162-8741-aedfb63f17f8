<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Specify year selector range</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.9"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.9">

	<script src="../common/testdata.js?v=8.0.9"></script>
	<style>
		html, body {
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}
	</style>
</head>

<body>
<div id="gantt_here" style='width:100%; height:100%'></div>

<style>
	.red .gantt_cell, .odd.red .gantt_cell,
	.red .gantt_task_cell, .odd.red .gantt_task_cell {
		background-color: #FDE0E0;
	}

	.green .gantt_cell, .odd.green .gantt_cell,
	.green .gantt_task_cell, .odd.green .gantt_task_cell {
		background-color: #BEE4BE;
	}
</style>
<script>
	gantt.locale.labels["section_priority"] = "Priority";
	gantt.config.lightbox.sections = [
		{name: "description", height: 38, map_to: "text", type: "textarea", focus: true},
		{name: "time", type: "duration", map_to: "auto", year_range: [2010, 2025]}
	];

	gantt.init("gantt_here");
	gantt.parse(users_data);
</script>
</body>