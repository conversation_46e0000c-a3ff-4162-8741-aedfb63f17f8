<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Checkbox control</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.9"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.9">
	<script src="../common/testdata.js?v=8.0.9"></script>
	<style>
		html, body{
			padding:0;
			margin: 0;
		}
	</style>
</head>

<body>
<div id="gantt_here" style='height:100vh;'></div>

<script>
	function getUserList(data) {
		var users = [];
		var uniqueUsers = {};
		var i;
		var result = [];

		for (i=0; i<data.length; i++) {
			users = users.concat(data[i].users);
		}

		for (i=0; i<users.length; i++) {
			uniqueUsers[users[i]] = true;
		}

		for (var key in uniqueUsers) {
			if (uniqueUsers.hasOwnProperty(key)) {
				result[result.length] = key;
			}
		}
        result.sort();

        return result.map(function(entry) {
            return {key: entry, label: entry};
        });
    }

	gantt.config.grid_width = 380;
	gantt.config.add_column = false;
	gantt.config.columns = [
		{name: "text", label: "Task name", tree: true, width: '*'},
		{name: "start_date", label: "Start time", align: "center"},
		{name: "duration", label: "Duration", align: "center"},
		{
			name: "priority", label: "Priority", width: 80, align: "center",
			template: function (item) {
				if (item.priority == 1)
					return "High";
				if (item.priority == 2)
					return "Normal";
				return "Low";
			}
		},
		{
			name: "assigned", label: "Assigned to",
			template: function (item) {
				if (!item.users) return "Nobody";
				return item.users.join(", ");
			}
		}
	];
	gantt.locale.labels.section_priority = "Priority";
	gantt.locale.labels.section_assigned = "Assigned to";

	gantt.config.lightbox.sections = [
		{name: "description", height: 38, map_to: "text", type: "textarea", focus: true},
		{
			name: "priority", height: 22, map_to: "priority", type: "select", options: [
				{key: 1, label: "High"},
				{key: 2, label: "Normal"},
				{key: 3, label: "Low"}
			]
		},
		{name: "time", type: "duration", map_to: "auto"},
		{
			name: "assigned", type: "checkbox", map_to: "users", options: getUserList(users_data.data),
			onchange: function() {
				console.log("checkbox switched");
			}
		}
	];

	gantt.init("gantt_here");
	gantt.parse(users_data);
	gantt.showLightbox(1);
</script>
</body>