<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Validate lightbox values</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.9"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.9">

	<script src="../common/testdata.js?v=8.0.9"></script>
	<style>
		html, body {
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}
	</style>
</head>

<body>
<div id="gantt_here" style='width:100%; height:100%;'></div>
<script>
	gantt.config.details_on_create = true;
	gantt.config.lightbox.sections = [
		{name: "description", height: 38, map_to: "text", type: "textarea", focus: true},
		{
			name: "user", height: 22, map_to: "user", type: "select", options: [
				{key: "", label: "Select a worker..."},
				{key: "<PERSON>", label: "<PERSON>"},
				{key: "<PERSON>", label: "<PERSON>"},
				{key: "<PERSON>", label: "Anna"}]
		},
		{name: "time", type: "duration", map_to: "auto"}
	];

	gantt.locale.labels["section_user"] = "Worker";

	gantt.attachEvent("onLightboxSave", function (id, item) {
		if (!item.text) {
			gantt.message({type: "error", text: "Enter task description!"});
			return false;
		}
		if (!item.user) {
			gantt.message({type: "error", text: "Pick a worker for this task!"});
			return false;
		}
		return true;
	});

	gantt.init("gantt_here");
	gantt.parse({
		data: [
			{id: 1, text: "Project #1", start_date: "01-04-2023", duration: "11", progress: 0.6, open: true, user: "", priority: "2"},
			{id: 2, text: "Task #1", start_date: "03-04-2023", duration: "5", parent: "1", progress: 1, open: true, user: "John", priority: "1"},
			{id: 3, text: "Task #2", start_date: "02-04-2023", duration: "7", parent: "1", progress: 0.5, open: true, user: "Anna", priority: "1"},
			{id: 4, text: "Task #3", start_date: "02-04-2023", duration: "6", parent: "1", progress: 0.8, open: true, user: "Mike", priority: "2"},
			{id: 5, text: "Task #4", start_date: "02-04-2023", duration: "5", parent: "1", progress: 0.2, open: true, user: "John", priority: "3"},
			{id: 6, text: "Task #5", start_date: "02-04-2023", duration: "7", parent: "1", progress: 0, open: true, user: "John", priority: "2"},
			{id: 7, text: "Task #2.1", start_date: "03-04-2023", duration: "2", parent: "3", progress: 1, open: true, user: "Mike", priority: "2"},
			{id: 8, text: "Task #2.2", start_date: "06-04-2023", duration: "3", parent: "3", progress: 0.8, open: true, user: "Anna", priority: "3"},
			{id: 9, text: "Task #2.3", start_date: "10-04-2023", duration: "4", parent: "3", progress: 0.2, open: true, user: "Mike", priority: "1"},
			{id: 10, text: "Task #2.4", start_date: "10-04-2023", duration: "4", parent: "3", progress: 0, open: true, user: "John", priority: "1"},
			{id: 11, text: "Task #4.1", start_date: "03-04-2023", duration: "4", parent: "5", progress: 0.5, open: true, user: "John", priority: "3"},
			{id: 12, text: "Task #4.2", start_date: "03-04-2023", duration: "4", parent: "5", progress: 0.1, open: true, user: "John", priority: "3"},
			{id: 13, text: "Task #4.3", start_date: "03-04-2023", duration: "5", parent: "5", progress: 0, open: true, user: "Anna", priority: "3"}
		],
		links: [
			{id: "10", source: "11", target: "12", type: "1"},
			{id: "11", source: "11", target: "13", type: "1"}
		]
	});
</script>
</body>