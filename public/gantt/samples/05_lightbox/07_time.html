<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Time control</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.9"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.9">
	<script src="../common/testdata.js?v=8.0.9"></script>
	<style>
		html, body{
			padding:0;
			margin: 0;
		}
	</style>
</head>

<body>
<div id="gantt_here" style='height:100vh;'></div>

<script>
	gantt.locale.labels.section_period = "Time period";
	gantt.config.lightbox.sections = [
		{name: "description", height: 38, map_to: "text", type: "textarea", focus: true},
		{name: "period", type: "time", map_to: "auto"}
	];

	gantt.init("gantt_here");
	gantt.parse(users_data);
	gantt.showLightbox(1);
</script>
</body>