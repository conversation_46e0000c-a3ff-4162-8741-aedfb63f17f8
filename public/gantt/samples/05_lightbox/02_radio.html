<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Radio control</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.9"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.9">
	<script src="../common/testdata.js?v=8.0.9"></script>
	<style>
		html, body{
			padding:0;
			margin: 0;
		}
	</style>
</head>

<body>
<div id="gantt_here" style='height:100vh;'></div>

<script>
	gantt.config.grid_width = 380;
	gantt.config.add_column = false;
	gantt.config.columns = [
		{name: "text", label: "Task name", tree: true, width: '*'},
		{name: "start_date", label: "Start time", align: "center"},
		{name: "duration", label: "Duration", align: "center"},
		{
			name: "priority", label: "Priority", width: 80, align: "center",
			template: function (item) {
				if (item.priority == 1)
					return "High";
				if (item.priority == 2)
					return "Normal";
				if (item.priority == 3)
					return "Low";
				return "Undefined";
			}
		}
	];
	gantt.locale.labels.section_priority = "Priority";

	gantt.config.lightbox.sections = [
		{name: "description", height: 38, map_to: "text", type: "textarea", focus: true},
		{
			name: "priority", height: 22, map_to: "priority", type: "radio",
			options: [
				{key: 1, label: "High"},
				{key: 2, label: "Normal"},
				{key: 3, label: "Low"},
			],
			onchange: function() {
				console.log("radio switched");
			}
		},
		{name: "time", type: "duration", map_to: "auto"}
	];
	gantt.attachEvent("onLightboxSave", function(id, task, is_new){
		//any custom logic here
		return true;
	});

	gantt.init("gantt_here");
	gantt.parse(users_data);
	gantt.showLightbox(1);
</script>
</body>