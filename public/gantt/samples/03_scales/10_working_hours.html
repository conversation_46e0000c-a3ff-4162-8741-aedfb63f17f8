<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Show working hours</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.9"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.9">

	<script src="../common/testdata.js?v=8.0.9"></script>
	<style>
		html, body {
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}

		.gantt_task_cell.day_end {
			border-right-color: #C7DFFF;
		}
	</style>
</head>

<body>
<div id="gantt_here" style='width:100%; height:100%;'></div>
<script>

	gantt.config.min_column_width = 20;

	gantt.config.scale_height = 20 * 3;

	gantt.templates.timeline_cell_class = function (task, date) {
		if (date.getHours() == 8) {
			return "day_start";
		}
		if (date.getHours() == 18) {
			return "day_end";
		}
		return "";
	};


	var weekScaleTemplate = function (date) {
		var dateToStr = gantt.date.date_to_str("%d %M");
		var weekNum = gantt.date.date_to_str("(week %W)");
		var endDate = gantt.date.add(gantt.date.add(date, 1, "week"), -1, "day");
		return dateToStr(date) + " - " + dateToStr(endDate) + " " + weekNum(date);
	};

	gantt.config.scales = [
		{unit: "day", format: "%l, %F %d"},
		//	{unit:"month", step:1, format:"%F, %Y"},
		{unit: "week", step: 1, format: weekScaleTemplate},
		{unit: "hour", step: 1, format: "%G"}

	];

	gantt.ignore_time = function (date) {
		if (date.getDay() == 0 || date.getDay() == 6)
			return true;
		if (date.getHours() < 8 || date.getHours() > 18)
			return true;

		return false;
	};
	gantt.init("gantt_here");
	gantt.parse(demo_tasks);
</script>
</body>