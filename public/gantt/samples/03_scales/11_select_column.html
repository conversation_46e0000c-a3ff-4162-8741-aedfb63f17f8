<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Selecting columns</title>

	<script src="../../codebase/dhtmlxgantt.js?v=8.0.9"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.9">

	<script src="../common/testdata.js?v=8.0.9"></script>
	<style>
		html, body {
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}

		.highlighted-column {
			background-color: #fff3a1;
		}

		.gantt_task_scale .gantt_scale_cell {
			cursor: default;
		}

		.gantt_task_scale .gantt_scale_cell.highlighted-column {
			color: #454545;
			font-weight: bold;
		}
	</style>
</head>
<body>
<div id="gantt_here" style='width:100%; height:100%;'></div>
<script>

	var selected_column = null;

	gantt.attachEvent("onScaleClick", function (e, date) {
		selected_column = date;
		var pos = gantt.getScrollState();
		gantt.render();
		gantt.scrollTo(pos.x, pos.y);
	});

	function is_selected_column(column_date) {
		if (selected_column && column_date.valueOf() == selected_column.valueOf()) {
			return true;
		}
		return false;
	}

	gantt.templates.scale_cell_class = function (date) {
		if (is_selected_column(date))
			return "highlighted-column";
	};
	gantt.templates.timeline_cell_class = function (item, date) {
		if (is_selected_column(date))
			return "highlighted-column";
	};

	gantt.message("Click on any date in a scale to select a column");

	gantt.init("gantt_here");
	gantt.parse(demo_tasks);

</script>
</body>