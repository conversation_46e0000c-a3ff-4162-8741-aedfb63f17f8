<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Auto resize scale</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.9"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.9">

	<script src="../common/testdata.js?v=8.0.9"></script>
	<style>
		html, body {
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}
	</style>
</head>

<body>
<div id="gantt_here" style='width:100%; height:100%;'></div>
<script>

	gantt.config.fit_tasks = true;

	gantt.config.min_column_width = 50;

	gantt.config.scale_height = 54;

	gantt.config.scales = [
		{unit: "day", format: "%F %d"},
		{unit: "hour", step: 3, format: "%H:%i"}
	];

	gantt.init("gantt_here");

	function showScaleDesc() {
		var min = gantt.getState().min_date,
			max = gantt.getState().max_date,
			to_str = gantt.templates.task_date;

		return gantt.message("Scale shows days from " + to_str(min) + " to " + to_str(max));
	}

	gantt.parse(demo_tasks);
	setTimeout(showScaleDesc, 500);
	setTimeout(function () {
		gantt.message("Change date or duration of any task and scales will be adjusted");
	}, 4500);
	gantt.attachEvent("onScaleAdjusted", showScaleDesc);

</script>
</body>