<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Not render weekends on the scale</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.9"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.9">

	<script src="../common/data.js?v=8.0.9"></script>

	<style>
		html, body {
			padding: 0px;
			margin: 0px;
			height: 100%;
		}
	</style>
</head>
<body>
<div id="gantt_here" style='width:100%; height:100%;'></div>
<script>

	gantt.config.date_format = "%Y-%m-%d %H:%i:%s";

	gantt.config.min_column_width = 50;

	gantt.config.scale_height = 90;

	var weekScaleTemplate = function (date) {
		var dateToStr = gantt.date.date_to_str("%d %M");
		var endDate = gantt.date.add(gantt.date.add(date, 1, "week"), -1, "day");
		return dateToStr(date) + " - " + dateToStr(endDate);
	};

	gantt.config.scales = [
		{unit: "month", step: 1, format: "%F, %Y"},
		{unit: "week", step: 1, format: weekScaleTemplate},
		{unit: "day", step: 1, format: "%D"}
	];


	gantt.ignore_time = function (date) {
		if (date.getDay() == 0 || date.getDay() == 6)
			return true;
		return false;
	};

	gantt.init("gantt_here");
	gantt.parse(taskData);
</script>
</body>