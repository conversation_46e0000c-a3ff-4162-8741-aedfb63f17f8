<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Custom scales</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.9"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.9">
	<script src="../common/testdata.js?v=8.0.9"></script>
	<style>
		html, body {
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}

		.gantt_container .gantt_grid_scale .gantt_grid_head_cell {
			line-height: 43px;
		}

		.gantt_container .gantt_scale_cell {
			padding-top: 4px;
			line-height: 17px;
		}
	</style>
</head>

<body>
<div id="gantt_here" style='width:100%; height:100%;'></div>

<script>
	gantt.config.scale_height = 44;

	var dayNumber = function(date){
		return gantt.columnIndexByDate(date) + 1;
	} 
	var dateFormat = gantt.date.date_to_str("%F %d");

	gantt.config.scales = [
		{ unit: "day", step:1, format: function(date){
			return "<strong>Day " + dayNumber(date) + "</strong><br/>" + dateFormat(date);
		}}
	]


	gantt.init("gantt_here");
	gantt.parse(demo_tasks);
</script>
</body>