<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Decimal durations for tasks</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.9"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.9">
	<link rel="stylesheet" href="../common/controls_styles.css?v=8.0.9">

	<script src="../common/testdata.js?v=8.0.9"></script>
	<style>
		html, body {
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}

		.gantt_task_cell.week_end, .gantt_task_cell.no_work_hour {
			background-color: #F5F5F5;
		}

		.gantt_task_row.gantt_selected .gantt_task_cell.week_end {
			background-color: #F8EC9C;
		}
	</style>
</head>

<body>
<div id="gantt_here" style='width:100%; height:100vh;'></div>

<script>

	// scale settings
	gantt.config.scales = [
		{unit: "month", step: 1, format:"%M %Y"},
		{unit: "day", step: 1, format: "%d, %l"},
		{unit: "hour", step: 1, format: "%H"},
	];
	gantt.config.scale_height = 20 * 3;
	gantt.config.min_column_width = 18;
	gantt.ignore_time = function(date) {
		return !gantt.isWorkTime(date, "hour");
	};

	// work time and duration
	gantt.config.duration_unit = "minute";
	gantt.config.work_time = true;
	gantt.config.time_step = 15;
	gantt.config.round_dnd_dates = false;
	gantt.config.open_tree_initially = true;

	gantt.setWorkTime({hours: [9, 13, 14, 18]});//global working hours. 8:00-12:00, 13:00-17:00

	// formatting durations
	var dayFormatter = gantt.ext.formatters.durationFormatter({
		enter: "day", 
		store: "minute", 
		format: "day",
		hoursPerDay: 8,
		hoursPerWeek: 40,
		daysPerMonth: 30,
		short: false	
	});
	var hourFormatter = gantt.ext.formatters.durationFormatter({
		enter: "hour", 
		store: "minute", 
		format: "hour",
		short: true	
	});
	var autoFormatter = gantt.ext.formatters.durationFormatter({
		enter: "day", 
		store: "minute", 
		format: "auto"
	});

	var textEditor = {type: "text", map_to: "text"};
	var dateEditor = {type: "date", map_to: "start_date", min: new Date(2023, 0, 1), max: new Date(2024, 0, 1)};
	var durationEditor = {type: "duration", map_to: "duration", formatter: autoFormatter, min:0, max:1000};
	var dayDurationEditor = {type: "duration", map_to: "duration", formatter: dayFormatter, min:0, max:1000};
	var hourDurationEditor = {type: "duration", map_to: "duration", formatter: hourFormatter, min:0, max:1000};

	gantt.config.columns = [
		{name: "text", tree: true, width: 170, resize: true, editor: textEditor},
		{name: "start_date", align: "center", resize: true, editor: dateEditor},
		{name: "duration", label:"Duration", resize: true, align: "center", template: function(task) {
			return autoFormatter.format(task.duration);
		}, editor: durationEditor, width: 100},
		{name: "dayDuration", label:"Duration (days)", resize: true, align: "center", template: function(task) {
			return dayFormatter.format(task.duration);
		}, editor: dayDurationEditor, width: 100},
		{name: "hourDuration", label:"Duration (hours)", resize: true, align: "center", template: function(task) {
			return hourFormatter.format(task.duration);
		}, editor: hourDurationEditor, width: 100},
		{name: "add", width: 44}
	];
	gantt.config.lightbox.sections = [
		{name: "description", height: 70, map_to: "text", type: "textarea", focus: true},
		{name: "time", type: "duration", map_to: "auto", formatter: autoFormatter}
	];

	gantt.templates.timeline_cell_class = function (task, date) {
		var css = [];

		if (!gantt.isWorkTime(date, 'day')) {
			css.push("week_end");
		}

		return css.join(" ");
	};



	gantt.init("gantt_here");
	gantt.parse({
		data: [
			{ id: 1, text: "Office itinerancy", type: "project", start_date: "02-04-2024 00:00", duration: 58*60, progress: 0.4, parent: 0},
			{ id: 2, text: "Office facing", type: "project", start_date: "02-04-2024 00:00", duration: 64*60, progress: 0.6, parent: "1"},
			{ id: 3, text: "Furniture installation", type: "project", start_date: "11-04-2024 00:00", duration: 64*60, parent: "1", progress: 0.6, owner_id: "5"},
			{ id: 4, text: "The employee relocation", type: "project", start_date: "13-04-2024 00:00", duration: 40*60, parent: "1", progress: 0.5, priority:3},
			{ id: 5, text: "Interior office", type: "task", start_date: "03-04-2024 00:00", duration: 56*60, parent: "2", progress: 0.6, priority:1},
			{ id: 6, text: "Air conditioners check", type: "task", start_date: "03-04-2024 00:00", duration: 4*60, parent: "2", progress: 0.6, priority:2},
			{ id: 7, text: "Workplaces preparation", type: "task", start_date: "12-04-2024 00:00", duration: 4*60, parent: "3", progress: 0.6, owner_id: "10"},
			{ id: 8, text: "Preparing workplaces", type: "task", start_date: "14-04-2024 00:00", duration: 12*60, parent: "4", progress: 0.5, priority:1},
			{ id: 9, text: "Workplaces importation", type: "task", start_date: "21-04-2024 00:00", duration: 32*60, parent: "4", progress: 0.5, owner_id: "7"},
			{ id: 10, text: "Workplaces exportation", type: "task", start_date: "27-04-2024 00:00", duration: 26*60, parent: "4", progress: 0.5, priority:2},
			{ id: 11, text: "Product launch", type: "project", progress: 0.6, start_date: "02-04-2024 00:00", duration: 120*60, parent: 0},
			{ id: 12, text: "Perform Initial testing", type: "task", start_date: "03-04-2024 00:00", duration: 40*60, parent: "11", progress: 1, owner_id: "7"},
			{ id: 13, text: "Development", type: "project", start_date: "03-04-2024 00:00", duration: 88*60, parent: "11", progress: 0.5, owner_id: "5"},
			{ id: 14, text: "Analysis", type: "task", start_date: "03-04-2024 00:00", duration: 48*60, parent: "11", progress: 0.8, owner_id: "5"},
			{ id: 15, text: "Design", type: "project", start_date: "03-04-2024 00:00", duration: 40*60, parent: "11", progress: 0.2, owner_id: "5"},
			{ id: 16, text: "Documentation creation", type: "task", start_date: "03-04-2024 00:00", duration: 56*60, parent: "11", progress: 0, priority:1},
			{ id: 17, text: "Develop System", type: "task", start_date: "03-04-2024 00:00", duration: 16*60, parent: "13", progress: 1, priority:2},
			{ id: 25, text: "Beta Release", type: "milestone", start_date: "06-04-2024 00:00", parent: "13", progress: 0, duration: 0},
			{ id: 18, text: "Integrate System", type: "task", start_date: "10-04-2024 00:00", duration: 16*60, parent: "13", progress: 0.8, priority:3},
			{ id: 19, text: "Test", type: "task", start_date: "13-04-2024 00:00", duration: 32*60, parent: "13", progress: 0.2, owner_id: "6"},
			{ id: 20, text: "Marketing", type: "task", start_date: "13-04-2024 00:00", duration: 32*60, parent: "13", progress: 0, priority:1},
			{ id: 21, text: "Design database", type: "task", start_date: "03-04-2024 00:00", duration: 32*60, parent: "15", progress: 0.5, owner_id: "6"},
			{ id: 22, text: "Software design", type: "task", start_date: "03-04-2024 00:00", duration: 32*60, parent: "15", progress: 0.1, priority:1},
			{ id: 23, text: "Interface setup", type: "task", start_date: "03-04-2024 00:00", duration: 40*60, parent: "15", progress: 0, priority:1},
			{ id: 24, text: "Release v1.0", type: "milestone", start_date: "20-04-2024 00:00", parent: "11", progress: 0, duration: 0}

		],
		links: [

			{ id: "2", source: "2", target: "3", type: "0" },
			{ id: "3", source: "3", target: "4", type: "0" },
			{ id: "7", source: "8", target: "9", type: "0" },
			{ id: "8", source: "9", target: "10", type: "0" },
			{ id: "16", source: "17", target: "25", type: "0" },
			{ id: "17", source: "18", target: "19", type: "0" },
			{ id: "18", source: "19", target: "20", type: "0" },
			{ id: "22", source: "13", target: "24", type: "0" },
			{ id: "23", source: "25", target: "18", type: "0" }

		]
	});
</script>
</body>