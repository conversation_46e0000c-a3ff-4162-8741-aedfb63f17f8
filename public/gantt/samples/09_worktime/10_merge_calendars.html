<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Merge work Calendars of different resources</title>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.9">
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.9"></script>
	<script src="../11_resources/common/jquery_multiselect.js?v=8.0.9"></script>
	<link rel="stylesheet" href="../11_resources/common/jquery_multiselect.css?v=8.0.9">
	<link rel="stylesheet" href="../common/controls_styles.css?v=8.0.9">
	<script src="https://code.jquery.com/jquery-3.3.1.min.js?v=8.0.9"
		integrity="sha256-FgpCb/KJQlLNfOu91ta32o/NMZxltwRo8QtmkMRdAu8=" crossorigin="anonymous"></script>


	<script src="https://cdnjs.cloudflare.com/ajax/libs/chosen/1.8.7/chosen.jquery.js?v=8.0.9"></script>

	<link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/chosen/1.8.7/chosen.css?v=8.0.9">


	<style>
		html,
		body {
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}

		.gantt_task_cell.week_end {
			background-color: #e8e8e8;
		}

		.gantt_task_row.gantt_selected .gantt_task_cell.week_end {
			background-color: #e0e0dd !important;
		}
		.gantt_message_area {
			width: 420px;
		}
	</style>
</head>

<body>
	<div id="gantt_here" style="width:100%; height:100%;"></div>
	<script>
		gantt.message({
			text: "Each owner has their own work days:<br>"+
				"- <b>John</b> - <b>Mondays</b>, <b>Wednesdays</b>, and <b>Fridays</b><br>" +
				"- <b>Mike</b> - <b>Saturdays</b>, and <b>Sundays</b><br>" +
				"- <b>Anna</b> - <b>Mondays</b>, <b>Tuesdays</b>, <b>Fridays</b>, and <b>Saturdays</b><br>" +
				"A task with multiple owners gets a dynamic calendar which has a common work time of all assigned workers",
			expire: -1}
			);

		gantt.config.work_time = true;
		gantt.config.min_column_width = 60;
		gantt.config.duration_unit = "day";
		gantt.config.scale_height = 20 * 3;
		gantt.config.row_height = 28;

		var weekScaleTemplate = function(date) {
			var dateToStr = gantt.date.date_to_str("%d %M");
			var weekNum = gantt.date.date_to_str("(week %W)");
			var endDate = gantt.date.add(gantt.date.add(date, 1, "week"), -1, "day");
			return dateToStr(date) + " - " + dateToStr(endDate) + " " + weekNum(date);
		};

		gantt.config.scales = [
			{ unit: "month", step: 1, format: "%F, %Y" },
			{ unit: "week", step: 1, format: weekScaleTemplate },
			{ unit: "day", step: 1, format: "%D, %d" }
		];

		gantt.serverList("users", [
			{ key: "1", label: "John" },
			{ key: "2", label: "Mike" },
			{ key: "3", label: "Anna" }
		]);

		function byId(list, id) {
			for (var i = 0; i < list.length; i++) {
				if (list[i].key == id)
					return list[i].label || "";
			}
			return "";
		}

		gantt.attachEvent("onTaskCreated", function(task){
			task.users = [];
			task.calendar_id = null;
			return true;
		});

		gantt.locale.labels.column_owner = "Owner";
		gantt.locale.labels.section_owner = "Owner";
		gantt.locale.labels.column_duration = "Effort";
		gantt.locale.labels.column_time = "Time";

		gantt.config.columns = [
			{ name: "text", label: "Task name", tree: true, width: 170 },
			{ name: "owner", width: 80, align: "center", template: function(task) {
					return task.users.map(function(userId) {
							return byId(gantt.serverList("users"), userId);
						}).join(",");
				}, width: 60
			},
			{ name: "start_date", align: "center", width: 90 },
			{ name: "time", align: "center", width: 60, template: function(task) {
					var days = 0;
					var date = task.start_date;
					while (date < task.end_date) {
						days++;
						date = gantt.date.add(date, 1, "day");
					}
					return days + "";
				}
			},
			{ name: "duration", align: "center", width: 60 },
			{ name: "add", width: 40 }
		];

		gantt.config.lightbox.sections = [
			{ name: "description", height: 38, map_to: "text", type: "textarea", focus: true },
			{ name: "owner", height: 22, map_to: "users", type: "multiselect", options: gantt.serverList("users"), onchange: function(){
				var value = $(this).chosen().val();
				var resultCalendar = gantt.mergeCalendars(
					value.map(function(resourceId){
						return gantt.getResourceCalendar(resourceId);
					})
				);
				if(!resultCalendar.hasWorkTime()){
					gantt.alert("Selected workers don't have common work time. The global calendar will be used for this task.");
				}
			} },
			{ name: "time", type: "duration", map_to: "auto" }
		];

		// automatically merge work calendars when multiple resources assigned to a task
		gantt.config.dynamic_resource_calendars = true;
		gantt.config.resource_property = "users";
		gantt.config.resource_calendars = {
			1: gantt.addCalendar({
				worktime: {
					days: [0, 1, 0, 1, 0, 1, 0]
				}
			}),
			2: gantt.addCalendar({
				worktime: {
					days: [1, 0, 0, 0, 0, 0, 1]
				}
			}),
			3: gantt.addCalendar({
				worktime: {
					days: [0, 1, 1, 0, 0, 1, 1]
				}
			})
		};


		gantt.templates.timeline_cell_class = function(task, date) {
			if (!gantt.isWorkTime({ date: date, task: task })) {
				return "week_end";
			}
			return "";
		};

		gantt.templates.task_text = function(start, end, task) {
			var text = [task.text];
			var users =  task.users.map(function(entry) {
				return byId(gantt.serverList("users"), entry);
			}).join(",");
			
			text.push(users);
			return text.join(", ");
		};

		gantt.init("gantt_here");
		gantt.parse({
			data: [
				{ id: 11, users: ["0"], text: "Project #1", start_date: "28-03-2023", duration: "3", progress: 0.6, open: true },
				{ id: 1, users: ["1"], text: "Project #2", start_date: "01-04-2023", duration: "5", progress: 0.4, open: true },
				{ id: 2, users: ["0"], text: "Task #1", start_date: "02-04-2023", duration: "2", parent: "1", progress: 0.5, open: true },
				{ id: 3, users: ["1", "3"], text: "Task #2", start_date: "11-04-2023", duration: "4", parent: "1", progress: 0.6, open: true },
				{ id: 4, users: ["3"], text: "Task #3", start_date: "13-04-2023", duration: "3", parent: "1", progress: 0.5, open: true },
				{ id: 5, users: ["0"], text: "Task #1.1", start_date: "02-04-2023", duration: "7", parent: "2", progress: 0.6, open: true },
				{ id: 6, users: ["1"], text: "Task #1.2", start_date: "03-04-2023", duration: "7", parent: "2", progress: 0.6, open: true },
				{ id: 7, users: ["2"], text: "Task #2.1", start_date: "11-04-2023", duration: "8", parent: "3", progress: 0.6, open: true },
				{ id: 8, users: ["0"], text: "Task #3.1", start_date: "14-04-2023", duration: "5", parent: "4", progress: 0.5, open: true },
				{ id: 9, users: ["0"], text: "Task #3.2", start_date: "14-04-2023", duration: "4", parent: "4", progress: 0.5, open: true },
				{ id: 10, users: ["1"], text: "Task #3.3", start_date: "14-04-2023", duration: "3", parent: "4", progress: 0.5, open: true },
				{ id: 12, users: ["0"], text: "Task #1", start_date: "03-04-2023", duration: "5", parent: "11", progress: 1, open: true },
				{ id: 13, users: ["1"], text: "Task #2", start_date: "02-04-2023", duration: "7", parent: "11", progress: 0.5, open: true },
				{ id: 14, users: ["0"], text: "Task #3", start_date: "02-04-2023", duration: "6", parent: "11", progress: 0.8, open: true },
				{ id: 15, users: ["2"], text: "Task #4", start_date: "02-04-2023", duration: "5", parent: "11", progress: 0.2, open: true },
				{ id: 16, users: ["0"], text: "Task #5", start_date: "02-04-2023", duration: "7", parent: "11", progress: 0, open: true },
				{ id: 17, users: ["3"], text: "Task #2.1", start_date: "03-04-2023", duration: "2", parent: "13", progress: 1, open: true },
				{ id: 18, users: ["0"], text: "Task #2.2", start_date: "06-04-2023", duration: "3", parent: "13", progress: 0.8, open: true },
				{ id: 19, users: ["0"], text: "Task #2.3", start_date: "10-04-2023", duration: "4", parent: "13", progress: 0.2, open: true },
				{ id: 20, users: ["0"], text: "Task #2.4", start_date: "10-04-2023", duration: "4", parent: "13", progress: 0, open: true },
				{ id: 21, users: ["0"], text: "Task #4.1", start_date: "03-04-2023", duration: "4", parent: "15", progress: 0.5, open: true },
				{ id: 22, users: ["0"], text: "Task #4.2", start_date: "03-04-2023", duration: "4", parent: "15", progress: 0.1, open: true },
				{ id: 23, users: ["0"], text: "Task #4.3", start_date: "03-04-2023", duration: "5", parent: "15", progress: 0, open: true }
			],
			links: [
				{ id: "1", source: "1", target: "2", type: "1" },
				{ id: "2", source: "2", target: "3", type: "0" },
				{ id: "3", source: "3", target: "4", type: "0" },
				{ id: "4", source: "2", target: "5", type: "2" },
				{ id: "5", source: "2", target: "6", type: "2" },
				{ id: "6", source: "3", target: "7", type: "2" },
				{ id: "7", source: "4", target: "8", type: "2" },
				{ id: "8", source: "4", target: "9", type: "2" },
				{ id: "9", source: "4", target: "10", type: "2" },
				{ id: "10", source: "11", target: "12", type: "1" },
				{ id: "11", source: "11", target: "13", type: "1" },
				{ id: "12", source: "11", target: "14", type: "1" },
				{ id: "13", source: "11", target: "15", type: "1" },
				{ id: "14", source: "11", target: "16", type: "1" },
				{ id: "15", source: "13", target: "17", type: "1" },
				{ id: "16", source: "17", target: "18", type: "0" },
				{ id: "17", source: "18", target: "19", type: "0" },
				{ id: "18", source: "19", target: "20", type: "0" },
				{ id: "19", source: "15", target: "21", type: "2" },
				{ id: "20", source: "15", target: "22", type: "2" },
				{ id: "21", source: "15", target: "23", type: "2" }
			]
		});
	</script>
</body>