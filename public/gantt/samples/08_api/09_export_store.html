<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Export data: store online</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.9"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.9">
	<link rel="stylesheet" href="../common/controls_styles.css?v=8.0.9">

	<script src="../common/testdata.js?v=8.0.9"></script>
	<style>
		html, body {
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}
	</style>
</head>
<body>
<div class="gantt_control">	
	<input value="Export to PDF" type="button" onclick='gantt.exportToPDF({ callback:show_result })'>
	<input value="Export to PNG" type="button" onclick='gantt.exportToPNG({ callback:show_result })'>
	<input value="Export to Excel" type="button" onclick='gantt.exportToExcel({ callback:show_result })'>
	<input value="Export to iCal" type="button" onclick='gantt.exportToICal({ callback:show_result })'>
</div>
<div id="gantt_here" style='width:100%; height: calc(100vh - 52px);'></div>

<script>
	function show_result(info) {
		if (!info)
			gantt.message({
				text: "Server error",
				type: "error",
				expire: -1
			});
		else
			gantt.message({
				text: "Stored at <a href='" + info.url + "'>export.dhtlmx.com</a>",
				expire: -1
			});
	}

	gantt.plugins({
		export_api: true,
	});


	gantt.templates.task_text = function (s, e, task) {
		return "Export " + task.text;
	};

	gantt.config.columns[0].template = function (obj) {
		return obj.text + " -";
	};

	gantt.init("gantt_here");
	gantt.parse(demo_tasks);
</script>
</body>