<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Show Slack time</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.9"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.9">

	<script src="../common/testdata.js?v=8.0.9"></script>
	<link rel="stylesheet" href="../common/controls_styles.css?v=8.0.9">
	<style>
		html, body {
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}

		.gantt_task_cell.week_end {
			background-color: #EFF5FD;
		}

		.gantt_task_row.gantt_selected .gantt_task_cell.week_end {
			background-color: #F8EC9C;
		}

		.slack {
			position: absolute;
			border-radius: 0;
			opacity: 0.7;

			border: none;
			border-right: 1px solid #b6b6b6;
			margin-left: -2px;
			background: #b6b6b6;
			background: repeating-linear-gradient(
					45deg, #FFFFFF,
					#FFFFFF 5px,
					#b6b6b6 5px,
					#b6b6b6 10px
			);
		}
	</style>

</head>

<body>
<div class="gantt_control">
	<input type="button" value="Show Slack" onclick="toggleSlack(this)">
	<input type="button" value="Show Critical Path" onclick="updateCriticalPath(this)">
</div>
<div id="gantt_here" style='width:100%; height:calc(100vh - 52px);'></div>
<script>

	gantt.plugins({
		auto_scheduling: true,
		critical_path: true
	});

	function toggleSlack(toggle) {
		toggle.enabled = !toggle.enabled;
		if (toggle.enabled) {
			toggle.innerHTML = "Hide Slack";
			//declaring custom config
			gantt.config.show_slack = true;
		} else {
			toggle.innerHTML = "Show Slack";
			gantt.config.show_slack = false;
		}
		gantt.render();
	}

	function updateCriticalPath(toggle) {
		toggle.enabled = !toggle.enabled;
		if (toggle.enabled) {
			toggle.innerHTML = "Hide Critical Path";
			gantt.config.highlight_critical_path = true;
		} else {
			toggle.innerHTML = "Show Critical Path";
			gantt.config.highlight_critical_path = false;
		}
		gantt.render();
	}

	/* show slack */
	(function () {
		var totalSlackColumn = {
			name: "totalSlack",
			align: "center",
			resize: true,
			width: 70,
			label: "Total slack",
			template: function(task) {
				return gantt.getTotalSlack(task);
			}
		}

		var freeSlackColumn = {
			name: "freeSlack",
			align: "center",
			resize: true,
			width: 70,
			label: "Free slack",
			template: function(task) {
				if (gantt.isSummaryTask(task)) {
					return "";
				}
				return gantt.getFreeSlack(task);
			}
		};

		gantt.config.columns = [
			{ name: "text", tree: true, width: 250, resize: true },
			{ name: "start_date", align: "center", resize: true, width: 90 },
			{ name: "duration", align: "center", resize: true, width: 78 },
			freeSlackColumn,
			totalSlackColumn,
			{ name: "add", width: 44, min_width: 44, max_width: 44 }
		];

		gantt.config.show_slack = false;
		gantt.addTaskLayer(function addSlack(task) {
			if (!gantt.config.show_slack) {
				return null;
			}

			var slack = gantt.getFreeSlack(task);

			if (!slack) {
				return null;
			}

			var state = gantt.getState().drag_mode;

			if (state == 'resize' || state == 'move') {
				return null;
			}

			var slackStart = new Date(task.end_date);
			var slackEnd = gantt.calculateEndDate(slackStart, slack);
			var sizes = gantt.getTaskPosition(task, slackStart, slackEnd);
			var el = document.createElement('div');

			el.className = 'slack';
			el.style.left = sizes.left + 'px';
			el.style.top = sizes.top + 2 + 'px';
			el.style.width = sizes.width + 'px';
			el.style.height = sizes.height + 'px';

			return el;
		});
	})();
	/* end show slack */
	gantt.config.auto_scheduling = true;
	gantt.config.auto_scheduling_strict = false;
	gantt.config.work_time = true;
	gantt.config.details_on_create = false;

	gantt.config.duration_unit = "day";
	gantt.config.row_height = 30;
	gantt.config.min_column_width = 40;

	gantt.templates.timeline_cell_class = function (task, date) {
		if (!gantt.isWorkTime(date))
			return "week_end";
		return "";
	};

	gantt.init("gantt_here");
	gantt.parse({
		data: [
			{id: 1, text: "Office itinerancy", open: true, type: "project"},
			{id: 2, text: "Office facing", start_date: "21-07-2024", duration: "17", parent: "1"},
			{id: 3, text: "Furniture installation", start_date: "21-07-2024", duration: "5", parent: "1"},
			{id: 4, text: "The employee relocation", start_date: "28-07-2024", duration: "15", parent: "1"},
			{id: 5, text: "Interior office", start_date: "28-07-2024", duration: "13", parent: "1"},
			{id: 6, text: "Air conditioners installation", start_date: "18-08-2024", duration: "2", parent: "1"},
			{id: 7, text: "Workplaces preparation", start_date: "23-08-2024", duration: "2", parent: "1"},
			{id: 8, text: "Preparing workplaces for us", start_date: "21-07-2024", duration: "10", parent: "1"},
			{id: 9, text: "Workplaces importation", start_date: "22-08-2024", duration: "1", parent: "1"},
			{id: 10, text: "Analysis", open: true, type: "project"},
			{id: 11, text: "Documentation creation", start_date: "30-08-2024", duration: "14", parent: "10"},
			{id: 12, text: "Software design", start_date: "30-08-2024", duration: "10", parent: "10"},
			{id: 13, text: "Interface setup", start_date: "12-09-2024", duration: "1", parent: "10"},
			{id: 14, text: "Development", open: true, type: "project"},
			{id: 15, text: "Develop System", start_date: "15-09-2024", duration: "5", parent: "14"},
			{id: 16, text: "Integrate System", start_date: "15-09-2024", duration: "15", parent: "14"},
			{id: 17, text: "Test", start_date: "06-10-2024", duration: "1", parent: "14"}


		],
		links: [
			{id: "1", source: "3", target: "4", type: "0"},
			{id: "2", source: "3", target: "5", type: "0"},
			{id: "3", source: "2", target: "6", type: "0"},
			{id: "4", source: "4", target: "6", type: "0"},
			{id: "5", source: "5", target: "6", type: "0"},
			{id: "6", source: "6", target: "7", type: "0"},
			{id: "7", source: "7", target: "9", type: "0"},
			{id: "8", source: "8", target: "9", type: "0"},
			{id: "9", source: "9", target: "10", type: "0"},
			{id: "10", source: "9", target: "11", type: "0"},
			{id: "11", source: "9", target: "12", type: "0"},
			{id: "12", source: "11", target: "13", type: "0"},
			{id: "13", source: "12", target: "13", type: "0"},
			{id: "14", source: "13", target: "14", type: "0"},
			{id: "15", source: "13", target: "15", type: "0"},
			{id: "16", source: "15", target: "17", type: "0"},
			{id: "17", source: "16", target: "17", type: "0"}
		]
	});
</script>
</body>