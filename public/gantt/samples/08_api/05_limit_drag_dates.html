<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Denying dragging tasks out of specific dates</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.9"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.9">

	<style>
		html, body {
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}

	</style>
</head>

<body>
<div id="gantt_here" style='width:100%; height:100%;'></div>
<script>
	gantt.attachEvent("onTaskDrag", function (id, mode, task, original) {
		var modes = gantt.config.drag_mode;
		if (mode == modes.move) {
			var diff = task.start_date - original.start_date;
			gantt.eachTask(function (child) {
				if (child.$source.length != 0 || child.$target.length != 0) {
					child.start_date = new Date(+child.start_date + diff);
					child.end_date = new Date(+child.end_date + diff);
					gantt.refreshTask(child.id, true);
				}
			}, id);
		}
		return true;
	});

	//rounds the positions of child items to the scale
	gantt.attachEvent("onAfterTaskDrag", function(id, mode, e){
		var modes = gantt.config.drag_mode;
		if(mode == modes.move ){
			gantt.eachTask(function(child){
				child.start_date = gantt.roundDate(child.start_date);
				child.end_date = gantt.calculateEndDate(child.start_date, child.duration);
				gantt.updateTask(child.id);
			},id );
		}
	});

	gantt.init("gantt_here", new Date(2023, 2, 30), new Date(2023, 3, 15));
	gantt.parse({
		data: [
			{
				id: 1, text: "Project #2", start_date: "01-04-2023", duration: 10,
				progress: 0.4, open: true
			},
			{
				id: 2, text: "Task #1", start_date: "02-04-2023", duration: 4,
				progress: 0.6, parent: 1
			},
			{
				id: 3, text: "Task #2", start_date: "07-04-2023", duration: 4,
				progress: 0.6, parent: 1
			},
			{
				id: 9, text: "Task #6", start_date: "09-04-2023", duration: 4,
				progress: 0.6, parent: 1
			}
		],
		links: [
			{id: 1, source: 1, target: 2, type: "1"},
			{id: 2, source: 2, target: 3, type: "0"}
		]
	});

</script>
</body>