<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>'Broadway' skin</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.9"></script>
	<link rel="stylesheet" href="../../codebase/skins/dhtmlxgantt_broadway.css?v=8.0.9">

	<script src="../common/testdata.js?v=8.0.9"></script>
	<style>
		html, body {
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}


	</style>
</head>

<body>
<div id="gantt_here" style='width:100%; height:100%;'></div>
<script>
	/*gantt.templates.grid_row_class = function(item){
		return item.$level==0?"gantt_project":""
	}
	gantt.templates.task_row_class = function(st,end,item){
		return item.$level==0?"gantt_project":""
	}*/
	gantt.templates.task_class = function (st, end, item) {
		return item.$level == 0 ? "gantt_project" : ""
	};
	gantt.init("gantt_here");
	gantt.parse(demo_tasks);
</script>
</body>