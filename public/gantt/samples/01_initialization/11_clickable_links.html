<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Clickable links</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.9"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.9">

	<script src="../common/testdata.js?v=8.0.9"></script>
	<style>
		html, body {
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}
	</style>
</head>
<body>
<div id="gantt_here" style='width:100%; height:100%;'></div>
<script>
	gantt.init("gantt_here");
	gantt.parse(demo_tasks);

	gantt.attachEvent("onLinkClick", function (id) {
		var link = this.getLink(id),
			src = this.getTask(link.source),
			trg = this.getTask(link.target),
			types = this.config.links;

		var first = "", second = "";
		switch (link.type) {
			case types.finish_to_start:
				first = "finish";
				second = "start";
				break;
			case types.start_to_start:
				first = "start";
				second = "start";
				break;
			case types.finish_to_finish:
				first = "finish";
				second = "finish";
				break;
		}

		gantt.message("Must " + first + " <b>" + src.text + "</b> to " + second + " <b>" + trg.text + "</b>");
	});

</script>
</body>