<!DOCTYPE html>
	<html>
	<head>
		<meta http-equiv="Content-type" content="text/html; charset=utf-8">
		<title>Tasks outside timescale</title>
		<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.9">
		<script src="../../codebase/dhtmlxgantt.js?v=8.0.9"></script>
		<style>
			html, body {
				height: 100%;
				padding: 0px;
				margin: 0px;
			}
		</style>
	</head>
	<body>
		<div id="gantt_here" style="width:100%; height:100%;"></div>

		<script>

			gantt.message({
				text: "Timescale is limited to February 2023. <br> <b>Task #1</b> and <b>Task #3</b> are beyond this time period and are not displayed, but are listed in the grid.",
				expire: -1
			});
			

			
			gantt.templates.date_grid = function(date, task) {
				if (!date) {
					return "";
				}
				return gantt.templates.grid_date_format(date);
			};

			gantt.config.scale_height = 60;
			gantt.config.scale_height = 50;

			gantt.config.scales = [
				{unit: "month", step: 1, format: "%F, %Y"},
				{unit: "day", step: 1, format: "%j, %D"}
			];
			gantt.config.show_tasks_outside_timescale = true;
			gantt.config.start_date = new Date(2023, 1, 1);
			gantt.config.end_date = new Date(2023, 2,1);
			gantt.init("gantt_here");


			gantt.parse({
				data:[
					{id:11, text:"Project #1", start_date:null, duration:0, type:"project", progress: 0.6, open: true},

					{id:12, text:"Task #1", start_date:"03-01-2023", duration:5, parent:11, progress: 1, open: true},
					{id:13, text:"Task #2", start_date:"03-02-2023", type:"project", parent:11, progress: 0.5, open: true},
					{id:14, text:"Task #3", start_date:"02-03-2023", duration:6, parent:11, progress: 0.8, open: true},
					{id:15, text:"Task #4", start_date:null, duration:0, type:"project", parent:11, progress: 0.2, open: true},

					{id:17, text:"Task #2.1", start_date:"03-02-2023", duration:2, parent:13, progress: 1, open: true},
					{id:18, text:"Task #2.2", start_date:"06-02-2023", duration:3, parent:13, progress: 0.8, open: true},
					{id:19, text:"Task #2.3", start_date:"10-02-2023", duration:4, parent:13, progress: 0.2, open: true},
					{id:20, text:"Task #2.4", start_date:"15-02-2023", duration:4, parent:13, progress: 0, open: true},
					{id:21, text:"Task #4.1", start_date:"03-02-2023", duration:4, parent:15, progress: 0.5, open: true},
					{id:22, text:"Task #4.2", start_date:"08-02-2023", duration:4, parent:15, progress: 0.1, open: true}
				],
				links:[
					{id:1, source:17, target:18, type:"0"},
					{id:2, source:18, target:19, type:"0"},
					{id:3, source:19, target:20, type:"0"},
					{id:4, source:21, target:22, type:"0"}
				]
			});
		</script>
	</body>
</html>