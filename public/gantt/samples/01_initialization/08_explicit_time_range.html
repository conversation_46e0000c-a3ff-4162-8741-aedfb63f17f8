<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Define date range</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.9"></script>
	<link rel="stylesheet" href="../../codebase/skins/dhtmlxgantt_meadow.css?v=8.0.9">

	<script src="../common/testdata.js?v=8.0.9"></script>
	<style>
		html, body {
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}
	</style>
</head>
<body>
<div id="gantt_here" style='width:100%; height:100%;'></div>
<script>
	gantt.config.scales = [
		{unit: "month", step: 1, format: "%M"},
		{unit: "year", step: 1, format: "%Y"},
		{unit: "day", format: "%d %M"}
	];
	gantt.config.scale_height = 3 * 28;
	gantt.init("gantt_here", new Date(2023, 02, 10), new Date(2023, 03, 20));
	gantt.parse(demo_tasks);
</script>
</body>