<!DOCTYPE html>
<head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<title>Right to left gantt</title>
	<script src="../../codebase/dhtmlxgantt.js?v=8.0.9"></script>
	<link rel="stylesheet" href="../../codebase/dhtmlxgantt.css?v=8.0.9">

	<script src="../common/testdata.js?v=8.0.9"></script>
	<style>
		html, body {
			height: 100%;
			padding: 0px;
			margin: 0px;
			overflow: hidden;
		}

		.gantt_grid{
			text-align: right;
		}

		.gantt_task_progress {
			text-align: right;
			padding-left: 10px;
			box-sizing: border-box;
			color: white;
			font-weight: bold;
		}
	</style>
</head>

<body>
<div id="gantt_here" style='width:100%; height:100%;'></div>


<script>
	gantt.config.rtl = true;
	gantt.config.scale_height = 50;
	gantt.config.scales = [
		{unit: "month", step: 1, date: "%F, %Y"},
		{unit: "day", step: 1, date: "%j, %D"}
	];

	gantt.templates.progress_text = function (start, end, task) {
		return "<span style='text-align:right;'>" + Math.round(task.progress * 100) + "% </span>";
	};

	gantt.templates.rightside_text = function (start, end, task) {
		if (task.type == gantt.config.types.milestone) {
			return task.text;
		}
		return "";
	};
	gantt.config.lightbox.sections = [
		{name: "description", height: 70, map_to: "text", type: "textarea", focus: true},
		{name: "type", type: "typeselect", map_to: "type"},
		{name: "time", type: "duration", map_to: "auto"}
	];

	gantt.config.layout = {
		css: "gantt_container",
		rows: [
			{
				cols: [
					{view: "scrollbar", id: "scrollVer"},
					{view: "timeline", scrollX: "scrollHor", scrollY: "scrollVer"},
					{resizer: true, width: 1},
					{view: "grid", scrollX: "scrollHor", scrollY: "scrollVer"}
				]
			},
			{view: "scrollbar", id: "scrollHor", height: 20}
		]
	};

	gantt.init("gantt_here");


	gantt.parse(projects_with_milestones);

</script>
</body>